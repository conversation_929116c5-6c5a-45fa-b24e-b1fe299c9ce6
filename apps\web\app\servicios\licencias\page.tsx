import { Metadata } from 'next';
import Link from 'next/link';
import { 
  DocumentCheckIcon, 
  BuildingStorefrontIcon,
  TruckIcon,
  HomeIcon,
  ArrowRightIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

export const metadata: Metadata = {
  title: 'Licencias y Permisos | Portal CHIA',
  description: 'Solicita licencias de construcción, funcionamiento, urbanismo y otros permisos municipales en Chía.',
  keywords: 'licencias, permisos, construcción, funcionamiento, urbanismo, Chía',
};

const licenses = [
  {
    id: 'construccion',
    name: 'Licencia de Construcción',
    description: 'Permiso para construcción, ampliación o modificación de edificaciones',
    icon: HomeIcon,
    price: 'Variable según área',
    time: '15-30 días hábiles',
    requirements: [
      'Planos arquitectónicos y estructurales',
      'Estudio de suelos',
      'Certificado de libertad y tradición',
      'Paz y salvo de impuesto predial'
    ],
    popular: true
  },
  {
    id: 'funcionamiento',
    name: 'Licencia de Funcionamiento',
    description: 'Autorización para el funcionamiento de establecimientos comerciales',
    icon: BuildingStorefrontIcon,
    price: '$150,000 - $500,000',
    time: '10-15 días hábiles',
    requirements: [
      'Registro mercantil vigente',
      'Certificado de uso del suelo',
      'Concepto sanitario (si aplica)',
      'Certificado de bomberos'
    ],
    popular: true
  },
  {
    id: 'urbanismo',
    name: 'Licencia de Urbanismo',
    description: 'Permiso para desarrollo de proyectos urbanísticos',
    icon: TruckIcon,
    price: 'Variable según proyecto',
    time: '30-45 días hábiles',
    requirements: [
      'Planos urbanísticos',
      'Estudio de impacto ambiental',
      'Estudio de tráfico',
      'Concepto de servicios públicos'
    ],
    popular: false
  },
  {
    id: 'publicidad',
    name: 'Permiso de Publicidad Exterior',
    description: 'Autorización para instalación de publicidad exterior',
    icon: DocumentCheckIcon,
    price: '$80,000 - $200,000',
    time: '5-10 días hábiles',
    requirements: [
      'Diseño y ubicación de la publicidad',
      'Autorización del propietario del inmueble',
      'Certificado de uso del suelo',
      'Póliza de responsabilidad civil'
    ],
    popular: false
  }
];

const processSteps = [
  {
    step: 1,
    title: 'Consulta Previa',
    description: 'Verifica los requisitos específicos para tu tipo de licencia'
  },
  {
    step: 2,
    title: 'Documentación',
    description: 'Prepara y presenta todos los documentos requeridos'
  },
  {
    step: 3,
    title: 'Radicación',
    description: 'Presenta la solicitud y realiza el pago correspondiente'
  },
  {
    step: 4,
    title: 'Revisión Técnica',
    description: 'Nuestro equipo técnico evalúa tu solicitud'
  },
  {
    step: 5,
    title: 'Aprobación',
    description: 'Recibe tu licencia una vez aprobada la solicitud'
  }
];

export default function LicenciasPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-4">
              <li>
                <Link href="/" className="text-gray-400 hover:text-gray-500">
                  Inicio
                </Link>
              </li>
              <li>
                <ArrowRightIcon className="h-4 w-4 text-gray-400" />
              </li>
              <li>
                <Link href="/servicios" className="text-gray-400 hover:text-gray-500">
                  Servicios
                </Link>
              </li>
              <li>
                <ArrowRightIcon className="h-4 w-4 text-gray-400" />
              </li>
              <li>
                <span className="text-gray-900 font-medium">Licencias y Permisos</span>
              </li>
            </ol>
          </nav>
        </div>
      </div>

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <DocumentCheckIcon className="h-16 w-16 mx-auto mb-4 text-blue-200" />
            <h1 className="text-4xl font-bold mb-4">
              Licencias y Permisos
            </h1>
            <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
              Obtén las licencias y permisos necesarios para tus proyectos de construcción, 
              comercio y desarrollo urbano de manera ágil y transparente.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Licenses Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {licenses.map((license) => (
            <div key={license.id} className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition-shadow">
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-3 bg-blue-100 rounded-lg">
                      <license.icon className="h-6 w-6 text-blue-600" />
                    </div>
                    {license.popular && (
                      <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">
                        Popular
                      </span>
                    )}
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-blue-600">{license.price}</div>
                    <div className="text-sm text-gray-500">Costo</div>
                  </div>
                </div>
                
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {license.name}
                </h3>
                
                <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                  {license.description}
                </p>
                
                <div className="flex items-center gap-4 text-sm text-gray-500 mb-6">
                  <div className="flex items-center gap-1">
                    <ClockIcon className="h-4 w-4" />
                    {license.time}
                  </div>
                </div>
                
                <div className="mb-6">
                  <h4 className="font-medium text-gray-900 mb-3">Requisitos principales:</h4>
                  <ul className="space-y-2">
                    {license.requirements.map((req, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm text-gray-600">
                        <CheckCircleIcon className="h-4 w-4 text-green-500 flex-shrink-0 mt-0.5" />
                        {req}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
              
              <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
                <button className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                  Solicitar Licencia
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Process Steps */}
        <div className="bg-white rounded-xl shadow-md p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            Proceso de Solicitud de Licencias
          </h2>
          
          <div className="relative">
            <div className="absolute top-8 left-0 right-0 h-0.5 bg-blue-200 hidden lg:block"></div>
            
            <div className="grid grid-cols-1 lg:grid-cols-5 gap-8">
              {processSteps.map((step, index) => (
                <div key={index} className="text-center relative">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 relative z-10 border-4 border-white">
                    <span className="text-blue-600 font-bold text-lg">{step.step}</span>
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">{step.title}</h3>
                  <p className="text-gray-600 text-sm">{step.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Important Information */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
          <h3 className="font-semibold text-yellow-900 mb-4">
            Información Importante
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-yellow-800 text-sm">
            <ul className="space-y-2">
              <li className="flex items-start gap-2">
                <CheckCircleIcon className="h-4 w-4 text-yellow-600 flex-shrink-0 mt-0.5" />
                Los tiempos de respuesta pueden variar según la complejidad del proyecto
              </li>
              <li className="flex items-start gap-2">
                <CheckCircleIcon className="h-4 w-4 text-yellow-600 flex-shrink-0 mt-0.5" />
                Es obligatorio contar con la licencia antes de iniciar cualquier obra
              </li>
            </ul>
            <ul className="space-y-2">
              <li className="flex items-start gap-2">
                <CheckCircleIcon className="h-4 w-4 text-yellow-600 flex-shrink-0 mt-0.5" />
                Todas las licencias tienen vigencia limitada según la normatividad
              </li>
              <li className="flex items-start gap-2">
                <CheckCircleIcon className="h-4 w-4 text-yellow-600 flex-shrink-0 mt-0.5" />
                Se requiere supervisión técnica durante la ejecución de obras
              </li>
            </ul>
          </div>
        </div>

        {/* Quick Consultation */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          <div className="bg-white rounded-xl shadow-md p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              Consulta de Estado
            </h3>
            <p className="text-gray-600 mb-4">
              Consulta el estado de tu solicitud de licencia
            </p>
            <div className="space-y-3">
              <input
                type="text"
                placeholder="Número de radicado"
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                Consultar Estado
              </button>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-md p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              Asesoría Técnica
            </h3>
            <p className="text-gray-600 mb-4">
              ¿Necesitas ayuda para determinar qué licencia requieres?
            </p>
            <div className="space-y-3">
              <Link
                href="/chat"
                className="block w-full text-center bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                Consultar con IA
              </Link>
              <Link
                href="/contacto"
                className="block w-full text-center bg-white hover:bg-gray-50 text-blue-600 border border-blue-600 px-4 py-2 rounded-lg font-medium transition-colors"
              >
                Contactar Experto
              </Link>
            </div>
          </div>
        </div>

        {/* Help Section */}
        <div className="bg-white rounded-xl shadow-md p-8 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            ¿Necesitas más información?
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Nuestro equipo técnico está disponible para orientarte en el proceso 
            de solicitud de licencias y permisos municipales.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/chat"
              className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Asistente Virtual
            </Link>
            <Link
              href="/contacto"
              className="inline-flex items-center gap-2 bg-white hover:bg-gray-50 text-blue-600 border border-blue-600 px-6 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Contactar Oficina Técnica
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
