import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabase();
    const { searchParams } = new URL(request.url);
    
    // Get query parameters
    const query = searchParams.get('q') || searchParams.get('query');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');
    const tipo = searchParams.get('tipo'); // 'faq', 'tramite', 'opa'

    if (!query || query.trim().length === 0) {
      return NextResponse.json(
        { error: 'Search query is required' },
        { status: 400 }
      );
    }

    // Use the database function for unified search
    const { data: searchResults, error } = await supabase
      .schema('ingestion')
      .rpc('buscar_contenido', {
        p_query: query.trim(),
        p_limite: limit,
        p_offset: offset
      });

    if (error) {
      console.error('Error in unified search:', error);
      return NextResponse.json(
        { error: 'Search failed', details: error.message },
        { status: 500 }
      );
    }

    // Filter by type if specified
    let filteredResults = searchResults || [];
    if (tipo) {
      filteredResults = filteredResults.filter((result: any) => result.tipo === tipo);
    }

    // Transform results for frontend
    const transformedResults = filteredResults.map((result: any) => ({
      id: result.id,
      tipo: result.tipo,
      titulo: result.titulo,
      contenido: result.contenido,
      dependencia: result.dependencia,
      subdependencia: result.subdependencia,
      relevancia: result.relevancia,
      // Add type-specific URLs or actions
      url: getResultUrl(result.tipo, result.id),
      categoria: getResultCategory(result.tipo)
    }));

    // Group results by type for better presentation
    const groupedResults = {
      faqs: transformedResults.filter((r: any) => r.tipo === 'faq'),
      tramites: transformedResults.filter((r: any) => r.tipo === 'tramite'),
      opas: transformedResults.filter((r: any) => r.tipo === 'opa')
    };

    return NextResponse.json({
      success: true,
      query: query,
      data: transformedResults,
      grouped: groupedResults,
      pagination: {
        limit,
        offset,
        total: transformedResults.length
      },
      stats: {
        totalResults: transformedResults.length,
        faqCount: groupedResults.faqs.length,
        tramiteCount: groupedResults.tramites.length,
        opaCount: groupedResults.opas.length
      }
    });

  } catch (error) {
    console.error('Unexpected error in search API:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

// Helper function to generate URLs for different result types
function getResultUrl(tipo: string, id: string): string {
  switch (tipo) {
    case 'faq':
      return `/faqs/${id}`;
    case 'tramite':
      return `/servicios/tramite/${id}`;
    case 'opa':
      return `/procedimientos/${id}`;
    default:
      return '#';
  }
}

// Helper function to get category for result types
function getResultCategory(tipo: string): string {
  switch (tipo) {
    case 'faq':
      return 'Preguntas Frecuentes';
    case 'tramite':
      return 'Trámites y Servicios';
    case 'opa':
      return 'Procedimientos Administrativos';
    default:
      return 'General';
  }
}

// POST endpoint for advanced search with filters
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      query, 
      filters = {}, 
      limit = 20, 
      offset = 0 
    } = body;

    if (!query || query.trim().length === 0) {
      return NextResponse.json(
        { error: 'Search query is required' },
        { status: 400 }
      );
    }

    const supabase = createServerSupabase();

    // Perform search with additional filters
    let searchPromises = [];

    // Search FAQs with filters
    if (!filters.tipo || filters.tipo === 'faq') {
      let faqQuery = supabase
        .schema('ingestion')
        .from('faqs')
        .select(`
          id,
          tema,
          pregunta,
          respuesta,
          dependencias:dependencia_id (nombre),
          subdependencias:subdependencia_id (nombre)
        `)
        .eq('activo', true)
        .textSearch('vector_busqueda', query, { config: 'spanish' });

      if (filters.dependencia) {
        faqQuery = faqQuery.eq('dependencia_id', filters.dependencia);
      }

      searchPromises.push(
        faqQuery.then(({ data, error }) => ({
          type: 'faq',
          data: data?.map(item => ({
            ...item,
            tipo: 'faq',
            titulo: item.pregunta,
            contenido: item.respuesta
          })) || [],
          error
        }))
      );
    }

    // Search Tramites with filters
    if (!filters.tipo || filters.tipo === 'tramite') {
      let tramiteQuery = supabase
        .schema('ingestion')
        .from('tramites')
        .select(`
          id,
          nombre,
          descripcion,
          categoria,
          dependencias:dependencia_id (nombre),
          subdependencias:subdependencia_id (nombre)
        `)
        .eq('activo', true)
        .textSearch('vector_busqueda', query, { config: 'spanish' });

      if (filters.dependencia) {
        tramiteQuery = tramiteQuery.eq('dependencia_id', filters.dependencia);
      }
      if (filters.categoria) {
        tramiteQuery = tramiteQuery.eq('categoria', filters.categoria);
      }

      searchPromises.push(
        tramiteQuery.then(({ data, error }) => ({
          type: 'tramite',
          data: data?.map(item => ({
            ...item,
            tipo: 'tramite',
            titulo: item.nombre,
            contenido: item.descripcion
          })) || [],
          error
        }))
      );
    }

    const results = await Promise.all(searchPromises);
    
    // Check for errors
    const errors = results.filter(r => r.error);
    if (errors.length > 0) {
      console.error('Search errors:', errors);
      return NextResponse.json(
        { error: 'Search failed', details: errors.map(e => e.error?.message) },
        { status: 500 }
      );
    }

    // Combine and sort results
    const allResults = results.flatMap((r: any) => r.data || []);
    const sortedResults = allResults.slice(offset, offset + limit);

    return NextResponse.json({
      success: true,
      query,
      filters,
      data: sortedResults,
      pagination: {
        limit,
        offset,
        total: allResults.length
      }
    });

  } catch (error) {
    console.error('Error in advanced search:', error);
    return NextResponse.json(
      { error: 'Search failed' },
      { status: 500 }
    );
  }
}
