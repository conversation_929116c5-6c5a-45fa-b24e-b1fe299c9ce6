"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_next_dist_api_headers_js"],{

/***/ "(app-pages-browser)/../../node_modules/next/dist/api/headers.js":
/*!***************************************************!*\
  !*** ../../node_modules/next/dist/api/headers.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __esModule: () => (/* reexport safe */ _server_request_cookies__WEBPACK_IMPORTED_MODULE_0__.__esModule),\n/* harmony export */   cookies: () => (/* reexport safe */ _server_request_cookies__WEBPACK_IMPORTED_MODULE_0__.cookies),\n/* harmony export */   draftMode: () => (/* reexport safe */ _server_request_draft_mode__WEBPACK_IMPORTED_MODULE_2__.draftMode),\n/* harmony export */   headers: () => (/* reexport safe */ _server_request_headers__WEBPACK_IMPORTED_MODULE_1__.headers)\n/* harmony export */ });\n/* harmony import */ var _server_request_cookies__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../server/request/cookies */ \"(app-pages-browser)/../../node_modules/next/dist/server/request/cookies.js\");\n/* harmony import */ var _server_request_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../server/request/headers */ \"(app-pages-browser)/../../node_modules/next/dist/server/request/headers.js\");\n/* harmony import */ var _server_request_draft_mode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../server/request/draft-mode */ \"(app-pages-browser)/../../node_modules/next/dist/server/request/draft-mode.js\");\n\n\n\n\n//# sourceMappingURL=headers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2FwaS9oZWFkZXJzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBMEM7QUFDQTtBQUNHOztBQUU3QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxKdWFuIFB1bGdhcmluXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXGNoaWEtbmV4dFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxhcGlcXGhlYWRlcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi4vc2VydmVyL3JlcXVlc3QvY29va2llcyc7XG5leHBvcnQgKiBmcm9tICcuLi9zZXJ2ZXIvcmVxdWVzdC9oZWFkZXJzJztcbmV4cG9ydCAqIGZyb20gJy4uL3NlcnZlci9yZXF1ZXN0L2RyYWZ0LW1vZGUnO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1oZWFkZXJzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/api/headers.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/client/components/hooks-server-context.js":
/*!******************************************************************************!*\
  !*** ../../node_modules/next/dist/client/components/hooks-server-context.js ***!
  \******************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DynamicServerError: function() {\n        return DynamicServerError;\n    },\n    isDynamicServerError: function() {\n        return isDynamicServerError;\n    }\n});\nconst DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE';\nclass DynamicServerError extends Error {\n    constructor(description){\n        super(\"Dynamic server usage: \" + description), this.description = description, this.digest = DYNAMIC_ERROR_CODE;\n    }\n}\nfunction isDynamicServerError(err) {\n    if (typeof err !== 'object' || err === null || !('digest' in err) || typeof err.digest !== 'string') {\n        return false;\n    }\n    return err.digest === DYNAMIC_ERROR_CODE;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hooks-server-context.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2hvb2tzLXNlcnZlci1jb250ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztJQUVhQSxrQkFBa0I7ZUFBbEJBOztJQVFHQyxvQkFBb0I7ZUFBcEJBOzs7QUFWaEIsTUFBTUMscUJBQXFCO0FBRXBCLE1BQU1GLDJCQUEyQkc7SUFHdENDLFlBQTRCQyxXQUFtQixDQUFFO1FBQy9DLEtBQUssQ0FBRSwyQkFBd0JBLGNBQUFBLElBQUFBLENBRExBLFdBQUFBLEdBQUFBLGFBQUFBLElBQUFBLENBRjVCQyxNQUFBQSxHQUFvQ0o7SUFJcEM7QUFDRjtBQUVPLFNBQVNELHFCQUFxQk0sR0FBWTtJQUMvQyxJQUNFLE9BQU9BLFFBQVEsWUFDZkEsUUFBUSxRQUNSLENBQUUsYUFBWUEsR0FBQUEsQ0FBRSxJQUNoQixPQUFPQSxJQUFJRCxNQUFNLEtBQUssVUFDdEI7UUFDQSxPQUFPO0lBQ1Q7SUFFQSxPQUFPQyxJQUFJRCxNQUFNLEtBQUtKO0FBQ3hCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEp1YW4gUHVsZ2FyaW5cXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXGhvb2tzLXNlcnZlci1jb250ZXh0LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IERZTkFNSUNfRVJST1JfQ09ERSA9ICdEWU5BTUlDX1NFUlZFUl9VU0FHRSdcblxuZXhwb3J0IGNsYXNzIER5bmFtaWNTZXJ2ZXJFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgZGlnZXN0OiB0eXBlb2YgRFlOQU1JQ19FUlJPUl9DT0RFID0gRFlOQU1JQ19FUlJPUl9DT0RFXG5cbiAgY29uc3RydWN0b3IocHVibGljIHJlYWRvbmx5IGRlc2NyaXB0aW9uOiBzdHJpbmcpIHtcbiAgICBzdXBlcihgRHluYW1pYyBzZXJ2ZXIgdXNhZ2U6ICR7ZGVzY3JpcHRpb259YClcbiAgfVxufVxuXG5leHBvcnQgZnVuY3Rpb24gaXNEeW5hbWljU2VydmVyRXJyb3IoZXJyOiB1bmtub3duKTogZXJyIGlzIER5bmFtaWNTZXJ2ZXJFcnJvciB7XG4gIGlmIChcbiAgICB0eXBlb2YgZXJyICE9PSAnb2JqZWN0JyB8fFxuICAgIGVyciA9PT0gbnVsbCB8fFxuICAgICEoJ2RpZ2VzdCcgaW4gZXJyKSB8fFxuICAgIHR5cGVvZiBlcnIuZGlnZXN0ICE9PSAnc3RyaW5nJ1xuICApIHtcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxuXG4gIHJldHVybiBlcnIuZGlnZXN0ID09PSBEWU5BTUlDX0VSUk9SX0NPREVcbn1cbiJdLCJuYW1lcyI6WyJEeW5hbWljU2VydmVyRXJyb3IiLCJpc0R5bmFtaWNTZXJ2ZXJFcnJvciIsIkRZTkFNSUNfRVJST1JfQ09ERSIsIkVycm9yIiwiY29uc3RydWN0b3IiLCJkZXNjcmlwdGlvbiIsImRpZ2VzdCIsImVyciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/client/components/hooks-server-context.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/client/components/static-generation-bailout.js":
/*!***********************************************************************************!*\
  !*** ../../node_modules/next/dist/client/components/static-generation-bailout.js ***!
  \***********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    StaticGenBailoutError: function() {\n        return StaticGenBailoutError;\n    },\n    isStaticGenBailoutError: function() {\n        return isStaticGenBailoutError;\n    }\n});\nconst NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT';\nclass StaticGenBailoutError extends Error {\n    constructor(...args){\n        super(...args), this.code = NEXT_STATIC_GEN_BAILOUT;\n    }\n}\nfunction isStaticGenBailoutError(error) {\n    if (typeof error !== 'object' || error === null || !('code' in error)) {\n        return false;\n    }\n    return error.code === NEXT_STATIC_GEN_BAILOUT;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=static-generation-bailout.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3N0YXRpYy1nZW5lcmF0aW9uLWJhaWxvdXQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBRWFBLHFCQUFxQjtlQUFyQkE7O0lBSUdDLHVCQUF1QjtlQUF2QkE7OztBQU5oQixNQUFNQywwQkFBMEI7QUFFekIsTUFBTUYsOEJBQThCRzs7UUFBcEMscUJBQ1dDLElBQUFBLEdBQU9GOztBQUN6QjtBQUVPLFNBQVNELHdCQUNkSSxLQUFjO0lBRWQsSUFBSSxPQUFPQSxVQUFVLFlBQVlBLFVBQVUsUUFBUSxDQUFFLFdBQVVBLEtBQUFBLENBQUksRUFBSTtRQUNyRSxPQUFPO0lBQ1Q7SUFFQSxPQUFPQSxNQUFNRCxJQUFJLEtBQUtGO0FBQ3hCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEp1YW4gUHVsZ2FyaW5cXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXHN0YXRpYy1nZW5lcmF0aW9uLWJhaWxvdXQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgTkVYVF9TVEFUSUNfR0VOX0JBSUxPVVQgPSAnTkVYVF9TVEFUSUNfR0VOX0JBSUxPVVQnXG5cbmV4cG9ydCBjbGFzcyBTdGF0aWNHZW5CYWlsb3V0RXJyb3IgZXh0ZW5kcyBFcnJvciB7XG4gIHB1YmxpYyByZWFkb25seSBjb2RlID0gTkVYVF9TVEFUSUNfR0VOX0JBSUxPVVRcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGlzU3RhdGljR2VuQmFpbG91dEVycm9yKFxuICBlcnJvcjogdW5rbm93blxuKTogZXJyb3IgaXMgU3RhdGljR2VuQmFpbG91dEVycm9yIHtcbiAgaWYgKHR5cGVvZiBlcnJvciAhPT0gJ29iamVjdCcgfHwgZXJyb3IgPT09IG51bGwgfHwgISgnY29kZScgaW4gZXJyb3IpKSB7XG4gICAgcmV0dXJuIGZhbHNlXG4gIH1cblxuICByZXR1cm4gZXJyb3IuY29kZSA9PT0gTkVYVF9TVEFUSUNfR0VOX0JBSUxPVVRcbn1cbiJdLCJuYW1lcyI6WyJTdGF0aWNHZW5CYWlsb3V0RXJyb3IiLCJpc1N0YXRpY0dlbkJhaWxvdXRFcnJvciIsIk5FWFRfU1RBVElDX0dFTl9CQUlMT1VUIiwiRXJyb3IiLCJjb2RlIiwiZXJyb3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/client/components/static-generation-bailout.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/compiled/@edge-runtime/cookies/index.js":
/*!****************************************************************************!*\
  !*** ../../node_modules/next/dist/compiled/@edge-runtime/cookies/index.js ***!
  \****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar src_exports = {};\n__export(src_exports, {\n  RequestCookies: () => RequestCookies,\n  ResponseCookies: () => ResponseCookies,\n  parseCookie: () => parseCookie,\n  parseSetCookie: () => parseSetCookie,\n  stringifyCookie: () => stringifyCookie\n});\nmodule.exports = __toCommonJS(src_exports);\n\n// src/serialize.ts\nfunction stringifyCookie(c) {\n  var _a;\n  const attrs = [\n    \"path\" in c && c.path && `Path=${c.path}`,\n    \"expires\" in c && (c.expires || c.expires === 0) && `Expires=${(typeof c.expires === \"number\" ? new Date(c.expires) : c.expires).toUTCString()}`,\n    \"maxAge\" in c && typeof c.maxAge === \"number\" && `Max-Age=${c.maxAge}`,\n    \"domain\" in c && c.domain && `Domain=${c.domain}`,\n    \"secure\" in c && c.secure && \"Secure\",\n    \"httpOnly\" in c && c.httpOnly && \"HttpOnly\",\n    \"sameSite\" in c && c.sameSite && `SameSite=${c.sameSite}`,\n    \"partitioned\" in c && c.partitioned && \"Partitioned\",\n    \"priority\" in c && c.priority && `Priority=${c.priority}`\n  ].filter(Boolean);\n  const stringified = `${c.name}=${encodeURIComponent((_a = c.value) != null ? _a : \"\")}`;\n  return attrs.length === 0 ? stringified : `${stringified}; ${attrs.join(\"; \")}`;\n}\nfunction parseCookie(cookie) {\n  const map = /* @__PURE__ */ new Map();\n  for (const pair of cookie.split(/; */)) {\n    if (!pair)\n      continue;\n    const splitAt = pair.indexOf(\"=\");\n    if (splitAt === -1) {\n      map.set(pair, \"true\");\n      continue;\n    }\n    const [key, value] = [pair.slice(0, splitAt), pair.slice(splitAt + 1)];\n    try {\n      map.set(key, decodeURIComponent(value != null ? value : \"true\"));\n    } catch {\n    }\n  }\n  return map;\n}\nfunction parseSetCookie(setCookie) {\n  if (!setCookie) {\n    return void 0;\n  }\n  const [[name, value], ...attributes] = parseCookie(setCookie);\n  const {\n    domain,\n    expires,\n    httponly,\n    maxage,\n    path,\n    samesite,\n    secure,\n    partitioned,\n    priority\n  } = Object.fromEntries(\n    attributes.map(([key, value2]) => [\n      key.toLowerCase().replace(/-/g, \"\"),\n      value2\n    ])\n  );\n  const cookie = {\n    name,\n    value: decodeURIComponent(value),\n    domain,\n    ...expires && { expires: new Date(expires) },\n    ...httponly && { httpOnly: true },\n    ...typeof maxage === \"string\" && { maxAge: Number(maxage) },\n    path,\n    ...samesite && { sameSite: parseSameSite(samesite) },\n    ...secure && { secure: true },\n    ...priority && { priority: parsePriority(priority) },\n    ...partitioned && { partitioned: true }\n  };\n  return compact(cookie);\n}\nfunction compact(t) {\n  const newT = {};\n  for (const key in t) {\n    if (t[key]) {\n      newT[key] = t[key];\n    }\n  }\n  return newT;\n}\nvar SAME_SITE = [\"strict\", \"lax\", \"none\"];\nfunction parseSameSite(string) {\n  string = string.toLowerCase();\n  return SAME_SITE.includes(string) ? string : void 0;\n}\nvar PRIORITY = [\"low\", \"medium\", \"high\"];\nfunction parsePriority(string) {\n  string = string.toLowerCase();\n  return PRIORITY.includes(string) ? string : void 0;\n}\nfunction splitCookiesString(cookiesString) {\n  if (!cookiesString)\n    return [];\n  var cookiesStrings = [];\n  var pos = 0;\n  var start;\n  var ch;\n  var lastComma;\n  var nextStart;\n  var cookiesSeparatorFound;\n  function skipWhitespace() {\n    while (pos < cookiesString.length && /\\s/.test(cookiesString.charAt(pos))) {\n      pos += 1;\n    }\n    return pos < cookiesString.length;\n  }\n  function notSpecialChar() {\n    ch = cookiesString.charAt(pos);\n    return ch !== \"=\" && ch !== \";\" && ch !== \",\";\n  }\n  while (pos < cookiesString.length) {\n    start = pos;\n    cookiesSeparatorFound = false;\n    while (skipWhitespace()) {\n      ch = cookiesString.charAt(pos);\n      if (ch === \",\") {\n        lastComma = pos;\n        pos += 1;\n        skipWhitespace();\n        nextStart = pos;\n        while (pos < cookiesString.length && notSpecialChar()) {\n          pos += 1;\n        }\n        if (pos < cookiesString.length && cookiesString.charAt(pos) === \"=\") {\n          cookiesSeparatorFound = true;\n          pos = nextStart;\n          cookiesStrings.push(cookiesString.substring(start, lastComma));\n          start = pos;\n        } else {\n          pos = lastComma + 1;\n        }\n      } else {\n        pos += 1;\n      }\n    }\n    if (!cookiesSeparatorFound || pos >= cookiesString.length) {\n      cookiesStrings.push(cookiesString.substring(start, cookiesString.length));\n    }\n  }\n  return cookiesStrings;\n}\n\n// src/request-cookies.ts\nvar RequestCookies = class {\n  constructor(requestHeaders) {\n    /** @internal */\n    this._parsed = /* @__PURE__ */ new Map();\n    this._headers = requestHeaders;\n    const header = requestHeaders.get(\"cookie\");\n    if (header) {\n      const parsed = parseCookie(header);\n      for (const [name, value] of parsed) {\n        this._parsed.set(name, { name, value });\n      }\n    }\n  }\n  [Symbol.iterator]() {\n    return this._parsed[Symbol.iterator]();\n  }\n  /**\n   * The amount of cookies received from the client\n   */\n  get size() {\n    return this._parsed.size;\n  }\n  get(...args) {\n    const name = typeof args[0] === \"string\" ? args[0] : args[0].name;\n    return this._parsed.get(name);\n  }\n  getAll(...args) {\n    var _a;\n    const all = Array.from(this._parsed);\n    if (!args.length) {\n      return all.map(([_, value]) => value);\n    }\n    const name = typeof args[0] === \"string\" ? args[0] : (_a = args[0]) == null ? void 0 : _a.name;\n    return all.filter(([n]) => n === name).map(([_, value]) => value);\n  }\n  has(name) {\n    return this._parsed.has(name);\n  }\n  set(...args) {\n    const [name, value] = args.length === 1 ? [args[0].name, args[0].value] : args;\n    const map = this._parsed;\n    map.set(name, { name, value });\n    this._headers.set(\n      \"cookie\",\n      Array.from(map).map(([_, value2]) => stringifyCookie(value2)).join(\"; \")\n    );\n    return this;\n  }\n  /**\n   * Delete the cookies matching the passed name or names in the request.\n   */\n  delete(names) {\n    const map = this._parsed;\n    const result = !Array.isArray(names) ? map.delete(names) : names.map((name) => map.delete(name));\n    this._headers.set(\n      \"cookie\",\n      Array.from(map).map(([_, value]) => stringifyCookie(value)).join(\"; \")\n    );\n    return result;\n  }\n  /**\n   * Delete all the cookies in the cookies in the request.\n   */\n  clear() {\n    this.delete(Array.from(this._parsed.keys()));\n    return this;\n  }\n  /**\n   * Format the cookies in the request as a string for logging\n   */\n  [Symbol.for(\"edge-runtime.inspect.custom\")]() {\n    return `RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`;\n  }\n  toString() {\n    return [...this._parsed.values()].map((v) => `${v.name}=${encodeURIComponent(v.value)}`).join(\"; \");\n  }\n};\n\n// src/response-cookies.ts\nvar ResponseCookies = class {\n  constructor(responseHeaders) {\n    /** @internal */\n    this._parsed = /* @__PURE__ */ new Map();\n    var _a, _b, _c;\n    this._headers = responseHeaders;\n    const setCookie = (_c = (_b = (_a = responseHeaders.getSetCookie) == null ? void 0 : _a.call(responseHeaders)) != null ? _b : responseHeaders.get(\"set-cookie\")) != null ? _c : [];\n    const cookieStrings = Array.isArray(setCookie) ? setCookie : splitCookiesString(setCookie);\n    for (const cookieString of cookieStrings) {\n      const parsed = parseSetCookie(cookieString);\n      if (parsed)\n        this._parsed.set(parsed.name, parsed);\n    }\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-get CookieStore#get} without the Promise.\n   */\n  get(...args) {\n    const key = typeof args[0] === \"string\" ? args[0] : args[0].name;\n    return this._parsed.get(key);\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-getAll CookieStore#getAll} without the Promise.\n   */\n  getAll(...args) {\n    var _a;\n    const all = Array.from(this._parsed.values());\n    if (!args.length) {\n      return all;\n    }\n    const key = typeof args[0] === \"string\" ? args[0] : (_a = args[0]) == null ? void 0 : _a.name;\n    return all.filter((c) => c.name === key);\n  }\n  has(name) {\n    return this._parsed.has(name);\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-set CookieStore#set} without the Promise.\n   */\n  set(...args) {\n    const [name, value, cookie] = args.length === 1 ? [args[0].name, args[0].value, args[0]] : args;\n    const map = this._parsed;\n    map.set(name, normalizeCookie({ name, value, ...cookie }));\n    replace(map, this._headers);\n    return this;\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-delete CookieStore#delete} without the Promise.\n   */\n  delete(...args) {\n    const [name, options] = typeof args[0] === \"string\" ? [args[0]] : [args[0].name, args[0]];\n    return this.set({ ...options, name, value: \"\", expires: /* @__PURE__ */ new Date(0) });\n  }\n  [Symbol.for(\"edge-runtime.inspect.custom\")]() {\n    return `ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`;\n  }\n  toString() {\n    return [...this._parsed.values()].map(stringifyCookie).join(\"; \");\n  }\n};\nfunction replace(bag, headers) {\n  headers.delete(\"set-cookie\");\n  for (const [, value] of bag) {\n    const serialized = stringifyCookie(value);\n    headers.append(\"set-cookie\", serialized);\n  }\n}\nfunction normalizeCookie(cookie = { name: \"\", value: \"\" }) {\n  if (typeof cookie.expires === \"number\") {\n    cookie.expires = new Date(cookie.expires);\n  }\n  if (cookie.maxAge) {\n    cookie.expires = new Date(Date.now() + cookie.maxAge * 1e3);\n  }\n  if (cookie.path === null || cookie.path === void 0) {\n    cookie.path = \"/\";\n  }\n  return cookie;\n}\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/@edge-runtime/cookies/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/lib/metadata/metadata-constants.js":
/*!***********************************************************************!*\
  !*** ../../node_modules/next/dist/lib/metadata/metadata-constants.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    METADATA_BOUNDARY_NAME: function() {\n        return METADATA_BOUNDARY_NAME;\n    },\n    OUTLET_BOUNDARY_NAME: function() {\n        return OUTLET_BOUNDARY_NAME;\n    },\n    VIEWPORT_BOUNDARY_NAME: function() {\n        return VIEWPORT_BOUNDARY_NAME;\n    }\n});\nconst METADATA_BOUNDARY_NAME = '__next_metadata_boundary__';\nconst VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__';\nconst OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__';\n\n//# sourceMappingURL=metadata-constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9tZXRhZGF0YS1jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRixNQUFNLENBSUw7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcSnVhbiBQdWxnYXJpblxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxjaGlhLW5leHRcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcbGliXFxtZXRhZGF0YVxcbWV0YWRhdGEtY29uc3RhbnRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuMCAmJiAobW9kdWxlLmV4cG9ydHMgPSB7XG4gICAgTUVUQURBVEFfQk9VTkRBUllfTkFNRTogbnVsbCxcbiAgICBPVVRMRVRfQk9VTkRBUllfTkFNRTogbnVsbCxcbiAgICBWSUVXUE9SVF9CT1VOREFSWV9OQU1FOiBudWxsXG59KTtcbmZ1bmN0aW9uIF9leHBvcnQodGFyZ2V0LCBhbGwpIHtcbiAgICBmb3IodmFyIG5hbWUgaW4gYWxsKU9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0YXJnZXQsIG5hbWUsIHtcbiAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgZ2V0OiBhbGxbbmFtZV1cbiAgICB9KTtcbn1cbl9leHBvcnQoZXhwb3J0cywge1xuICAgIE1FVEFEQVRBX0JPVU5EQVJZX05BTUU6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gTUVUQURBVEFfQk9VTkRBUllfTkFNRTtcbiAgICB9LFxuICAgIE9VVExFVF9CT1VOREFSWV9OQU1FOiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIE9VVExFVF9CT1VOREFSWV9OQU1FO1xuICAgIH0sXG4gICAgVklFV1BPUlRfQk9VTkRBUllfTkFNRTogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBWSUVXUE9SVF9CT1VOREFSWV9OQU1FO1xuICAgIH1cbn0pO1xuY29uc3QgTUVUQURBVEFfQk9VTkRBUllfTkFNRSA9ICdfX25leHRfbWV0YWRhdGFfYm91bmRhcnlfXyc7XG5jb25zdCBWSUVXUE9SVF9CT1VOREFSWV9OQU1FID0gJ19fbmV4dF92aWV3cG9ydF9ib3VuZGFyeV9fJztcbmNvbnN0IE9VVExFVF9CT1VOREFSWV9OQU1FID0gJ19fbmV4dF9vdXRsZXRfYm91bmRhcnlfXyc7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW1ldGFkYXRhLWNvbnN0YW50cy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/lib/metadata/metadata-constants.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/lib/scheduler.js":
/*!*****************************************************!*\
  !*** ../../node_modules/next/dist/lib/scheduler.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/../../node_modules/next/dist/build/polyfills/process.js\");\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    atLeastOneTask: function() {\n        return atLeastOneTask;\n    },\n    scheduleImmediate: function() {\n        return scheduleImmediate;\n    },\n    scheduleOnNextTick: function() {\n        return scheduleOnNextTick;\n    },\n    waitAtLeastOneReactRenderTask: function() {\n        return waitAtLeastOneReactRenderTask;\n    }\n});\nconst scheduleOnNextTick = (cb)=>{\n    // We use Promise.resolve().then() here so that the operation is scheduled at\n    // the end of the promise job queue, we then add it to the next process tick\n    // to ensure it's evaluated afterwards.\n    //\n    // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n    //\n    Promise.resolve().then(()=>{\n        if (false) {} else {\n            process.nextTick(cb);\n        }\n    });\n};\nconst scheduleImmediate = (cb)=>{\n    if (false) {} else {\n        setImmediate(cb);\n    }\n};\nfunction atLeastOneTask() {\n    return new Promise((resolve)=>scheduleImmediate(resolve));\n}\nfunction waitAtLeastOneReactRenderTask() {\n    if (false) {} else {\n        return new Promise((r)=>setImmediate(r));\n    }\n}\n\n//# sourceMappingURL=scheduler.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/lib/scheduler.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/server/app-render/after-task-async-storage-instance.js":
/*!*******************************************************************************************!*\
  !*** ../../node_modules/next/dist/server/app-render/after-task-async-storage-instance.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"afterTaskAsyncStorageInstance\", ({\n    enumerable: true,\n    get: function() {\n        return afterTaskAsyncStorageInstance;\n    }\n}));\nconst _asynclocalstorage = __webpack_require__(/*! ./async-local-storage */ \"(app-pages-browser)/../../node_modules/next/dist/server/app-render/async-local-storage.js\");\nconst afterTaskAsyncStorageInstance = (0, _asynclocalstorage.createAsyncLocalStorage)();\n\n//# sourceMappingURL=after-task-async-storage-instance.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2FmdGVyLXRhc2stYXN5bmMtc3RvcmFnZS1pbnN0YW5jZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLGlFQUFnRTtBQUNoRTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGLDJCQUEyQixtQkFBTyxDQUFDLHdIQUF1QjtBQUMxRDs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxKdWFuIFB1bGdhcmluXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXGNoaWEtbmV4dFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxzZXJ2ZXJcXGFwcC1yZW5kZXJcXGFmdGVyLXRhc2stYXN5bmMtc3RvcmFnZS1pbnN0YW5jZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcImFmdGVyVGFza0FzeW5jU3RvcmFnZUluc3RhbmNlXCIsIHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBhZnRlclRhc2tBc3luY1N0b3JhZ2VJbnN0YW5jZTtcbiAgICB9XG59KTtcbmNvbnN0IF9hc3luY2xvY2Fsc3RvcmFnZSA9IHJlcXVpcmUoXCIuL2FzeW5jLWxvY2FsLXN0b3JhZ2VcIik7XG5jb25zdCBhZnRlclRhc2tBc3luY1N0b3JhZ2VJbnN0YW5jZSA9ICgwLCBfYXN5bmNsb2NhbHN0b3JhZ2UuY3JlYXRlQXN5bmNMb2NhbFN0b3JhZ2UpKCk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFmdGVyLXRhc2stYXN5bmMtc3RvcmFnZS1pbnN0YW5jZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/server/app-render/after-task-async-storage-instance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/server/app-render/after-task-async-storage.external.js":
/*!*******************************************************************************************!*\
  !*** ../../node_modules/next/dist/server/app-render/after-task-async-storage.external.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"afterTaskAsyncStorage\", ({\n    enumerable: true,\n    get: function() {\n        return _aftertaskasyncstorageinstance.afterTaskAsyncStorageInstance;\n    }\n}));\nconst _aftertaskasyncstorageinstance = __webpack_require__(/*! ./after-task-async-storage-instance */ \"(app-pages-browser)/../../node_modules/next/dist/server/app-render/after-task-async-storage-instance.js\");\n\n//# sourceMappingURL=after-task-async-storage.external.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2FmdGVyLXRhc2stYXN5bmMtc3RvcmFnZS5leHRlcm5hbC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLHlEQUF3RDtBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGLHVDQUF1QyxtQkFBTyxDQUFDLG9KQUFxQzs7QUFFcEYiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcSnVhbiBQdWxnYXJpblxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxjaGlhLW5leHRcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcc2VydmVyXFxhcHAtcmVuZGVyXFxhZnRlci10YXNrLWFzeW5jLXN0b3JhZ2UuZXh0ZXJuYWwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJhZnRlclRhc2tBc3luY1N0b3JhZ2VcIiwge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIF9hZnRlcnRhc2thc3luY3N0b3JhZ2VpbnN0YW5jZS5hZnRlclRhc2tBc3luY1N0b3JhZ2VJbnN0YW5jZTtcbiAgICB9XG59KTtcbmNvbnN0IF9hZnRlcnRhc2thc3luY3N0b3JhZ2VpbnN0YW5jZSA9IHJlcXVpcmUoXCIuL2FmdGVyLXRhc2stYXN5bmMtc3RvcmFnZS1pbnN0YW5jZVwiKTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YWZ0ZXItdGFzay1hc3luYy1zdG9yYWdlLmV4dGVybmFsLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/server/app-render/after-task-async-storage.external.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/server/app-render/async-local-storage.js":
/*!*****************************************************************************!*\
  !*** ../../node_modules/next/dist/server/app-render/async-local-storage.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    bindSnapshot: function() {\n        return bindSnapshot;\n    },\n    createAsyncLocalStorage: function() {\n        return createAsyncLocalStorage;\n    },\n    createSnapshot: function() {\n        return createSnapshot;\n    }\n});\nconst sharedAsyncLocalStorageNotAvailableError = Object.defineProperty(new Error('Invariant: AsyncLocalStorage accessed in runtime where it is not available'), \"__NEXT_ERROR_CODE\", {\n    value: \"E504\",\n    enumerable: false,\n    configurable: true\n});\nclass FakeAsyncLocalStorage {\n    disable() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    getStore() {\n        // This fake implementation of AsyncLocalStorage always returns `undefined`.\n        return undefined;\n    }\n    run() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    exit() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    enterWith() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    static bind(fn) {\n        return fn;\n    }\n}\nconst maybeGlobalAsyncLocalStorage = typeof globalThis !== 'undefined' && globalThis.AsyncLocalStorage;\nfunction createAsyncLocalStorage() {\n    if (maybeGlobalAsyncLocalStorage) {\n        return new maybeGlobalAsyncLocalStorage();\n    }\n    return new FakeAsyncLocalStorage();\n}\nfunction bindSnapshot(fn) {\n    if (maybeGlobalAsyncLocalStorage) {\n        return maybeGlobalAsyncLocalStorage.bind(fn);\n    }\n    return FakeAsyncLocalStorage.bind(fn);\n}\nfunction createSnapshot() {\n    if (maybeGlobalAsyncLocalStorage) {\n        return maybeGlobalAsyncLocalStorage.snapshot();\n    }\n    return function(fn, ...args) {\n        return fn(...args);\n    };\n}\n\n//# sourceMappingURL=async-local-storage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/server/app-render/async-local-storage.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/server/app-render/dynamic-rendering.js":
/*!***************************************************************************!*\
  !*** ../../node_modules/next/dist/server/app-render/dynamic-rendering.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Postpone: function() {\n        return Postpone;\n    },\n    abortAndThrowOnSynchronousRequestDataAccess: function() {\n        return abortAndThrowOnSynchronousRequestDataAccess;\n    },\n    abortOnSynchronousPlatformIOAccess: function() {\n        return abortOnSynchronousPlatformIOAccess;\n    },\n    accessedDynamicData: function() {\n        return accessedDynamicData;\n    },\n    annotateDynamicAccess: function() {\n        return annotateDynamicAccess;\n    },\n    consumeDynamicAccess: function() {\n        return consumeDynamicAccess;\n    },\n    createDynamicTrackingState: function() {\n        return createDynamicTrackingState;\n    },\n    createDynamicValidationState: function() {\n        return createDynamicValidationState;\n    },\n    createHangingInputAbortSignal: function() {\n        return createHangingInputAbortSignal;\n    },\n    createPostponedAbortSignal: function() {\n        return createPostponedAbortSignal;\n    },\n    formatDynamicAPIAccesses: function() {\n        return formatDynamicAPIAccesses;\n    },\n    getFirstDynamicReason: function() {\n        return getFirstDynamicReason;\n    },\n    isDynamicPostpone: function() {\n        return isDynamicPostpone;\n    },\n    isPrerenderInterruptedError: function() {\n        return isPrerenderInterruptedError;\n    },\n    markCurrentScopeAsDynamic: function() {\n        return markCurrentScopeAsDynamic;\n    },\n    postponeWithTracking: function() {\n        return postponeWithTracking;\n    },\n    throwIfDisallowedDynamic: function() {\n        return throwIfDisallowedDynamic;\n    },\n    throwToInterruptStaticGeneration: function() {\n        return throwToInterruptStaticGeneration;\n    },\n    trackAllowedDynamicAccess: function() {\n        return trackAllowedDynamicAccess;\n    },\n    trackDynamicDataInDynamicRender: function() {\n        return trackDynamicDataInDynamicRender;\n    },\n    trackFallbackParamAccessed: function() {\n        return trackFallbackParamAccessed;\n    },\n    trackSynchronousPlatformIOAccessInDev: function() {\n        return trackSynchronousPlatformIOAccessInDev;\n    },\n    trackSynchronousRequestDataAccessInDev: function() {\n        return trackSynchronousRequestDataAccessInDev;\n    },\n    useDynamicRouteParams: function() {\n        return useDynamicRouteParams;\n    }\n});\nconst _react = /*#__PURE__*/ _interop_require_default(__webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\"));\nconst _hooksservercontext = __webpack_require__(/*! ../../client/components/hooks-server-context */ \"(app-pages-browser)/../../node_modules/next/dist/client/components/hooks-server-context.js\");\nconst _staticgenerationbailout = __webpack_require__(/*! ../../client/components/static-generation-bailout */ \"(app-pages-browser)/../../node_modules/next/dist/client/components/static-generation-bailout.js\");\nconst _workunitasyncstorageexternal = __webpack_require__(/*! ./work-unit-async-storage.external */ \"(shared)/../../node_modules/next/dist/server/app-render/work-unit-async-storage.external.js\");\nconst _workasyncstorageexternal = __webpack_require__(/*! ../app-render/work-async-storage.external */ \"(shared)/../../node_modules/next/dist/server/app-render/work-async-storage.external.js\");\nconst _dynamicrenderingutils = __webpack_require__(/*! ../dynamic-rendering-utils */ \"(app-pages-browser)/../../node_modules/next/dist/server/dynamic-rendering-utils.js\");\nconst _metadataconstants = __webpack_require__(/*! ../../lib/metadata/metadata-constants */ \"(app-pages-browser)/../../node_modules/next/dist/lib/metadata/metadata-constants.js\");\nconst _scheduler = __webpack_require__(/*! ../../lib/scheduler */ \"(app-pages-browser)/../../node_modules/next/dist/lib/scheduler.js\");\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nconst hasPostpone = typeof _react.default.unstable_postpone === 'function';\nfunction createDynamicTrackingState(isDebugDynamicAccesses) {\n    return {\n        isDebugDynamicAccesses,\n        dynamicAccesses: [],\n        syncDynamicExpression: undefined,\n        syncDynamicErrorWithStack: null\n    };\n}\nfunction createDynamicValidationState() {\n    return {\n        hasSuspendedDynamic: false,\n        hasDynamicMetadata: false,\n        hasDynamicViewport: false,\n        hasSyncDynamicErrors: false,\n        dynamicErrors: []\n    };\n}\nfunction getFirstDynamicReason(trackingState) {\n    var _trackingState_dynamicAccesses_;\n    return (_trackingState_dynamicAccesses_ = trackingState.dynamicAccesses[0]) == null ? void 0 : _trackingState_dynamicAccesses_.expression;\n}\nfunction markCurrentScopeAsDynamic(store, workUnitStore, expression) {\n    if (workUnitStore) {\n        if (workUnitStore.type === 'cache' || workUnitStore.type === 'unstable-cache') {\n            // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n            // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n            // forbidden inside a cache scope.\n            return;\n        }\n    }\n    // If we're forcing dynamic rendering or we're forcing static rendering, we\n    // don't need to do anything here because the entire page is already dynamic\n    // or it's static and it should not throw or postpone here.\n    if (store.forceDynamic || store.forceStatic) return;\n    if (store.dynamicShouldError) {\n        throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n            value: \"E553\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    if (workUnitStore) {\n        if (workUnitStore.type === 'prerender-ppr') {\n            postponeWithTracking(store.route, expression, workUnitStore.dynamicTracking);\n        } else if (workUnitStore.type === 'prerender-legacy') {\n            workUnitStore.revalidate = 0;\n            // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n            const err = Object.defineProperty(new _hooksservercontext.DynamicServerError(`Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`), \"__NEXT_ERROR_CODE\", {\n                value: \"E550\",\n                enumerable: false,\n                configurable: true\n            });\n            store.dynamicUsageDescription = expression;\n            store.dynamicUsageStack = err.stack;\n            throw err;\n        } else if ( true && workUnitStore && workUnitStore.type === 'request') {\n            workUnitStore.usedDynamic = true;\n        }\n    }\n}\nfunction trackFallbackParamAccessed(store, expression) {\n    const prerenderStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (!prerenderStore || prerenderStore.type !== 'prerender-ppr') return;\n    postponeWithTracking(store.route, expression, prerenderStore.dynamicTracking);\n}\nfunction throwToInterruptStaticGeneration(expression, store, prerenderStore) {\n    // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n    const err = Object.defineProperty(new _hooksservercontext.DynamicServerError(`Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`), \"__NEXT_ERROR_CODE\", {\n        value: \"E558\",\n        enumerable: false,\n        configurable: true\n    });\n    prerenderStore.revalidate = 0;\n    store.dynamicUsageDescription = expression;\n    store.dynamicUsageStack = err.stack;\n    throw err;\n}\nfunction trackDynamicDataInDynamicRender(_store, workUnitStore) {\n    if (workUnitStore) {\n        if (workUnitStore.type === 'cache' || workUnitStore.type === 'unstable-cache') {\n            // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n            // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n            // forbidden inside a cache scope.\n            return;\n        }\n        if (workUnitStore.type === 'prerender' || workUnitStore.type === 'prerender-legacy') {\n            workUnitStore.revalidate = 0;\n        }\n        if ( true && workUnitStore.type === 'request') {\n            workUnitStore.usedDynamic = true;\n        }\n    }\n}\n// Despite it's name we don't actually abort unless we have a controller to call abort on\n// There are times when we let a prerender run long to discover caches where we want the semantics\n// of tracking dynamic access without terminating the prerender early\nfunction abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore) {\n    const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`;\n    const error = createPrerenderInterruptedError(reason);\n    prerenderStore.controller.abort(error);\n    const dynamicTracking = prerenderStore.dynamicTracking;\n    if (dynamicTracking) {\n        dynamicTracking.dynamicAccesses.push({\n            // When we aren't debugging, we don't need to create another error for the\n            // stack trace.\n            stack: dynamicTracking.isDebugDynamicAccesses ? new Error().stack : undefined,\n            expression\n        });\n    }\n}\nfunction abortOnSynchronousPlatformIOAccess(route, expression, errorWithStack, prerenderStore) {\n    const dynamicTracking = prerenderStore.dynamicTracking;\n    if (dynamicTracking) {\n        if (dynamicTracking.syncDynamicErrorWithStack === null) {\n            dynamicTracking.syncDynamicExpression = expression;\n            dynamicTracking.syncDynamicErrorWithStack = errorWithStack;\n        }\n    }\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore);\n}\nfunction trackSynchronousPlatformIOAccessInDev(requestStore) {\n    // We don't actually have a controller to abort but we do the semantic equivalent by\n    // advancing the request store out of prerender mode\n    requestStore.prerenderPhase = false;\n}\nfunction abortAndThrowOnSynchronousRequestDataAccess(route, expression, errorWithStack, prerenderStore) {\n    const prerenderSignal = prerenderStore.controller.signal;\n    if (prerenderSignal.aborted === false) {\n        // TODO it would be better to move this aborted check into the callsite so we can avoid making\n        // the error object when it isn't relevant to the aborting of the prerender however\n        // since we need the throw semantics regardless of whether we abort it is easier to land\n        // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n        // to ideal implementation\n        const dynamicTracking = prerenderStore.dynamicTracking;\n        if (dynamicTracking) {\n            if (dynamicTracking.syncDynamicErrorWithStack === null) {\n                dynamicTracking.syncDynamicExpression = expression;\n                dynamicTracking.syncDynamicErrorWithStack = errorWithStack;\n                if (prerenderStore.validating === true) {\n                    // We always log Request Access in dev at the point of calling the function\n                    // So we mark the dynamic validation as not requiring it to be printed\n                    dynamicTracking.syncDynamicLogged = true;\n                }\n            }\n        }\n        abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore);\n    }\n    throw createPrerenderInterruptedError(`Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`);\n}\nconst trackSynchronousRequestDataAccessInDev = trackSynchronousPlatformIOAccessInDev;\nfunction Postpone({ reason, route }) {\n    const prerenderStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    const dynamicTracking = prerenderStore && prerenderStore.type === 'prerender-ppr' ? prerenderStore.dynamicTracking : null;\n    postponeWithTracking(route, reason, dynamicTracking);\n}\nfunction postponeWithTracking(route, expression, dynamicTracking) {\n    assertPostpone();\n    if (dynamicTracking) {\n        dynamicTracking.dynamicAccesses.push({\n            // When we aren't debugging, we don't need to create another error for the\n            // stack trace.\n            stack: dynamicTracking.isDebugDynamicAccesses ? new Error().stack : undefined,\n            expression\n        });\n    }\n    _react.default.unstable_postpone(createPostponeReason(route, expression));\n}\nfunction createPostponeReason(route, expression) {\n    return `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` + `React throws this special object to indicate where. It should not be caught by ` + `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`;\n}\nfunction isDynamicPostpone(err) {\n    if (typeof err === 'object' && err !== null && typeof err.message === 'string') {\n        return isDynamicPostponeReason(err.message);\n    }\n    return false;\n}\nfunction isDynamicPostponeReason(reason) {\n    return reason.includes('needs to bail out of prerendering at this point because it used') && reason.includes('Learn more: https://nextjs.org/docs/messages/ppr-caught-error');\n}\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n    throw Object.defineProperty(new Error('Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'), \"__NEXT_ERROR_CODE\", {\n        value: \"E296\",\n        enumerable: false,\n        configurable: true\n    });\n}\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED';\nfunction createPrerenderInterruptedError(message) {\n    const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    error.digest = NEXT_PRERENDER_INTERRUPTED;\n    return error;\n}\nfunction isPrerenderInterruptedError(error) {\n    return typeof error === 'object' && error !== null && error.digest === NEXT_PRERENDER_INTERRUPTED && 'name' in error && 'message' in error && error instanceof Error;\n}\nfunction accessedDynamicData(dynamicAccesses) {\n    return dynamicAccesses.length > 0;\n}\nfunction consumeDynamicAccess(serverDynamic, clientDynamic) {\n    // We mutate because we only call this once we are no longer writing\n    // to the dynamicTrackingState and it's more efficient than creating a new\n    // array.\n    serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses);\n    return serverDynamic.dynamicAccesses;\n}\nfunction formatDynamicAPIAccesses(dynamicAccesses) {\n    return dynamicAccesses.filter((access)=>typeof access.stack === 'string' && access.stack.length > 0).map(({ expression, stack })=>{\n        stack = stack.split('\\n')// Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4).filter((line)=>{\n            // Exclude Next.js internals from the stack trace.\n            if (line.includes('node_modules/next/')) {\n                return false;\n            }\n            // Exclude anonymous functions from the stack trace.\n            if (line.includes(' (<anonymous>)')) {\n                return false;\n            }\n            // Exclude Node.js internals from the stack trace.\n            if (line.includes(' (node:')) {\n                return false;\n            }\n            return true;\n        }).join('\\n');\n        return `Dynamic API Usage Debug - ${expression}:\\n${stack}`;\n    });\n}\nfunction assertPostpone() {\n    if (!hasPostpone) {\n        throw Object.defineProperty(new Error(`Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`), \"__NEXT_ERROR_CODE\", {\n            value: \"E224\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n}\nfunction createPostponedAbortSignal(reason) {\n    assertPostpone();\n    const controller = new AbortController();\n    // We get our hands on a postpone instance by calling postpone and catching the throw\n    try {\n        _react.default.unstable_postpone(reason);\n    } catch (x) {\n        controller.abort(x);\n    }\n    return controller.signal;\n}\nfunction createHangingInputAbortSignal(workUnitStore) {\n    const controller = new AbortController();\n    if (workUnitStore.cacheSignal) {\n        // If we have a cacheSignal it means we're in a prospective render. If the input\n        // we're waiting on is coming from another cache, we do want to wait for it so that\n        // we can resolve this cache entry too.\n        workUnitStore.cacheSignal.inputReady().then(()=>{\n            controller.abort();\n        });\n    } else {\n        // Otherwise we're in the final render and we should already have all our caches\n        // filled. We might still be waiting on some microtasks so we wait one tick before\n        // giving up. When we give up, we still want to render the content of this cache\n        // as deeply as we can so that we can suspend as deeply as possible in the tree\n        // or not at all if we don't end up waiting for the input.\n        (0, _scheduler.scheduleOnNextTick)(()=>controller.abort());\n    }\n    return controller.signal;\n}\nfunction annotateDynamicAccess(expression, prerenderStore) {\n    const dynamicTracking = prerenderStore.dynamicTracking;\n    if (dynamicTracking) {\n        dynamicTracking.dynamicAccesses.push({\n            stack: dynamicTracking.isDebugDynamicAccesses ? new Error().stack : undefined,\n            expression\n        });\n    }\n}\nfunction useDynamicRouteParams(expression) {\n    const workStore = _workasyncstorageexternal.workAsyncStorage.getStore();\n    if (workStore && workStore.isStaticGeneration && workStore.fallbackRouteParams && workStore.fallbackRouteParams.size > 0) {\n        // There are fallback route params, we should track these as dynamic\n        // accesses.\n        const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n        if (workUnitStore) {\n            // We're prerendering with dynamicIO or PPR or both\n            if (workUnitStore.type === 'prerender') {\n                // We are in a prerender with dynamicIO semantics\n                // We are going to hang here and never resolve. This will cause the currently\n                // rendering component to effectively be a dynamic hole\n                _react.default.use((0, _dynamicrenderingutils.makeHangingPromise)(workUnitStore.renderSignal, expression));\n            } else if (workUnitStore.type === 'prerender-ppr') {\n                // We're prerendering with PPR\n                postponeWithTracking(workStore.route, expression, workUnitStore.dynamicTracking);\n            } else if (workUnitStore.type === 'prerender-legacy') {\n                throwToInterruptStaticGeneration(expression, workStore, workUnitStore);\n            }\n        }\n    }\n}\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/;\nconst hasMetadataRegex = new RegExp(`\\\\n\\\\s+at ${_metadataconstants.METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`);\nconst hasViewportRegex = new RegExp(`\\\\n\\\\s+at ${_metadataconstants.VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`);\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${_metadataconstants.OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`);\nfunction trackAllowedDynamicAccess(route, componentStack, dynamicValidation, serverDynamic, clientDynamic) {\n    if (hasOutletRegex.test(componentStack)) {\n        // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n        return;\n    } else if (hasMetadataRegex.test(componentStack)) {\n        dynamicValidation.hasDynamicMetadata = true;\n        return;\n    } else if (hasViewportRegex.test(componentStack)) {\n        dynamicValidation.hasDynamicViewport = true;\n        return;\n    } else if (hasSuspenseRegex.test(componentStack)) {\n        dynamicValidation.hasSuspendedDynamic = true;\n        return;\n    } else if (serverDynamic.syncDynamicErrorWithStack || clientDynamic.syncDynamicErrorWithStack) {\n        dynamicValidation.hasSyncDynamicErrors = true;\n        return;\n    } else {\n        const message = `Route \"${route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`;\n        const error = createErrorWithComponentStack(message, componentStack);\n        dynamicValidation.dynamicErrors.push(error);\n        return;\n    }\n}\nfunction createErrorWithComponentStack(message, componentStack) {\n    const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    error.stack = 'Error: ' + message + componentStack;\n    return error;\n}\nfunction throwIfDisallowedDynamic(route, dynamicValidation, serverDynamic, clientDynamic) {\n    let syncError;\n    let syncExpression;\n    let syncLogged;\n    if (serverDynamic.syncDynamicErrorWithStack) {\n        syncError = serverDynamic.syncDynamicErrorWithStack;\n        syncExpression = serverDynamic.syncDynamicExpression;\n        syncLogged = serverDynamic.syncDynamicLogged === true;\n    } else if (clientDynamic.syncDynamicErrorWithStack) {\n        syncError = clientDynamic.syncDynamicErrorWithStack;\n        syncExpression = clientDynamic.syncDynamicExpression;\n        syncLogged = clientDynamic.syncDynamicLogged === true;\n    } else {\n        syncError = null;\n        syncExpression = undefined;\n        syncLogged = false;\n    }\n    if (dynamicValidation.hasSyncDynamicErrors && syncError) {\n        if (!syncLogged) {\n            // In dev we already log errors about sync dynamic access. But during builds we need to ensure\n            // the offending sync error is logged before we exit the build\n            console.error(syncError);\n        }\n        // The actual error should have been logged when the sync access ocurred\n        throw new _staticgenerationbailout.StaticGenBailoutError();\n    }\n    const dynamicErrors = dynamicValidation.dynamicErrors;\n    if (dynamicErrors.length) {\n        for(let i = 0; i < dynamicErrors.length; i++){\n            console.error(dynamicErrors[i]);\n        }\n        throw new _staticgenerationbailout.StaticGenBailoutError();\n    }\n    if (!dynamicValidation.hasSuspendedDynamic) {\n        if (dynamicValidation.hasDynamicMetadata) {\n            if (syncError) {\n                console.error(syncError);\n                throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route \"${route}\" has a \\`generateMetadata\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E608\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route \"${route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateMetadata\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`), \"__NEXT_ERROR_CODE\", {\n                value: \"E534\",\n                enumerable: false,\n                configurable: true\n            });\n        } else if (dynamicValidation.hasDynamicViewport) {\n            if (syncError) {\n                console.error(syncError);\n                throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route \"${route}\" has a \\`generateViewport\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E573\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route \"${route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateViewport\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`), \"__NEXT_ERROR_CODE\", {\n                value: \"E590\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n    }\n}\n\n//# sourceMappingURL=dynamic-rendering.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/server/app-render/dynamic-rendering.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js":
/*!*********************************************************************************************!*\
  !*** ../../node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createDedupedByCallsiteServerErrorLoggerDev\", ({\n    enumerable: true,\n    get: function() {\n        return createDedupedByCallsiteServerErrorLoggerDev;\n    }\n}));\nconst _react = /*#__PURE__*/ _interop_require_wildcard(__webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\"));\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {\n        __proto__: null\n    };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nconst errorRef = {\n    current: null\n};\n// React.cache is currently only available in canary/experimental React channels.\nconst cache = typeof _react.cache === 'function' ? _react.cache : (fn)=>fn;\n// When Dynamic IO is enabled, we record these as errors so that they\n// are captured by the dev overlay as it's more critical to fix these\n// when enabled.\nconst logErrorOrWarn =  false ? 0 : console.warn;\n// We don't want to dedupe across requests.\n// The developer might've just attempted to fix the warning so we should warn again if it still happens.\nconst flushCurrentErrorIfNew = cache(// eslint-disable-next-line @typescript-eslint/no-unused-vars -- cache key\n(key)=>{\n    try {\n        logErrorOrWarn(errorRef.current);\n    } finally{\n        errorRef.current = null;\n    }\n});\nfunction createDedupedByCallsiteServerErrorLoggerDev(getMessage) {\n    return function logDedupedError(...args) {\n        const message = getMessage(...args);\n        if (true) {\n            var _stack;\n            const callStackFrames = (_stack = new Error().stack) == null ? void 0 : _stack.split('\\n');\n            if (callStackFrames === undefined || callStackFrames.length < 4) {\n                logErrorOrWarn(message);\n            } else {\n                // Error:\n                //   logDedupedError\n                //   asyncApiBeingAccessedSynchronously\n                //   <userland callsite>\n                // TODO: This breaks if sourcemaps with ignore lists are enabled.\n                const key = callStackFrames[4];\n                errorRef.current = message;\n                flushCurrentErrorIfNew(key);\n            }\n        } else {}\n    };\n}\n\n//# sourceMappingURL=create-deduped-by-callsite-server-error-logger.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/server/dynamic-rendering-utils.js":
/*!**********************************************************************!*\
  !*** ../../node_modules/next/dist/server/dynamic-rendering-utils.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    isHangingPromiseRejectionError: function() {\n        return isHangingPromiseRejectionError;\n    },\n    makeHangingPromise: function() {\n        return makeHangingPromise;\n    }\n});\nfunction isHangingPromiseRejectionError(err) {\n    if (typeof err !== 'object' || err === null || !('digest' in err)) {\n        return false;\n    }\n    return err.digest === HANGING_PROMISE_REJECTION;\n}\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION';\nclass HangingPromiseRejectionError extends Error {\n    constructor(expression){\n        super(`During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context.`), this.expression = expression, this.digest = HANGING_PROMISE_REJECTION;\n    }\n}\nconst abortListenersBySignal = new WeakMap();\nfunction makeHangingPromise(signal, expression) {\n    if (signal.aborted) {\n        return Promise.reject(new HangingPromiseRejectionError(expression));\n    } else {\n        const hangingPromise = new Promise((_, reject)=>{\n            const boundRejection = reject.bind(null, new HangingPromiseRejectionError(expression));\n            let currentListeners = abortListenersBySignal.get(signal);\n            if (currentListeners) {\n                currentListeners.push(boundRejection);\n            } else {\n                const listeners = [\n                    boundRejection\n                ];\n                abortListenersBySignal.set(signal, listeners);\n                signal.addEventListener('abort', ()=>{\n                    for(let i = 0; i < listeners.length; i++){\n                        listeners[i]();\n                    }\n                }, {\n                    once: true\n                });\n            }\n        });\n        // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n        // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n        // your own promise out of it you'll need to ensure you handle the error when it rejects.\n        hangingPromise.catch(ignoreReject);\n        return hangingPromise;\n    }\n}\nfunction ignoreReject() {}\n\n//# sourceMappingURL=dynamic-rendering-utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/server/dynamic-rendering-utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/server/request/cookies.js":
/*!**************************************************************!*\
  !*** ../../node_modules/next/dist/server/request/cookies.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"cookies\", ({\n    enumerable: true,\n    get: function() {\n        return cookies;\n    }\n}));\nconst _requestcookies = __webpack_require__(/*! ../web/spec-extension/adapters/request-cookies */ \"(app-pages-browser)/../../node_modules/next/dist/server/web/spec-extension/adapters/request-cookies.js\");\nconst _cookies = __webpack_require__(/*! ../web/spec-extension/cookies */ \"(app-pages-browser)/../../node_modules/next/dist/server/web/spec-extension/cookies.js\");\nconst _workasyncstorageexternal = __webpack_require__(/*! ../app-render/work-async-storage.external */ \"(shared)/../../node_modules/next/dist/server/app-render/work-async-storage.external.js\");\nconst _workunitasyncstorageexternal = __webpack_require__(/*! ../app-render/work-unit-async-storage.external */ \"(shared)/../../node_modules/next/dist/server/app-render/work-unit-async-storage.external.js\");\nconst _dynamicrendering = __webpack_require__(/*! ../app-render/dynamic-rendering */ \"(app-pages-browser)/../../node_modules/next/dist/server/app-render/dynamic-rendering.js\");\nconst _staticgenerationbailout = __webpack_require__(/*! ../../client/components/static-generation-bailout */ \"(app-pages-browser)/../../node_modules/next/dist/client/components/static-generation-bailout.js\");\nconst _dynamicrenderingutils = __webpack_require__(/*! ../dynamic-rendering-utils */ \"(app-pages-browser)/../../node_modules/next/dist/server/dynamic-rendering-utils.js\");\nconst _creatededupedbycallsiteservererrorlogger = __webpack_require__(/*! ../create-deduped-by-callsite-server-error-logger */ \"(app-pages-browser)/../../node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js\");\nconst _scheduler = __webpack_require__(/*! ../../lib/scheduler */ \"(app-pages-browser)/../../node_modules/next/dist/lib/scheduler.js\");\nconst _utils = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/../../node_modules/next/dist/server/request/utils.js\");\nfunction cookies() {\n    const callingExpression = 'cookies';\n    const workStore = _workasyncstorageexternal.workAsyncStorage.getStore();\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workStore) {\n        if (workUnitStore && workUnitStore.phase === 'after' && !(0, _utils.isRequestAPICallableInsideAfter)()) {\n            throw Object.defineProperty(new Error(// TODO(after): clarify that this only applies to pages?\n            `Route ${workStore.route} used \"cookies\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"cookies\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`), \"__NEXT_ERROR_CODE\", {\n                value: \"E88\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workStore.forceStatic) {\n            // When using forceStatic we override all other logic and always just return an empty\n            // cookies object without tracking\n            const underlyingCookies = createEmptyCookies();\n            return makeUntrackedExoticCookies(underlyingCookies);\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'cache') {\n                throw Object.defineProperty(new Error(`Route ${workStore.route} used \"cookies\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E398\",\n                    enumerable: false,\n                    configurable: true\n                });\n            } else if (workUnitStore.type === 'unstable-cache') {\n                throw Object.defineProperty(new Error(`Route ${workStore.route} used \"cookies\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E157\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n        if (workStore.dynamicShouldError) {\n            throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`cookies\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n                value: \"E549\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'prerender') {\n                // dynamicIO Prerender\n                // We don't track dynamic access here because access will be tracked when you access\n                // one of the properties of the cookies object.\n                return makeDynamicallyTrackedExoticCookies(workStore.route, workUnitStore);\n            } else if (workUnitStore.type === 'prerender-ppr') {\n                // PPR Prerender (no dynamicIO)\n                // We are prerendering with PPR. We need track dynamic access here eagerly\n                // to keep continuity with how cookies has worked in PPR without dynamicIO.\n                (0, _dynamicrendering.postponeWithTracking)(workStore.route, callingExpression, workUnitStore.dynamicTracking);\n            } else if (workUnitStore.type === 'prerender-legacy') {\n                // Legacy Prerender\n                // We track dynamic access here so we don't need to wrap the cookies in\n                // individual property access tracking.\n                (0, _dynamicrendering.throwToInterruptStaticGeneration)(callingExpression, workStore, workUnitStore);\n            }\n        }\n        // We fall through to the dynamic context below but we still track dynamic access\n        // because in dev we can still error for things like using cookies inside a cache context\n        (0, _dynamicrendering.trackDynamicDataInDynamicRender)(workStore, workUnitStore);\n    }\n    // cookies is being called in a dynamic context\n    const requestStore = (0, _workunitasyncstorageexternal.getExpectedRequestStore)(callingExpression);\n    let underlyingCookies;\n    if ((0, _requestcookies.areCookiesMutableInCurrentPhase)(requestStore)) {\n        // We can't conditionally return different types here based on the context.\n        // To avoid confusion, we always return the readonly type here.\n        underlyingCookies = requestStore.userspaceMutableCookies;\n    } else {\n        underlyingCookies = requestStore.cookies;\n    }\n    if ( true && !(workStore == null ? void 0 : workStore.isPrefetchRequest)) {\n        return makeUntrackedExoticCookiesWithDevWarnings(underlyingCookies, workStore == null ? void 0 : workStore.route);\n    } else {\n        return makeUntrackedExoticCookies(underlyingCookies);\n    }\n}\nfunction createEmptyCookies() {\n    return _requestcookies.RequestCookiesAdapter.seal(new _cookies.RequestCookies(new Headers({})));\n}\nconst CachedCookies = new WeakMap();\nfunction makeDynamicallyTrackedExoticCookies(route, prerenderStore) {\n    const cachedPromise = CachedCookies.get(prerenderStore);\n    if (cachedPromise) {\n        return cachedPromise;\n    }\n    const promise = (0, _dynamicrenderingutils.makeHangingPromise)(prerenderStore.renderSignal, '`cookies()`');\n    CachedCookies.set(prerenderStore, promise);\n    Object.defineProperties(promise, {\n        [Symbol.iterator]: {\n            value: function() {\n                const expression = '`cookies()[Symbol.iterator]()`';\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        size: {\n            get () {\n                const expression = '`cookies().size`';\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        get: {\n            value: function get() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().get()`';\n                } else {\n                    expression = `\\`cookies().get(${describeNameArg(arguments[0])})\\``;\n                }\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        getAll: {\n            value: function getAll() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().getAll()`';\n                } else {\n                    expression = `\\`cookies().getAll(${describeNameArg(arguments[0])})\\``;\n                }\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        has: {\n            value: function has() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().has()`';\n                } else {\n                    expression = `\\`cookies().has(${describeNameArg(arguments[0])})\\``;\n                }\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        set: {\n            value: function set() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().set()`';\n                } else {\n                    const arg = arguments[0];\n                    if (arg) {\n                        expression = `\\`cookies().set(${describeNameArg(arg)}, ...)\\``;\n                    } else {\n                        expression = '`cookies().set(...)`';\n                    }\n                }\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        delete: {\n            value: function() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().delete()`';\n                } else if (arguments.length === 1) {\n                    expression = `\\`cookies().delete(${describeNameArg(arguments[0])})\\``;\n                } else {\n                    expression = `\\`cookies().delete(${describeNameArg(arguments[0])}, ...)\\``;\n                }\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        clear: {\n            value: function clear() {\n                const expression = '`cookies().clear()`';\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        toString: {\n            value: function toString() {\n                const expression = '`cookies().toString()`';\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        }\n    });\n    return promise;\n}\nfunction makeUntrackedExoticCookies(underlyingCookies) {\n    const cachedCookies = CachedCookies.get(underlyingCookies);\n    if (cachedCookies) {\n        return cachedCookies;\n    }\n    const promise = Promise.resolve(underlyingCookies);\n    CachedCookies.set(underlyingCookies, promise);\n    Object.defineProperties(promise, {\n        [Symbol.iterator]: {\n            value: underlyingCookies[Symbol.iterator] ? underlyingCookies[Symbol.iterator].bind(underlyingCookies) : // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesIterator.bind(underlyingCookies)\n        },\n        size: {\n            get () {\n                return underlyingCookies.size;\n            }\n        },\n        get: {\n            value: underlyingCookies.get.bind(underlyingCookies)\n        },\n        getAll: {\n            value: underlyingCookies.getAll.bind(underlyingCookies)\n        },\n        has: {\n            value: underlyingCookies.has.bind(underlyingCookies)\n        },\n        set: {\n            value: underlyingCookies.set.bind(underlyingCookies)\n        },\n        delete: {\n            value: underlyingCookies.delete.bind(underlyingCookies)\n        },\n        clear: {\n            value: // @ts-expect-error clear is defined in RequestCookies implementation but not in the type\n            typeof underlyingCookies.clear === 'function' ? underlyingCookies.clear.bind(underlyingCookies) : // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesClear.bind(underlyingCookies, promise)\n        },\n        toString: {\n            value: underlyingCookies.toString.bind(underlyingCookies)\n        }\n    });\n    return promise;\n}\nfunction makeUntrackedExoticCookiesWithDevWarnings(underlyingCookies, route) {\n    const cachedCookies = CachedCookies.get(underlyingCookies);\n    if (cachedCookies) {\n        return cachedCookies;\n    }\n    const promise = new Promise((resolve)=>(0, _scheduler.scheduleImmediate)(()=>resolve(underlyingCookies)));\n    CachedCookies.set(underlyingCookies, promise);\n    Object.defineProperties(promise, {\n        [Symbol.iterator]: {\n            value: function() {\n                const expression = '`...cookies()` or similar iteration';\n                syncIODev(route, expression);\n                return underlyingCookies[Symbol.iterator] ? underlyingCookies[Symbol.iterator].apply(underlyingCookies, arguments) : // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n                // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n                // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n                // has extra properties not available on RequestCookie instances.\n                polyfilledResponseCookiesIterator.call(underlyingCookies);\n            },\n            writable: false\n        },\n        size: {\n            get () {\n                const expression = '`cookies().size`';\n                syncIODev(route, expression);\n                return underlyingCookies.size;\n            }\n        },\n        get: {\n            value: function get() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().get()`';\n                } else {\n                    expression = `\\`cookies().get(${describeNameArg(arguments[0])})\\``;\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.get.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        getAll: {\n            value: function getAll() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().getAll()`';\n                } else {\n                    expression = `\\`cookies().getAll(${describeNameArg(arguments[0])})\\``;\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.getAll.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        has: {\n            value: function get() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().has()`';\n                } else {\n                    expression = `\\`cookies().has(${describeNameArg(arguments[0])})\\``;\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.has.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        set: {\n            value: function set() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().set()`';\n                } else {\n                    const arg = arguments[0];\n                    if (arg) {\n                        expression = `\\`cookies().set(${describeNameArg(arg)}, ...)\\``;\n                    } else {\n                        expression = '`cookies().set(...)`';\n                    }\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.set.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        delete: {\n            value: function() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().delete()`';\n                } else if (arguments.length === 1) {\n                    expression = `\\`cookies().delete(${describeNameArg(arguments[0])})\\``;\n                } else {\n                    expression = `\\`cookies().delete(${describeNameArg(arguments[0])}, ...)\\``;\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.delete.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        clear: {\n            value: function clear() {\n                const expression = '`cookies().clear()`';\n                syncIODev(route, expression);\n                // @ts-ignore clear is defined in RequestCookies implementation but not in the type\n                return typeof underlyingCookies.clear === 'function' ? underlyingCookies.clear.apply(underlyingCookies, arguments) : // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n                // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n                // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n                // has extra properties not available on RequestCookie instances.\n                polyfilledResponseCookiesClear.call(underlyingCookies, promise);\n            },\n            writable: false\n        },\n        toString: {\n            value: function toString() {\n                const expression = '`cookies().toString()` or implicit casting';\n                syncIODev(route, expression);\n                return underlyingCookies.toString.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        }\n    });\n    return promise;\n}\nfunction describeNameArg(arg) {\n    return typeof arg === 'object' && arg !== null && typeof arg.name === 'string' ? `'${arg.name}'` : typeof arg === 'string' ? `'${arg}'` : '...';\n}\nfunction syncIODev(route, expression) {\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workUnitStore && workUnitStore.type === 'request' && workUnitStore.prerenderPhase === true) {\n        // When we're rendering dynamically in dev we need to advance out of the\n        // Prerender environment when we read Request data synchronously\n        const requestStore = workUnitStore;\n        (0, _dynamicrendering.trackSynchronousRequestDataAccessInDev)(requestStore);\n    }\n    // In all cases we warn normally\n    warnForSyncAccess(route, expression);\n}\nconst warnForSyncAccess = (0, _creatededupedbycallsiteservererrorlogger.createDedupedByCallsiteServerErrorLoggerDev)(createCookiesAccessError);\nfunction createCookiesAccessError(route, expression) {\n    const prefix = route ? `Route \"${route}\" ` : 'This route ';\n    return Object.defineProperty(new Error(`${prefix}used ${expression}. ` + `\\`cookies()\\` should be awaited before using its value. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`), \"__NEXT_ERROR_CODE\", {\n        value: \"E223\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction polyfilledResponseCookiesIterator() {\n    return this.getAll().map((c)=>[\n            c.name,\n            c\n        ]).values();\n}\nfunction polyfilledResponseCookiesClear(returnable) {\n    for (const cookie of this.getAll()){\n        this.delete(cookie.name);\n    }\n    return returnable;\n}\n\n//# sourceMappingURL=cookies.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/server/request/cookies.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/server/request/draft-mode.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/next/dist/server/request/draft-mode.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"draftMode\", ({\n    enumerable: true,\n    get: function() {\n        return draftMode;\n    }\n}));\nconst _workunitasyncstorageexternal = __webpack_require__(/*! ../app-render/work-unit-async-storage.external */ \"(shared)/../../node_modules/next/dist/server/app-render/work-unit-async-storage.external.js\");\nconst _workasyncstorageexternal = __webpack_require__(/*! ../app-render/work-async-storage.external */ \"(shared)/../../node_modules/next/dist/server/app-render/work-async-storage.external.js\");\nconst _dynamicrendering = __webpack_require__(/*! ../app-render/dynamic-rendering */ \"(app-pages-browser)/../../node_modules/next/dist/server/app-render/dynamic-rendering.js\");\nconst _creatededupedbycallsiteservererrorlogger = __webpack_require__(/*! ../create-deduped-by-callsite-server-error-logger */ \"(app-pages-browser)/../../node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js\");\nconst _staticgenerationbailout = __webpack_require__(/*! ../../client/components/static-generation-bailout */ \"(app-pages-browser)/../../node_modules/next/dist/client/components/static-generation-bailout.js\");\nconst _hooksservercontext = __webpack_require__(/*! ../../client/components/hooks-server-context */ \"(app-pages-browser)/../../node_modules/next/dist/client/components/hooks-server-context.js\");\nfunction draftMode() {\n    const callingExpression = 'draftMode';\n    const workStore = _workasyncstorageexternal.workAsyncStorage.getStore();\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (!workStore || !workUnitStore) {\n        (0, _workunitasyncstorageexternal.throwForMissingRequestStore)(callingExpression);\n    }\n    switch(workUnitStore.type){\n        case 'request':\n            return createOrGetCachedExoticDraftMode(workUnitStore.draftMode, workStore);\n        case 'cache':\n        case 'unstable-cache':\n            // Inside of `\"use cache\"` or `unstable_cache`, draft mode is available if\n            // the outmost work unit store is a request store, and if draft mode is\n            // enabled.\n            const draftModeProvider = (0, _workunitasyncstorageexternal.getDraftModeProviderForCacheScope)(workStore, workUnitStore);\n            if (draftModeProvider) {\n                return createOrGetCachedExoticDraftMode(draftModeProvider, workStore);\n            }\n        // Otherwise, we fall through to providing an empty draft mode.\n        // eslint-disable-next-line no-fallthrough\n        case 'prerender':\n        case 'prerender-ppr':\n        case 'prerender-legacy':\n            // Return empty draft mode\n            if ( true && !(workStore == null ? void 0 : workStore.isPrefetchRequest)) {\n                const route = workStore == null ? void 0 : workStore.route;\n                return createExoticDraftModeWithDevWarnings(null, route);\n            } else {\n                return createExoticDraftMode(null);\n            }\n        default:\n            const _exhaustiveCheck = workUnitStore;\n            return _exhaustiveCheck;\n    }\n}\nfunction createOrGetCachedExoticDraftMode(draftModeProvider, workStore) {\n    const cachedDraftMode = CachedDraftModes.get(draftMode);\n    if (cachedDraftMode) {\n        return cachedDraftMode;\n    }\n    let promise;\n    if ( true && !(workStore == null ? void 0 : workStore.isPrefetchRequest)) {\n        const route = workStore == null ? void 0 : workStore.route;\n        promise = createExoticDraftModeWithDevWarnings(draftModeProvider, route);\n    } else {\n        promise = createExoticDraftMode(draftModeProvider);\n    }\n    CachedDraftModes.set(draftModeProvider, promise);\n    return promise;\n}\nconst CachedDraftModes = new WeakMap();\nfunction createExoticDraftMode(underlyingProvider) {\n    const instance = new DraftMode(underlyingProvider);\n    const promise = Promise.resolve(instance);\n    Object.defineProperty(promise, 'isEnabled', {\n        get () {\n            return instance.isEnabled;\n        },\n        set (newValue) {\n            Object.defineProperty(promise, 'isEnabled', {\n                value: newValue,\n                writable: true,\n                enumerable: true\n            });\n        },\n        enumerable: true,\n        configurable: true\n    });\n    promise.enable = instance.enable.bind(instance);\n    promise.disable = instance.disable.bind(instance);\n    return promise;\n}\nfunction createExoticDraftModeWithDevWarnings(underlyingProvider, route) {\n    const instance = new DraftMode(underlyingProvider);\n    const promise = Promise.resolve(instance);\n    Object.defineProperty(promise, 'isEnabled', {\n        get () {\n            const expression = '`draftMode().isEnabled`';\n            syncIODev(route, expression);\n            return instance.isEnabled;\n        },\n        set (newValue) {\n            Object.defineProperty(promise, 'isEnabled', {\n                value: newValue,\n                writable: true,\n                enumerable: true\n            });\n        },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(promise, 'enable', {\n        value: function get() {\n            const expression = '`draftMode().enable()`';\n            syncIODev(route, expression);\n            return instance.enable.apply(instance, arguments);\n        }\n    });\n    Object.defineProperty(promise, 'disable', {\n        value: function get() {\n            const expression = '`draftMode().disable()`';\n            syncIODev(route, expression);\n            return instance.disable.apply(instance, arguments);\n        }\n    });\n    return promise;\n}\nclass DraftMode {\n    constructor(provider){\n        this._provider = provider;\n    }\n    get isEnabled() {\n        if (this._provider !== null) {\n            return this._provider.isEnabled;\n        }\n        return false;\n    }\n    enable() {\n        // We have a store we want to track dynamic data access to ensure we\n        // don't statically generate routes that manipulate draft mode.\n        trackDynamicDraftMode('draftMode().enable()');\n        if (this._provider !== null) {\n            this._provider.enable();\n        }\n    }\n    disable() {\n        trackDynamicDraftMode('draftMode().disable()');\n        if (this._provider !== null) {\n            this._provider.disable();\n        }\n    }\n}\nfunction syncIODev(route, expression) {\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workUnitStore && workUnitStore.type === 'request' && workUnitStore.prerenderPhase === true) {\n        // When we're rendering dynamically in dev we need to advance out of the\n        // Prerender environment when we read Request data synchronously\n        const requestStore = workUnitStore;\n        (0, _dynamicrendering.trackSynchronousRequestDataAccessInDev)(requestStore);\n    }\n    // In all cases we warn normally\n    warnForSyncAccess(route, expression);\n}\nconst warnForSyncAccess = (0, _creatededupedbycallsiteservererrorlogger.createDedupedByCallsiteServerErrorLoggerDev)(createDraftModeAccessError);\nfunction createDraftModeAccessError(route, expression) {\n    const prefix = route ? `Route \"${route}\" ` : 'This route ';\n    return Object.defineProperty(new Error(`${prefix}used ${expression}. ` + `\\`draftMode()\\` should be awaited before using its value. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`), \"__NEXT_ERROR_CODE\", {\n        value: \"E377\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction trackDynamicDraftMode(expression) {\n    const store = _workasyncstorageexternal.workAsyncStorage.getStore();\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (store) {\n        // We have a store we want to track dynamic data access to ensure we\n        // don't statically generate routes that manipulate draft mode.\n        if (workUnitStore) {\n            if (workUnitStore.type === 'cache') {\n                throw Object.defineProperty(new Error(`Route ${store.route} used \"${expression}\" inside \"use cache\". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E246\",\n                    enumerable: false,\n                    configurable: true\n                });\n            } else if (workUnitStore.type === 'unstable-cache') {\n                throw Object.defineProperty(new Error(`Route ${store.route} used \"${expression}\" inside a function cached with \"unstable_cache(...)\". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E259\",\n                    enumerable: false,\n                    configurable: true\n                });\n            } else if (workUnitStore.phase === 'after') {\n                throw Object.defineProperty(new Error(`Route ${store.route} used \"${expression}\" inside \\`after\\`. The enabled status of draftMode can be read inside \\`after\\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E348\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n        if (store.dynamicShouldError) {\n            throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n                value: \"E553\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'prerender') {\n                // dynamicIO Prerender\n                const error = Object.defineProperty(new Error(`Route ${store.route} used ${expression} without first calling \\`await connection()\\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E126\",\n                    enumerable: false,\n                    configurable: true\n                });\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(store.route, expression, error, workUnitStore);\n            } else if (workUnitStore.type === 'prerender-ppr') {\n                // PPR Prerender\n                (0, _dynamicrendering.postponeWithTracking)(store.route, expression, workUnitStore.dynamicTracking);\n            } else if (workUnitStore.type === 'prerender-legacy') {\n                // legacy Prerender\n                workUnitStore.revalidate = 0;\n                const err = Object.defineProperty(new _hooksservercontext.DynamicServerError(`Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E558\",\n                    enumerable: false,\n                    configurable: true\n                });\n                store.dynamicUsageDescription = expression;\n                store.dynamicUsageStack = err.stack;\n                throw err;\n            } else if ( true && workUnitStore && workUnitStore.type === 'request') {\n                workUnitStore.usedDynamic = true;\n            }\n        }\n    }\n}\n\n//# sourceMappingURL=draft-mode.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/server/request/draft-mode.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/server/request/headers.js":
/*!**************************************************************!*\
  !*** ../../node_modules/next/dist/server/request/headers.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"headers\", ({\n    enumerable: true,\n    get: function() {\n        return headers;\n    }\n}));\nconst _headers = __webpack_require__(/*! ../web/spec-extension/adapters/headers */ \"(app-pages-browser)/../../node_modules/next/dist/server/web/spec-extension/adapters/headers.js\");\nconst _workasyncstorageexternal = __webpack_require__(/*! ../app-render/work-async-storage.external */ \"(shared)/../../node_modules/next/dist/server/app-render/work-async-storage.external.js\");\nconst _workunitasyncstorageexternal = __webpack_require__(/*! ../app-render/work-unit-async-storage.external */ \"(shared)/../../node_modules/next/dist/server/app-render/work-unit-async-storage.external.js\");\nconst _dynamicrendering = __webpack_require__(/*! ../app-render/dynamic-rendering */ \"(app-pages-browser)/../../node_modules/next/dist/server/app-render/dynamic-rendering.js\");\nconst _staticgenerationbailout = __webpack_require__(/*! ../../client/components/static-generation-bailout */ \"(app-pages-browser)/../../node_modules/next/dist/client/components/static-generation-bailout.js\");\nconst _dynamicrenderingutils = __webpack_require__(/*! ../dynamic-rendering-utils */ \"(app-pages-browser)/../../node_modules/next/dist/server/dynamic-rendering-utils.js\");\nconst _creatededupedbycallsiteservererrorlogger = __webpack_require__(/*! ../create-deduped-by-callsite-server-error-logger */ \"(app-pages-browser)/../../node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js\");\nconst _scheduler = __webpack_require__(/*! ../../lib/scheduler */ \"(app-pages-browser)/../../node_modules/next/dist/lib/scheduler.js\");\nconst _utils = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/../../node_modules/next/dist/server/request/utils.js\");\nfunction headers() {\n    const workStore = _workasyncstorageexternal.workAsyncStorage.getStore();\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workStore) {\n        if (workUnitStore && workUnitStore.phase === 'after' && !(0, _utils.isRequestAPICallableInsideAfter)()) {\n            throw Object.defineProperty(new Error(`Route ${workStore.route} used \"headers\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"headers\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`), \"__NEXT_ERROR_CODE\", {\n                value: \"E367\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workStore.forceStatic) {\n            // When using forceStatic we override all other logic and always just return an empty\n            // headers object without tracking\n            const underlyingHeaders = _headers.HeadersAdapter.seal(new Headers({}));\n            return makeUntrackedExoticHeaders(underlyingHeaders);\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'cache') {\n                throw Object.defineProperty(new Error(`Route ${workStore.route} used \"headers\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E304\",\n                    enumerable: false,\n                    configurable: true\n                });\n            } else if (workUnitStore.type === 'unstable-cache') {\n                throw Object.defineProperty(new Error(`Route ${workStore.route} used \"headers\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E127\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n        if (workStore.dynamicShouldError) {\n            throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`headers\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n                value: \"E525\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'prerender') {\n                // dynamicIO Prerender\n                // We don't track dynamic access here because access will be tracked when you access\n                // one of the properties of the headers object.\n                return makeDynamicallyTrackedExoticHeaders(workStore.route, workUnitStore);\n            } else if (workUnitStore.type === 'prerender-ppr') {\n                // PPR Prerender (no dynamicIO)\n                // We are prerendering with PPR. We need track dynamic access here eagerly\n                // to keep continuity with how headers has worked in PPR without dynamicIO.\n                // TODO consider switching the semantic to throw on property access instead\n                (0, _dynamicrendering.postponeWithTracking)(workStore.route, 'headers', workUnitStore.dynamicTracking);\n            } else if (workUnitStore.type === 'prerender-legacy') {\n                // Legacy Prerender\n                // We are in a legacy static generation mode while prerendering\n                // We track dynamic access here so we don't need to wrap the headers in\n                // individual property access tracking.\n                (0, _dynamicrendering.throwToInterruptStaticGeneration)('headers', workStore, workUnitStore);\n            }\n        }\n        // We fall through to the dynamic context below but we still track dynamic access\n        // because in dev we can still error for things like using headers inside a cache context\n        (0, _dynamicrendering.trackDynamicDataInDynamicRender)(workStore, workUnitStore);\n    }\n    const requestStore = (0, _workunitasyncstorageexternal.getExpectedRequestStore)('headers');\n    if ( true && !(workStore == null ? void 0 : workStore.isPrefetchRequest)) {\n        return makeUntrackedExoticHeadersWithDevWarnings(requestStore.headers, workStore == null ? void 0 : workStore.route);\n    } else {\n        return makeUntrackedExoticHeaders(requestStore.headers);\n    }\n}\nconst CachedHeaders = new WeakMap();\nfunction makeDynamicallyTrackedExoticHeaders(route, prerenderStore) {\n    const cachedHeaders = CachedHeaders.get(prerenderStore);\n    if (cachedHeaders) {\n        return cachedHeaders;\n    }\n    const promise = (0, _dynamicrenderingutils.makeHangingPromise)(prerenderStore.renderSignal, '`headers()`');\n    CachedHeaders.set(prerenderStore, promise);\n    Object.defineProperties(promise, {\n        append: {\n            value: function append() {\n                const expression = `\\`headers().append(${describeNameArg(arguments[0])}, ...)\\``;\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        delete: {\n            value: function _delete() {\n                const expression = `\\`headers().delete(${describeNameArg(arguments[0])})\\``;\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        get: {\n            value: function get() {\n                const expression = `\\`headers().get(${describeNameArg(arguments[0])})\\``;\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        has: {\n            value: function has() {\n                const expression = `\\`headers().has(${describeNameArg(arguments[0])})\\``;\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        set: {\n            value: function set() {\n                const expression = `\\`headers().set(${describeNameArg(arguments[0])}, ...)\\``;\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        getSetCookie: {\n            value: function getSetCookie() {\n                const expression = '`headers().getSetCookie()`';\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        forEach: {\n            value: function forEach() {\n                const expression = '`headers().forEach(...)`';\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        keys: {\n            value: function keys() {\n                const expression = '`headers().keys()`';\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        values: {\n            value: function values() {\n                const expression = '`headers().values()`';\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        entries: {\n            value: function entries() {\n                const expression = '`headers().entries()`';\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        [Symbol.iterator]: {\n            value: function() {\n                const expression = '`headers()[Symbol.iterator]()`';\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        }\n    });\n    return promise;\n}\nfunction makeUntrackedExoticHeaders(underlyingHeaders) {\n    const cachedHeaders = CachedHeaders.get(underlyingHeaders);\n    if (cachedHeaders) {\n        return cachedHeaders;\n    }\n    const promise = Promise.resolve(underlyingHeaders);\n    CachedHeaders.set(underlyingHeaders, promise);\n    Object.defineProperties(promise, {\n        append: {\n            value: underlyingHeaders.append.bind(underlyingHeaders)\n        },\n        delete: {\n            value: underlyingHeaders.delete.bind(underlyingHeaders)\n        },\n        get: {\n            value: underlyingHeaders.get.bind(underlyingHeaders)\n        },\n        has: {\n            value: underlyingHeaders.has.bind(underlyingHeaders)\n        },\n        set: {\n            value: underlyingHeaders.set.bind(underlyingHeaders)\n        },\n        getSetCookie: {\n            value: underlyingHeaders.getSetCookie.bind(underlyingHeaders)\n        },\n        forEach: {\n            value: underlyingHeaders.forEach.bind(underlyingHeaders)\n        },\n        keys: {\n            value: underlyingHeaders.keys.bind(underlyingHeaders)\n        },\n        values: {\n            value: underlyingHeaders.values.bind(underlyingHeaders)\n        },\n        entries: {\n            value: underlyingHeaders.entries.bind(underlyingHeaders)\n        },\n        [Symbol.iterator]: {\n            value: underlyingHeaders[Symbol.iterator].bind(underlyingHeaders)\n        }\n    });\n    return promise;\n}\nfunction makeUntrackedExoticHeadersWithDevWarnings(underlyingHeaders, route) {\n    const cachedHeaders = CachedHeaders.get(underlyingHeaders);\n    if (cachedHeaders) {\n        return cachedHeaders;\n    }\n    const promise = new Promise((resolve)=>(0, _scheduler.scheduleImmediate)(()=>resolve(underlyingHeaders)));\n    CachedHeaders.set(underlyingHeaders, promise);\n    Object.defineProperties(promise, {\n        append: {\n            value: function append() {\n                const expression = `\\`headers().append(${describeNameArg(arguments[0])}, ...)\\``;\n                syncIODev(route, expression);\n                return underlyingHeaders.append.apply(underlyingHeaders, arguments);\n            }\n        },\n        delete: {\n            value: function _delete() {\n                const expression = `\\`headers().delete(${describeNameArg(arguments[0])})\\``;\n                syncIODev(route, expression);\n                return underlyingHeaders.delete.apply(underlyingHeaders, arguments);\n            }\n        },\n        get: {\n            value: function get() {\n                const expression = `\\`headers().get(${describeNameArg(arguments[0])})\\``;\n                syncIODev(route, expression);\n                return underlyingHeaders.get.apply(underlyingHeaders, arguments);\n            }\n        },\n        has: {\n            value: function has() {\n                const expression = `\\`headers().has(${describeNameArg(arguments[0])})\\``;\n                syncIODev(route, expression);\n                return underlyingHeaders.has.apply(underlyingHeaders, arguments);\n            }\n        },\n        set: {\n            value: function set() {\n                const expression = `\\`headers().set(${describeNameArg(arguments[0])}, ...)\\``;\n                syncIODev(route, expression);\n                return underlyingHeaders.set.apply(underlyingHeaders, arguments);\n            }\n        },\n        getSetCookie: {\n            value: function getSetCookie() {\n                const expression = '`headers().getSetCookie()`';\n                syncIODev(route, expression);\n                return underlyingHeaders.getSetCookie.apply(underlyingHeaders, arguments);\n            }\n        },\n        forEach: {\n            value: function forEach() {\n                const expression = '`headers().forEach(...)`';\n                syncIODev(route, expression);\n                return underlyingHeaders.forEach.apply(underlyingHeaders, arguments);\n            }\n        },\n        keys: {\n            value: function keys() {\n                const expression = '`headers().keys()`';\n                syncIODev(route, expression);\n                return underlyingHeaders.keys.apply(underlyingHeaders, arguments);\n            }\n        },\n        values: {\n            value: function values() {\n                const expression = '`headers().values()`';\n                syncIODev(route, expression);\n                return underlyingHeaders.values.apply(underlyingHeaders, arguments);\n            }\n        },\n        entries: {\n            value: function entries() {\n                const expression = '`headers().entries()`';\n                syncIODev(route, expression);\n                return underlyingHeaders.entries.apply(underlyingHeaders, arguments);\n            }\n        },\n        [Symbol.iterator]: {\n            value: function() {\n                const expression = '`...headers()` or similar iteration';\n                syncIODev(route, expression);\n                return underlyingHeaders[Symbol.iterator].apply(underlyingHeaders, arguments);\n            }\n        }\n    });\n    return promise;\n}\nfunction describeNameArg(arg) {\n    return typeof arg === 'string' ? `'${arg}'` : '...';\n}\nfunction syncIODev(route, expression) {\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workUnitStore && workUnitStore.type === 'request' && workUnitStore.prerenderPhase === true) {\n        // When we're rendering dynamically in dev we need to advance out of the\n        // Prerender environment when we read Request data synchronously\n        const requestStore = workUnitStore;\n        (0, _dynamicrendering.trackSynchronousRequestDataAccessInDev)(requestStore);\n    }\n    // In all cases we warn normally\n    warnForSyncAccess(route, expression);\n}\nconst warnForSyncAccess = (0, _creatededupedbycallsiteservererrorlogger.createDedupedByCallsiteServerErrorLoggerDev)(createHeadersAccessError);\nfunction createHeadersAccessError(route, expression) {\n    const prefix = route ? `Route \"${route}\" ` : 'This route ';\n    return Object.defineProperty(new Error(`${prefix}used ${expression}. ` + `\\`headers()\\` should be awaited before using its value. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`), \"__NEXT_ERROR_CODE\", {\n        value: \"E277\",\n        enumerable: false,\n        configurable: true\n    });\n}\n\n//# sourceMappingURL=headers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/server/request/headers.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/server/request/utils.js":
/*!************************************************************!*\
  !*** ../../node_modules/next/dist/server/request/utils.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    isRequestAPICallableInsideAfter: function() {\n        return isRequestAPICallableInsideAfter;\n    },\n    throwForSearchParamsAccessInUseCache: function() {\n        return throwForSearchParamsAccessInUseCache;\n    },\n    throwWithStaticGenerationBailoutError: function() {\n        return throwWithStaticGenerationBailoutError;\n    },\n    throwWithStaticGenerationBailoutErrorWithDynamicError: function() {\n        return throwWithStaticGenerationBailoutErrorWithDynamicError;\n    }\n});\nconst _staticgenerationbailout = __webpack_require__(/*! ../../client/components/static-generation-bailout */ \"(app-pages-browser)/../../node_modules/next/dist/client/components/static-generation-bailout.js\");\nconst _aftertaskasyncstorageexternal = __webpack_require__(/*! ../app-render/after-task-async-storage.external */ \"(app-pages-browser)/../../node_modules/next/dist/server/app-render/after-task-async-storage.external.js\");\nfunction throwWithStaticGenerationBailoutError(route, expression) {\n    throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route ${route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n        value: \"E576\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction throwWithStaticGenerationBailoutErrorWithDynamicError(route, expression) {\n    throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route ${route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n        value: \"E543\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction throwForSearchParamsAccessInUseCache(workStore) {\n    const error = Object.defineProperty(new Error(`Route ${workStore.route} used \"searchParams\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"searchParams\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`), \"__NEXT_ERROR_CODE\", {\n        value: \"E634\",\n        enumerable: false,\n        configurable: true\n    });\n    workStore.invalidUsageError ??= error;\n    throw error;\n}\nfunction isRequestAPICallableInsideAfter() {\n    const afterTaskStore = _aftertaskasyncstorageexternal.afterTaskAsyncStorage.getStore();\n    return (afterTaskStore == null ? void 0 : afterTaskStore.rootTaskSpawnPhase) === 'action';\n}\n\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/server/request/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/server/web/spec-extension/adapters/headers.js":
/*!**********************************************************************************!*\
  !*** ../../node_modules/next/dist/server/web/spec-extension/adapters/headers.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    HeadersAdapter: function() {\n        return HeadersAdapter;\n    },\n    ReadonlyHeadersError: function() {\n        return ReadonlyHeadersError;\n    }\n});\nconst _reflect = __webpack_require__(/*! ./reflect */ \"(app-pages-browser)/../../node_modules/next/dist/server/web/spec-extension/adapters/reflect.js\");\nclass ReadonlyHeadersError extends Error {\n    constructor(){\n        super('Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers');\n    }\n    static callable() {\n        throw new ReadonlyHeadersError();\n    }\n}\nclass HeadersAdapter extends Headers {\n    constructor(headers){\n        // We've already overridden the methods that would be called, so we're just\n        // calling the super constructor to ensure that the instanceof check works.\n        super();\n        this.headers = new Proxy(headers, {\n            get (target, prop, receiver) {\n                // Because this is just an object, we expect that all \"get\" operations\n                // are for properties. If it's a \"get\" for a symbol, we'll just return\n                // the symbol.\n                if (typeof prop === 'symbol') {\n                    return _reflect.ReflectAdapter.get(target, prop, receiver);\n                }\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, return undefined.\n                if (typeof original === 'undefined') return;\n                // If the original casing exists, return the value.\n                return _reflect.ReflectAdapter.get(target, original, receiver);\n            },\n            set (target, prop, value, receiver) {\n                if (typeof prop === 'symbol') {\n                    return _reflect.ReflectAdapter.set(target, prop, value, receiver);\n                }\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, use the prop as the key.\n                return _reflect.ReflectAdapter.set(target, original ?? prop, value, receiver);\n            },\n            has (target, prop) {\n                if (typeof prop === 'symbol') return _reflect.ReflectAdapter.has(target, prop);\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, return false.\n                if (typeof original === 'undefined') return false;\n                // If the original casing exists, return true.\n                return _reflect.ReflectAdapter.has(target, original);\n            },\n            deleteProperty (target, prop) {\n                if (typeof prop === 'symbol') return _reflect.ReflectAdapter.deleteProperty(target, prop);\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, return true.\n                if (typeof original === 'undefined') return true;\n                // If the original casing exists, delete the property.\n                return _reflect.ReflectAdapter.deleteProperty(target, original);\n            }\n        });\n    }\n    /**\n   * Seals a Headers instance to prevent modification by throwing an error when\n   * any mutating method is called.\n   */ static seal(headers) {\n        return new Proxy(headers, {\n            get (target, prop, receiver) {\n                switch(prop){\n                    case 'append':\n                    case 'delete':\n                    case 'set':\n                        return ReadonlyHeadersError.callable;\n                    default:\n                        return _reflect.ReflectAdapter.get(target, prop, receiver);\n                }\n            }\n        });\n    }\n    /**\n   * Merges a header value into a string. This stores multiple values as an\n   * array, so we need to merge them into a string.\n   *\n   * @param value a header value\n   * @returns a merged header value (a string)\n   */ merge(value) {\n        if (Array.isArray(value)) return value.join(', ');\n        return value;\n    }\n    /**\n   * Creates a Headers instance from a plain object or a Headers instance.\n   *\n   * @param headers a plain object or a Headers instance\n   * @returns a headers instance\n   */ static from(headers) {\n        if (headers instanceof Headers) return headers;\n        return new HeadersAdapter(headers);\n    }\n    append(name, value) {\n        const existing = this.headers[name];\n        if (typeof existing === 'string') {\n            this.headers[name] = [\n                existing,\n                value\n            ];\n        } else if (Array.isArray(existing)) {\n            existing.push(value);\n        } else {\n            this.headers[name] = value;\n        }\n    }\n    delete(name) {\n        delete this.headers[name];\n    }\n    get(name) {\n        const value = this.headers[name];\n        if (typeof value !== 'undefined') return this.merge(value);\n        return null;\n    }\n    has(name) {\n        return typeof this.headers[name] !== 'undefined';\n    }\n    set(name, value) {\n        this.headers[name] = value;\n    }\n    forEach(callbackfn, thisArg) {\n        for (const [name, value] of this.entries()){\n            callbackfn.call(thisArg, value, name, this);\n        }\n    }\n    *entries() {\n        for (const key of Object.keys(this.headers)){\n            const name = key.toLowerCase();\n            // We assert here that this is a string because we got it from the\n            // Object.keys() call above.\n            const value = this.get(name);\n            yield [\n                name,\n                value\n            ];\n        }\n    }\n    *keys() {\n        for (const key of Object.keys(this.headers)){\n            const name = key.toLowerCase();\n            yield name;\n        }\n    }\n    *values() {\n        for (const key of Object.keys(this.headers)){\n            // We assert here that this is a string because we got it from the\n            // Object.keys() call above.\n            const value = this.get(key);\n            yield value;\n        }\n    }\n    [Symbol.iterator]() {\n        return this.entries();\n    }\n}\n\n//# sourceMappingURL=headers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/server/web/spec-extension/adapters/headers.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/server/web/spec-extension/adapters/reflect.js":
/*!**********************************************************************************!*\
  !*** ../../node_modules/next/dist/server/web/spec-extension/adapters/reflect.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ReflectAdapter\", ({\n    enumerable: true,\n    get: function() {\n        return ReflectAdapter;\n    }\n}));\nclass ReflectAdapter {\n    static get(target, prop, receiver) {\n        const value = Reflect.get(target, prop, receiver);\n        if (typeof value === 'function') {\n            return value.bind(target);\n        }\n        return value;\n    }\n    static set(target, prop, value, receiver) {\n        return Reflect.set(target, prop, value, receiver);\n    }\n    static has(target, prop) {\n        return Reflect.has(target, prop);\n    }\n    static deleteProperty(target, prop) {\n        return Reflect.deleteProperty(target, prop);\n    }\n}\n\n//# sourceMappingURL=reflect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci93ZWIvc3BlYy1leHRlbnNpb24vYWRhcHRlcnMvcmVmbGVjdC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLGtEQUFpRDtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxKdWFuIFB1bGdhcmluXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXGNoaWEtbmV4dFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxzZXJ2ZXJcXHdlYlxcc3BlYy1leHRlbnNpb25cXGFkYXB0ZXJzXFxyZWZsZWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiUmVmbGVjdEFkYXB0ZXJcIiwge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIFJlZmxlY3RBZGFwdGVyO1xuICAgIH1cbn0pO1xuY2xhc3MgUmVmbGVjdEFkYXB0ZXIge1xuICAgIHN0YXRpYyBnZXQodGFyZ2V0LCBwcm9wLCByZWNlaXZlcikge1xuICAgICAgICBjb25zdCB2YWx1ZSA9IFJlZmxlY3QuZ2V0KHRhcmdldCwgcHJvcCwgcmVjZWl2ZXIpO1xuICAgICAgICBpZiAodHlwZW9mIHZhbHVlID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgICAgICByZXR1cm4gdmFsdWUuYmluZCh0YXJnZXQpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB2YWx1ZTtcbiAgICB9XG4gICAgc3RhdGljIHNldCh0YXJnZXQsIHByb3AsIHZhbHVlLCByZWNlaXZlcikge1xuICAgICAgICByZXR1cm4gUmVmbGVjdC5zZXQodGFyZ2V0LCBwcm9wLCB2YWx1ZSwgcmVjZWl2ZXIpO1xuICAgIH1cbiAgICBzdGF0aWMgaGFzKHRhcmdldCwgcHJvcCkge1xuICAgICAgICByZXR1cm4gUmVmbGVjdC5oYXModGFyZ2V0LCBwcm9wKTtcbiAgICB9XG4gICAgc3RhdGljIGRlbGV0ZVByb3BlcnR5KHRhcmdldCwgcHJvcCkge1xuICAgICAgICByZXR1cm4gUmVmbGVjdC5kZWxldGVQcm9wZXJ0eSh0YXJnZXQsIHByb3ApO1xuICAgIH1cbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cmVmbGVjdC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/server/web/spec-extension/adapters/reflect.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/server/web/spec-extension/adapters/request-cookies.js":
/*!******************************************************************************************!*\
  !*** ../../node_modules/next/dist/server/web/spec-extension/adapters/request-cookies.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    MutableRequestCookiesAdapter: function() {\n        return MutableRequestCookiesAdapter;\n    },\n    ReadonlyRequestCookiesError: function() {\n        return ReadonlyRequestCookiesError;\n    },\n    RequestCookiesAdapter: function() {\n        return RequestCookiesAdapter;\n    },\n    appendMutableCookies: function() {\n        return appendMutableCookies;\n    },\n    areCookiesMutableInCurrentPhase: function() {\n        return areCookiesMutableInCurrentPhase;\n    },\n    getModifiedCookieValues: function() {\n        return getModifiedCookieValues;\n    },\n    responseCookiesToRequestCookies: function() {\n        return responseCookiesToRequestCookies;\n    },\n    wrapWithMutableAccessCheck: function() {\n        return wrapWithMutableAccessCheck;\n    }\n});\nconst _cookies = __webpack_require__(/*! ../cookies */ \"(app-pages-browser)/../../node_modules/next/dist/server/web/spec-extension/cookies.js\");\nconst _reflect = __webpack_require__(/*! ./reflect */ \"(app-pages-browser)/../../node_modules/next/dist/server/web/spec-extension/adapters/reflect.js\");\nconst _workasyncstorageexternal = __webpack_require__(/*! ../../../app-render/work-async-storage.external */ \"(shared)/../../node_modules/next/dist/server/app-render/work-async-storage.external.js\");\nconst _workunitasyncstorageexternal = __webpack_require__(/*! ../../../app-render/work-unit-async-storage.external */ \"(shared)/../../node_modules/next/dist/server/app-render/work-unit-async-storage.external.js\");\nclass ReadonlyRequestCookiesError extends Error {\n    constructor(){\n        super('Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options');\n    }\n    static callable() {\n        throw new ReadonlyRequestCookiesError();\n    }\n}\nclass RequestCookiesAdapter {\n    static seal(cookies) {\n        return new Proxy(cookies, {\n            get (target, prop, receiver) {\n                switch(prop){\n                    case 'clear':\n                    case 'delete':\n                    case 'set':\n                        return ReadonlyRequestCookiesError.callable;\n                    default:\n                        return _reflect.ReflectAdapter.get(target, prop, receiver);\n                }\n            }\n        });\n    }\n}\nconst SYMBOL_MODIFY_COOKIE_VALUES = Symbol.for('next.mutated.cookies');\nfunction getModifiedCookieValues(cookies) {\n    const modified = cookies[SYMBOL_MODIFY_COOKIE_VALUES];\n    if (!modified || !Array.isArray(modified) || modified.length === 0) {\n        return [];\n    }\n    return modified;\n}\nfunction appendMutableCookies(headers, mutableCookies) {\n    const modifiedCookieValues = getModifiedCookieValues(mutableCookies);\n    if (modifiedCookieValues.length === 0) {\n        return false;\n    }\n    // Return a new response that extends the response with\n    // the modified cookies as fallbacks. `res` cookies\n    // will still take precedence.\n    const resCookies = new _cookies.ResponseCookies(headers);\n    const returnedCookies = resCookies.getAll();\n    // Set the modified cookies as fallbacks.\n    for (const cookie of modifiedCookieValues){\n        resCookies.set(cookie);\n    }\n    // Set the original cookies as the final values.\n    for (const cookie of returnedCookies){\n        resCookies.set(cookie);\n    }\n    return true;\n}\nclass MutableRequestCookiesAdapter {\n    static wrap(cookies, onUpdateCookies) {\n        const responseCookies = new _cookies.ResponseCookies(new Headers());\n        for (const cookie of cookies.getAll()){\n            responseCookies.set(cookie);\n        }\n        let modifiedValues = [];\n        const modifiedCookies = new Set();\n        const updateResponseCookies = ()=>{\n            // TODO-APP: change method of getting workStore\n            const workStore = _workasyncstorageexternal.workAsyncStorage.getStore();\n            if (workStore) {\n                workStore.pathWasRevalidated = true;\n            }\n            const allCookies = responseCookies.getAll();\n            modifiedValues = allCookies.filter((c)=>modifiedCookies.has(c.name));\n            if (onUpdateCookies) {\n                const serializedCookies = [];\n                for (const cookie of modifiedValues){\n                    const tempCookies = new _cookies.ResponseCookies(new Headers());\n                    tempCookies.set(cookie);\n                    serializedCookies.push(tempCookies.toString());\n                }\n                onUpdateCookies(serializedCookies);\n            }\n        };\n        const wrappedCookies = new Proxy(responseCookies, {\n            get (target, prop, receiver) {\n                switch(prop){\n                    // A special symbol to get the modified cookie values\n                    case SYMBOL_MODIFY_COOKIE_VALUES:\n                        return modifiedValues;\n                    // TODO: Throw error if trying to set a cookie after the response\n                    // headers have been set.\n                    case 'delete':\n                        return function(...args) {\n                            modifiedCookies.add(typeof args[0] === 'string' ? args[0] : args[0].name);\n                            try {\n                                target.delete(...args);\n                                return wrappedCookies;\n                            } finally{\n                                updateResponseCookies();\n                            }\n                        };\n                    case 'set':\n                        return function(...args) {\n                            modifiedCookies.add(typeof args[0] === 'string' ? args[0] : args[0].name);\n                            try {\n                                target.set(...args);\n                                return wrappedCookies;\n                            } finally{\n                                updateResponseCookies();\n                            }\n                        };\n                    default:\n                        return _reflect.ReflectAdapter.get(target, prop, receiver);\n                }\n            }\n        });\n        return wrappedCookies;\n    }\n}\nfunction wrapWithMutableAccessCheck(responseCookies) {\n    const wrappedCookies = new Proxy(responseCookies, {\n        get (target, prop, receiver) {\n            switch(prop){\n                case 'delete':\n                    return function(...args) {\n                        ensureCookiesAreStillMutable('cookies().delete');\n                        target.delete(...args);\n                        return wrappedCookies;\n                    };\n                case 'set':\n                    return function(...args) {\n                        ensureCookiesAreStillMutable('cookies().set');\n                        target.set(...args);\n                        return wrappedCookies;\n                    };\n                default:\n                    return _reflect.ReflectAdapter.get(target, prop, receiver);\n            }\n        }\n    });\n    return wrappedCookies;\n}\nfunction areCookiesMutableInCurrentPhase(requestStore) {\n    return requestStore.phase === 'action';\n}\n/** Ensure that cookies() starts throwing on mutation\n * if we changed phases and can no longer mutate.\n *\n * This can happen when going:\n *   'render' -> 'after'\n *   'action' -> 'render'\n * */ function ensureCookiesAreStillMutable(callingExpression) {\n    const requestStore = (0, _workunitasyncstorageexternal.getExpectedRequestStore)(callingExpression);\n    if (!areCookiesMutableInCurrentPhase(requestStore)) {\n        // TODO: maybe we can give a more precise error message based on callingExpression?\n        throw new ReadonlyRequestCookiesError();\n    }\n}\nfunction responseCookiesToRequestCookies(responseCookies) {\n    const requestCookies = new _cookies.RequestCookies(new Headers());\n    for (const cookie of responseCookies.getAll()){\n        requestCookies.set(cookie);\n    }\n    return requestCookies;\n}\n\n//# sourceMappingURL=request-cookies.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/server/web/spec-extension/adapters/request-cookies.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/next/dist/server/web/spec-extension/cookies.js":
/*!*************************************************************************!*\
  !*** ../../node_modules/next/dist/server/web/spec-extension/cookies.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    RequestCookies: function() {\n        return _cookies.RequestCookies;\n    },\n    ResponseCookies: function() {\n        return _cookies.ResponseCookies;\n    },\n    stringifyCookie: function() {\n        return _cookies.stringifyCookie;\n    }\n});\nconst _cookies = __webpack_require__(/*! next/dist/compiled/@edge-runtime/cookies */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/@edge-runtime/cookies/index.js\");\n\n//# sourceMappingURL=cookies.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci93ZWIvc3BlYy1leHRlbnNpb24vY29va2llcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLE1BQU0sQ0FJTDtBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNELGlCQUFpQixtQkFBTyxDQUFDLDBJQUEwQzs7QUFFbkUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcSnVhbiBQdWxnYXJpblxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxjaGlhLW5leHRcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcc2VydmVyXFx3ZWJcXHNwZWMtZXh0ZW5zaW9uXFxjb29raWVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuMCAmJiAobW9kdWxlLmV4cG9ydHMgPSB7XG4gICAgUmVxdWVzdENvb2tpZXM6IG51bGwsXG4gICAgUmVzcG9uc2VDb29raWVzOiBudWxsLFxuICAgIHN0cmluZ2lmeUNvb2tpZTogbnVsbFxufSk7XG5mdW5jdGlvbiBfZXhwb3J0KHRhcmdldCwgYWxsKSB7XG4gICAgZm9yKHZhciBuYW1lIGluIGFsbClPYmplY3QuZGVmaW5lUHJvcGVydHkodGFyZ2V0LCBuYW1lLCB7XG4gICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgIGdldDogYWxsW25hbWVdXG4gICAgfSk7XG59XG5fZXhwb3J0KGV4cG9ydHMsIHtcbiAgICBSZXF1ZXN0Q29va2llczogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBfY29va2llcy5SZXF1ZXN0Q29va2llcztcbiAgICB9LFxuICAgIFJlc3BvbnNlQ29va2llczogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBfY29va2llcy5SZXNwb25zZUNvb2tpZXM7XG4gICAgfSxcbiAgICBzdHJpbmdpZnlDb29raWU6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gX2Nvb2tpZXMuc3RyaW5naWZ5Q29va2llO1xuICAgIH1cbn0pO1xuY29uc3QgX2Nvb2tpZXMgPSByZXF1aXJlKFwibmV4dC9kaXN0L2NvbXBpbGVkL0BlZGdlLXJ1bnRpbWUvY29va2llc1wiKTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29va2llcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/server/web/spec-extension/cookies.js\n"));

/***/ }),

/***/ "(shared)/../../node_modules/next/dist/client/components/app-router-headers.js":
/*!****************************************************************************!*\
  !*** ../../node_modules/next/dist/client/components/app-router-headers.js ***!
  \****************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ACTION_HEADER: function() {\n        return ACTION_HEADER;\n    },\n    FLIGHT_HEADERS: function() {\n        return FLIGHT_HEADERS;\n    },\n    NEXT_DID_POSTPONE_HEADER: function() {\n        return NEXT_DID_POSTPONE_HEADER;\n    },\n    NEXT_HMR_REFRESH_HASH_COOKIE: function() {\n        return NEXT_HMR_REFRESH_HASH_COOKIE;\n    },\n    NEXT_HMR_REFRESH_HEADER: function() {\n        return NEXT_HMR_REFRESH_HEADER;\n    },\n    NEXT_IS_PRERENDER_HEADER: function() {\n        return NEXT_IS_PRERENDER_HEADER;\n    },\n    NEXT_REWRITTEN_PATH_HEADER: function() {\n        return NEXT_REWRITTEN_PATH_HEADER;\n    },\n    NEXT_REWRITTEN_QUERY_HEADER: function() {\n        return NEXT_REWRITTEN_QUERY_HEADER;\n    },\n    NEXT_ROUTER_PREFETCH_HEADER: function() {\n        return NEXT_ROUTER_PREFETCH_HEADER;\n    },\n    NEXT_ROUTER_SEGMENT_PREFETCH_HEADER: function() {\n        return NEXT_ROUTER_SEGMENT_PREFETCH_HEADER;\n    },\n    NEXT_ROUTER_STALE_TIME_HEADER: function() {\n        return NEXT_ROUTER_STALE_TIME_HEADER;\n    },\n    NEXT_ROUTER_STATE_TREE_HEADER: function() {\n        return NEXT_ROUTER_STATE_TREE_HEADER;\n    },\n    NEXT_RSC_UNION_QUERY: function() {\n        return NEXT_RSC_UNION_QUERY;\n    },\n    NEXT_URL: function() {\n        return NEXT_URL;\n    },\n    RSC_CONTENT_TYPE_HEADER: function() {\n        return RSC_CONTENT_TYPE_HEADER;\n    },\n    RSC_HEADER: function() {\n        return RSC_HEADER;\n    }\n});\nconst RSC_HEADER = 'RSC';\nconst ACTION_HEADER = 'Next-Action';\nconst NEXT_ROUTER_STATE_TREE_HEADER = 'Next-Router-State-Tree';\nconst NEXT_ROUTER_PREFETCH_HEADER = 'Next-Router-Prefetch';\nconst NEXT_ROUTER_SEGMENT_PREFETCH_HEADER = 'Next-Router-Segment-Prefetch';\nconst NEXT_HMR_REFRESH_HEADER = 'Next-HMR-Refresh';\nconst NEXT_HMR_REFRESH_HASH_COOKIE = '__next_hmr_refresh_hash__';\nconst NEXT_URL = 'Next-Url';\nconst RSC_CONTENT_TYPE_HEADER = 'text/x-component';\nconst FLIGHT_HEADERS = [\n    RSC_HEADER,\n    NEXT_ROUTER_STATE_TREE_HEADER,\n    NEXT_ROUTER_PREFETCH_HEADER,\n    NEXT_HMR_REFRESH_HEADER,\n    NEXT_ROUTER_SEGMENT_PREFETCH_HEADER\n];\nconst NEXT_RSC_UNION_QUERY = '_rsc';\nconst NEXT_ROUTER_STALE_TIME_HEADER = 'x-nextjs-stale-time';\nconst NEXT_DID_POSTPONE_HEADER = 'x-nextjs-postponed';\nconst NEXT_REWRITTEN_PATH_HEADER = 'x-nextjs-rewritten-path';\nconst NEXT_REWRITTEN_QUERY_HEADER = 'x-nextjs-rewritten-query';\nconst NEXT_IS_PRERENDER_HEADER = 'x-nextjs-prerender';\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router-headers.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(shared)/../../node_modules/next/dist/client/components/app-router-headers.js\n"));

/***/ }),

/***/ "(shared)/../../node_modules/next/dist/server/app-render/async-local-storage.js":
/*!*****************************************************************************!*\
  !*** ../../node_modules/next/dist/server/app-render/async-local-storage.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    bindSnapshot: function() {\n        return bindSnapshot;\n    },\n    createAsyncLocalStorage: function() {\n        return createAsyncLocalStorage;\n    },\n    createSnapshot: function() {\n        return createSnapshot;\n    }\n});\nconst sharedAsyncLocalStorageNotAvailableError = Object.defineProperty(new Error('Invariant: AsyncLocalStorage accessed in runtime where it is not available'), \"__NEXT_ERROR_CODE\", {\n    value: \"E504\",\n    enumerable: false,\n    configurable: true\n});\nclass FakeAsyncLocalStorage {\n    disable() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    getStore() {\n        // This fake implementation of AsyncLocalStorage always returns `undefined`.\n        return undefined;\n    }\n    run() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    exit() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    enterWith() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    static bind(fn) {\n        return fn;\n    }\n}\nconst maybeGlobalAsyncLocalStorage = typeof globalThis !== 'undefined' && globalThis.AsyncLocalStorage;\nfunction createAsyncLocalStorage() {\n    if (maybeGlobalAsyncLocalStorage) {\n        return new maybeGlobalAsyncLocalStorage();\n    }\n    return new FakeAsyncLocalStorage();\n}\nfunction bindSnapshot(fn) {\n    if (maybeGlobalAsyncLocalStorage) {\n        return maybeGlobalAsyncLocalStorage.bind(fn);\n    }\n    return FakeAsyncLocalStorage.bind(fn);\n}\nfunction createSnapshot() {\n    if (maybeGlobalAsyncLocalStorage) {\n        return maybeGlobalAsyncLocalStorage.snapshot();\n    }\n    return function(fn, ...args) {\n        return fn(...args);\n    };\n}\n\n//# sourceMappingURL=async-local-storage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNoYXJlZCkvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9hc3luYy1sb2NhbC1zdG9yYWdlLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YsTUFBTSxDQUlMO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEp1YW4gUHVsZ2FyaW5cXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcY2hpYS1uZXh0XFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXHNlcnZlclxcYXBwLXJlbmRlclxcYXN5bmMtbG9jYWwtc3RvcmFnZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbjAgJiYgKG1vZHVsZS5leHBvcnRzID0ge1xuICAgIGJpbmRTbmFwc2hvdDogbnVsbCxcbiAgICBjcmVhdGVBc3luY0xvY2FsU3RvcmFnZTogbnVsbCxcbiAgICBjcmVhdGVTbmFwc2hvdDogbnVsbFxufSk7XG5mdW5jdGlvbiBfZXhwb3J0KHRhcmdldCwgYWxsKSB7XG4gICAgZm9yKHZhciBuYW1lIGluIGFsbClPYmplY3QuZGVmaW5lUHJvcGVydHkodGFyZ2V0LCBuYW1lLCB7XG4gICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgIGdldDogYWxsW25hbWVdXG4gICAgfSk7XG59XG5fZXhwb3J0KGV4cG9ydHMsIHtcbiAgICBiaW5kU25hcHNob3Q6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gYmluZFNuYXBzaG90O1xuICAgIH0sXG4gICAgY3JlYXRlQXN5bmNMb2NhbFN0b3JhZ2U6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gY3JlYXRlQXN5bmNMb2NhbFN0b3JhZ2U7XG4gICAgfSxcbiAgICBjcmVhdGVTbmFwc2hvdDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBjcmVhdGVTbmFwc2hvdDtcbiAgICB9XG59KTtcbmNvbnN0IHNoYXJlZEFzeW5jTG9jYWxTdG9yYWdlTm90QXZhaWxhYmxlRXJyb3IgPSBPYmplY3QuZGVmaW5lUHJvcGVydHkobmV3IEVycm9yKCdJbnZhcmlhbnQ6IEFzeW5jTG9jYWxTdG9yYWdlIGFjY2Vzc2VkIGluIHJ1bnRpbWUgd2hlcmUgaXQgaXMgbm90IGF2YWlsYWJsZScpLCBcIl9fTkVYVF9FUlJPUl9DT0RFXCIsIHtcbiAgICB2YWx1ZTogXCJFNTA0XCIsXG4gICAgZW51bWVyYWJsZTogZmFsc2UsXG4gICAgY29uZmlndXJhYmxlOiB0cnVlXG59KTtcbmNsYXNzIEZha2VBc3luY0xvY2FsU3RvcmFnZSB7XG4gICAgZGlzYWJsZSgpIHtcbiAgICAgICAgdGhyb3cgc2hhcmVkQXN5bmNMb2NhbFN0b3JhZ2VOb3RBdmFpbGFibGVFcnJvcjtcbiAgICB9XG4gICAgZ2V0U3RvcmUoKSB7XG4gICAgICAgIC8vIFRoaXMgZmFrZSBpbXBsZW1lbnRhdGlvbiBvZiBBc3luY0xvY2FsU3RvcmFnZSBhbHdheXMgcmV0dXJucyBgdW5kZWZpbmVkYC5cbiAgICAgICAgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICB9XG4gICAgcnVuKCkge1xuICAgICAgICB0aHJvdyBzaGFyZWRBc3luY0xvY2FsU3RvcmFnZU5vdEF2YWlsYWJsZUVycm9yO1xuICAgIH1cbiAgICBleGl0KCkge1xuICAgICAgICB0aHJvdyBzaGFyZWRBc3luY0xvY2FsU3RvcmFnZU5vdEF2YWlsYWJsZUVycm9yO1xuICAgIH1cbiAgICBlbnRlcldpdGgoKSB7XG4gICAgICAgIHRocm93IHNoYXJlZEFzeW5jTG9jYWxTdG9yYWdlTm90QXZhaWxhYmxlRXJyb3I7XG4gICAgfVxuICAgIHN0YXRpYyBiaW5kKGZuKSB7XG4gICAgICAgIHJldHVybiBmbjtcbiAgICB9XG59XG5jb25zdCBtYXliZUdsb2JhbEFzeW5jTG9jYWxTdG9yYWdlID0gdHlwZW9mIGdsb2JhbFRoaXMgIT09ICd1bmRlZmluZWQnICYmIGdsb2JhbFRoaXMuQXN5bmNMb2NhbFN0b3JhZ2U7XG5mdW5jdGlvbiBjcmVhdGVBc3luY0xvY2FsU3RvcmFnZSgpIHtcbiAgICBpZiAobWF5YmVHbG9iYWxBc3luY0xvY2FsU3RvcmFnZSkge1xuICAgICAgICByZXR1cm4gbmV3IG1heWJlR2xvYmFsQXN5bmNMb2NhbFN0b3JhZ2UoKTtcbiAgICB9XG4gICAgcmV0dXJuIG5ldyBGYWtlQXN5bmNMb2NhbFN0b3JhZ2UoKTtcbn1cbmZ1bmN0aW9uIGJpbmRTbmFwc2hvdChmbikge1xuICAgIGlmIChtYXliZUdsb2JhbEFzeW5jTG9jYWxTdG9yYWdlKSB7XG4gICAgICAgIHJldHVybiBtYXliZUdsb2JhbEFzeW5jTG9jYWxTdG9yYWdlLmJpbmQoZm4pO1xuICAgIH1cbiAgICByZXR1cm4gRmFrZUFzeW5jTG9jYWxTdG9yYWdlLmJpbmQoZm4pO1xufVxuZnVuY3Rpb24gY3JlYXRlU25hcHNob3QoKSB7XG4gICAgaWYgKG1heWJlR2xvYmFsQXN5bmNMb2NhbFN0b3JhZ2UpIHtcbiAgICAgICAgcmV0dXJuIG1heWJlR2xvYmFsQXN5bmNMb2NhbFN0b3JhZ2Uuc25hcHNob3QoKTtcbiAgICB9XG4gICAgcmV0dXJuIGZ1bmN0aW9uKGZuLCAuLi5hcmdzKSB7XG4gICAgICAgIHJldHVybiBmbiguLi5hcmdzKTtcbiAgICB9O1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hc3luYy1sb2NhbC1zdG9yYWdlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(shared)/../../node_modules/next/dist/server/app-render/async-local-storage.js\n"));

/***/ }),

/***/ "(shared)/../../node_modules/next/dist/server/app-render/work-async-storage-instance.js":
/*!*************************************************************************************!*\
  !*** ../../node_modules/next/dist/server/app-render/work-async-storage-instance.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"workAsyncStorageInstance\", ({\n    enumerable: true,\n    get: function() {\n        return workAsyncStorageInstance;\n    }\n}));\nconst _asynclocalstorage = __webpack_require__(/*! ./async-local-storage */ \"(shared)/../../node_modules/next/dist/server/app-render/async-local-storage.js\");\nconst workAsyncStorageInstance = (0, _asynclocalstorage.createAsyncLocalStorage)();\n\n//# sourceMappingURL=work-async-storage-instance.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNoYXJlZCkvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci93b3JrLWFzeW5jLXN0b3JhZ2UtaW5zdGFuY2UuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRiw0REFBMkQ7QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRiwyQkFBMkIsbUJBQU8sQ0FBQyw2R0FBdUI7QUFDMUQ7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcSnVhbiBQdWxnYXJpblxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxjaGlhLW5leHRcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcc2VydmVyXFxhcHAtcmVuZGVyXFx3b3JrLWFzeW5jLXN0b3JhZ2UtaW5zdGFuY2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJ3b3JrQXN5bmNTdG9yYWdlSW5zdGFuY2VcIiwge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIHdvcmtBc3luY1N0b3JhZ2VJbnN0YW5jZTtcbiAgICB9XG59KTtcbmNvbnN0IF9hc3luY2xvY2Fsc3RvcmFnZSA9IHJlcXVpcmUoXCIuL2FzeW5jLWxvY2FsLXN0b3JhZ2VcIik7XG5jb25zdCB3b3JrQXN5bmNTdG9yYWdlSW5zdGFuY2UgPSAoMCwgX2FzeW5jbG9jYWxzdG9yYWdlLmNyZWF0ZUFzeW5jTG9jYWxTdG9yYWdlKSgpO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD13b3JrLWFzeW5jLXN0b3JhZ2UtaW5zdGFuY2UuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(shared)/../../node_modules/next/dist/server/app-render/work-async-storage-instance.js\n"));

/***/ }),

/***/ "(shared)/../../node_modules/next/dist/server/app-render/work-async-storage.external.js":
/*!*************************************************************************************!*\
  !*** ../../node_modules/next/dist/server/app-render/work-async-storage.external.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"workAsyncStorage\", ({\n    enumerable: true,\n    get: function() {\n        return _workasyncstorageinstance.workAsyncStorageInstance;\n    }\n}));\nconst _workasyncstorageinstance = __webpack_require__(/*! ./work-async-storage-instance */ \"(shared)/../../node_modules/next/dist/server/app-render/work-async-storage-instance.js\");\n\n//# sourceMappingURL=work-async-storage.external.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNoYXJlZCkvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci93b3JrLWFzeW5jLXN0b3JhZ2UuZXh0ZXJuYWwuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRixvREFBbUQ7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRixrQ0FBa0MsbUJBQU8sQ0FBQyw2SEFBK0I7O0FBRXpFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEp1YW4gUHVsZ2FyaW5cXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcY2hpYS1uZXh0XFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXHNlcnZlclxcYXBwLXJlbmRlclxcd29yay1hc3luYy1zdG9yYWdlLmV4dGVybmFsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwid29ya0FzeW5jU3RvcmFnZVwiLCB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gX3dvcmthc3luY3N0b3JhZ2VpbnN0YW5jZS53b3JrQXN5bmNTdG9yYWdlSW5zdGFuY2U7XG4gICAgfVxufSk7XG5jb25zdCBfd29ya2FzeW5jc3RvcmFnZWluc3RhbmNlID0gcmVxdWlyZShcIi4vd29yay1hc3luYy1zdG9yYWdlLWluc3RhbmNlXCIpO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD13b3JrLWFzeW5jLXN0b3JhZ2UuZXh0ZXJuYWwuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(shared)/../../node_modules/next/dist/server/app-render/work-async-storage.external.js\n"));

/***/ }),

/***/ "(shared)/../../node_modules/next/dist/server/app-render/work-unit-async-storage-instance.js":
/*!******************************************************************************************!*\
  !*** ../../node_modules/next/dist/server/app-render/work-unit-async-storage-instance.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"workUnitAsyncStorageInstance\", ({\n    enumerable: true,\n    get: function() {\n        return workUnitAsyncStorageInstance;\n    }\n}));\nconst _asynclocalstorage = __webpack_require__(/*! ./async-local-storage */ \"(shared)/../../node_modules/next/dist/server/app-render/async-local-storage.js\");\nconst workUnitAsyncStorageInstance = (0, _asynclocalstorage.createAsyncLocalStorage)();\n\n//# sourceMappingURL=work-unit-async-storage-instance.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNoYXJlZCkvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci93b3JrLXVuaXQtYXN5bmMtc3RvcmFnZS1pbnN0YW5jZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLGdFQUErRDtBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGLDJCQUEyQixtQkFBTyxDQUFDLDZHQUF1QjtBQUMxRDs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxKdWFuIFB1bGdhcmluXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXGNoaWEtbmV4dFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxzZXJ2ZXJcXGFwcC1yZW5kZXJcXHdvcmstdW5pdC1hc3luYy1zdG9yYWdlLWluc3RhbmNlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwid29ya1VuaXRBc3luY1N0b3JhZ2VJbnN0YW5jZVwiLCB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gd29ya1VuaXRBc3luY1N0b3JhZ2VJbnN0YW5jZTtcbiAgICB9XG59KTtcbmNvbnN0IF9hc3luY2xvY2Fsc3RvcmFnZSA9IHJlcXVpcmUoXCIuL2FzeW5jLWxvY2FsLXN0b3JhZ2VcIik7XG5jb25zdCB3b3JrVW5pdEFzeW5jU3RvcmFnZUluc3RhbmNlID0gKDAsIF9hc3luY2xvY2Fsc3RvcmFnZS5jcmVhdGVBc3luY0xvY2FsU3RvcmFnZSkoKTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9d29yay11bml0LWFzeW5jLXN0b3JhZ2UtaW5zdGFuY2UuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(shared)/../../node_modules/next/dist/server/app-render/work-unit-async-storage-instance.js\n"));

/***/ }),

/***/ "(shared)/../../node_modules/next/dist/server/app-render/work-unit-async-storage.external.js":
/*!******************************************************************************************!*\
  !*** ../../node_modules/next/dist/server/app-render/work-unit-async-storage.external.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getDraftModeProviderForCacheScope: function() {\n        return getDraftModeProviderForCacheScope;\n    },\n    getExpectedRequestStore: function() {\n        return getExpectedRequestStore;\n    },\n    getHmrRefreshHash: function() {\n        return getHmrRefreshHash;\n    },\n    getPrerenderResumeDataCache: function() {\n        return getPrerenderResumeDataCache;\n    },\n    getRenderResumeDataCache: function() {\n        return getRenderResumeDataCache;\n    },\n    throwForMissingRequestStore: function() {\n        return throwForMissingRequestStore;\n    },\n    workUnitAsyncStorage: function() {\n        return _workunitasyncstorageinstance.workUnitAsyncStorageInstance;\n    }\n});\nconst _workunitasyncstorageinstance = __webpack_require__(/*! ./work-unit-async-storage-instance */ \"(shared)/../../node_modules/next/dist/server/app-render/work-unit-async-storage-instance.js\");\nconst _approuterheaders = __webpack_require__(/*! ../../client/components/app-router-headers */ \"(shared)/../../node_modules/next/dist/client/components/app-router-headers.js\");\nfunction getExpectedRequestStore(callingExpression) {\n    const workUnitStore = _workunitasyncstorageinstance.workUnitAsyncStorageInstance.getStore();\n    if (!workUnitStore) {\n        throwForMissingRequestStore(callingExpression);\n    }\n    switch(workUnitStore.type){\n        case 'request':\n            return workUnitStore;\n        case 'prerender':\n        case 'prerender-ppr':\n        case 'prerender-legacy':\n            // This should not happen because we should have checked it already.\n            throw Object.defineProperty(new Error(`\\`${callingExpression}\\` cannot be called inside a prerender. This is a bug in Next.js.`), \"__NEXT_ERROR_CODE\", {\n                value: \"E401\",\n                enumerable: false,\n                configurable: true\n            });\n        case 'cache':\n            throw Object.defineProperty(new Error(`\\`${callingExpression}\\` cannot be called inside \"use cache\". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`), \"__NEXT_ERROR_CODE\", {\n                value: \"E37\",\n                enumerable: false,\n                configurable: true\n            });\n        case 'unstable-cache':\n            throw Object.defineProperty(new Error(`\\`${callingExpression}\\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`), \"__NEXT_ERROR_CODE\", {\n                value: \"E69\",\n                enumerable: false,\n                configurable: true\n            });\n        default:\n            const _exhaustiveCheck = workUnitStore;\n            return _exhaustiveCheck;\n    }\n}\nfunction throwForMissingRequestStore(callingExpression) {\n    throw Object.defineProperty(new Error(`\\`${callingExpression}\\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`), \"__NEXT_ERROR_CODE\", {\n        value: \"E251\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction getPrerenderResumeDataCache(workUnitStore) {\n    if (workUnitStore.type === 'prerender' || workUnitStore.type === 'prerender-ppr') {\n        return workUnitStore.prerenderResumeDataCache;\n    }\n    return null;\n}\nfunction getRenderResumeDataCache(workUnitStore) {\n    if (workUnitStore.type !== 'prerender-legacy' && workUnitStore.type !== 'cache' && workUnitStore.type !== 'unstable-cache') {\n        if (workUnitStore.type === 'request') {\n            return workUnitStore.renderResumeDataCache;\n        }\n        // We return the mutable resume data cache here as an immutable version of\n        // the cache as it can also be used for reading.\n        return workUnitStore.prerenderResumeDataCache;\n    }\n    return null;\n}\nfunction getHmrRefreshHash(workStore, workUnitStore) {\n    var _workUnitStore_cookies_get;\n    if (!workStore.dev) {\n        return undefined;\n    }\n    return workUnitStore.type === 'cache' || workUnitStore.type === 'prerender' ? workUnitStore.hmrRefreshHash : workUnitStore.type === 'request' ? (_workUnitStore_cookies_get = workUnitStore.cookies.get(_approuterheaders.NEXT_HMR_REFRESH_HASH_COOKIE)) == null ? void 0 : _workUnitStore_cookies_get.value : undefined;\n}\nfunction getDraftModeProviderForCacheScope(workStore, workUnitStore) {\n    if (workStore.isDraftMode) {\n        switch(workUnitStore.type){\n            case 'cache':\n            case 'unstable-cache':\n            case 'request':\n                return workUnitStore.draftMode;\n            default:\n                return undefined;\n        }\n    }\n    return undefined;\n}\n\n//# sourceMappingURL=work-unit-async-storage.external.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(shared)/../../node_modules/next/dist/server/app-render/work-unit-async-storage.external.js\n"));

/***/ })

}]);