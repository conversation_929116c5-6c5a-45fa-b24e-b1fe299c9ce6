(()=>{var e={};e.id=772,e.ids=[772],e.modules={135:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>h,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>p});var n=t(48106),i=t(48819),o=t(12050),a=t(4235),u=t(24768);async function p(){try{let e=(0,u.y8)(),{data:r,error:t}=await e.from("ciudadanos").select("count").limit(1);if(t)throw Error(`Database connection failed: ${t.message}`);let s=["NEXT_PUBLIC_SUPABASE_URL","NEXT_PUBLIC_SUPABASE_ANON_KEY","SUPABASE_SERVICE_ROLE_KEY"].filter(e=>!process.env[e]);if(s.length>0)throw Error(`Missing environment variables: ${s.join(", ")}`);return a.NextResponse.json({status:"healthy",timestamp:new Date().toISOString(),version:process.env.npm_package_version||"1.0.0",environment:"production",database:"connected",services:{supabase:"operational",auth:"operational"}})}catch(e){return console.error("Health check failed:",e),a.NextResponse.json({status:"unhealthy",timestamp:new Date().toISOString(),error:e instanceof Error?e.message:"Unknown error",environment:"production"},{status:503})}}let c=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/health/route",pathname:"/api/health",filename:"route",bundlePath:"app/api/health/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\api\\health\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:h}=c;function x(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},1241:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},24768:(e,r,t)=>{"use strict";t.d(r,{y8:()=>a});var s=t(2492),n=t(62518);let i=process.env.NEXT_PUBLIC_SUPABASE_URL,o=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;if(!i||!o)throw Error("Missing Supabase environment variables");let a=async()=>{let{cookies:e}=await t.e(208).then(t.bind(t,65208)),r=e();return(0,n.createServerComponentClient)({cookies:()=>r})};(0,s.UU)(i,o)},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72088:()=>{},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80408:()=>{},81630:e=>{"use strict";e.exports=require("http")},87032:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[191,111,744,518],()=>t(135));module.exports=s})();