(()=>{var e={};e.id=772,e.ids=[772],e.modules={135:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>p});var n=t(8106),o=t(8819),i=t(2050),a=t(4235),u=t(4768);async function p(){try{let e=(0,u.y8)(),{data:r,error:t}=await e.from("ciudadanos").select("count").limit(1);if(t)throw Error(`Database connection failed: ${t.message}`);let s=["NEXT_PUBLIC_SUPABASE_URL","NEXT_PUBLIC_SUPABASE_ANON_KEY","SUPABASE_SERVICE_ROLE_KEY"].filter(e=>!process.env[e]);if(s.length>0)throw Error(`Missing environment variables: ${s.join(", ")}`);return a.NextResponse.json({status:"healthy",timestamp:new Date().toISOString(),version:process.env.npm_package_version||"1.0.0",environment:"production",database:"connected",services:{supabase:"operational",auth:"operational"}})}catch(e){return console.error("Health check failed:",e),a.NextResponse.json({status:"unhealthy",timestamp:new Date().toISOString(),error:e instanceof Error?e.message:"Unknown error",environment:"production"},{status:503})}}let c=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/health/route",pathname:"/api/health",filename:"route",bundlePath:"app/api/health/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\api\\health\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:x}=c;function h(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},408:()=>{},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1241:()=>{},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},2088:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4768:(e,r,t)=>{"use strict";t.d(r,{y8:()=>u});var s=t(2492),n=t(2518),o=t(5208);let i=process.env.NEXT_PUBLIC_SUPABASE_URL,a=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;if(!i||!a)throw Error("Missing Supabase environment variables");let u=()=>{let e=(0,o.UL)();return(0,n.createServerComponentClient)({cookies:()=>e})};(0,s.UU)(i,a)},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},7032:()=>{},7910:e=>{"use strict";e.exports=require("stream")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[363,525,744],()=>t(135));module.exports=s})();