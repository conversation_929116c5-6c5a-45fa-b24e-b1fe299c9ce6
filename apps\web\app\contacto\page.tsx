import { Metadata } from 'next';
import PageLayout from '@/components/layout/PageLayout';
import {
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  ClockIcon,
  ChatBubbleLeftRightIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

export const metadata: Metadata = {
  title: 'Contacto | Portal CHIA',
  description: 'Contacta con la Alcaldía de Chía. Encuentra información de contacto, horarios de atención, ubicación y canales de comunicación.',
  keywords: 'contacto, alcaldía Chía, teléfono, dirección, horarios atención',
};

const contactChannels = [
  {
    id: 'phone',
    title: 'Teléfono Principal',
    description: 'Línea de atención ciudadana',
    value: '(*************',
    icon: PhoneIcon,
    available: '24/7',
    action: 'tel:+576011234567'
  },
  {
    id: 'whatsapp',
    title: 'WhatsApp',
    description: 'Chat directo con funcionarios',
    value: '+57 ************',
    icon: ChatBubbleLeftRightIcon,
    available: 'Lun-Vie 8AM-5PM',
    action: 'https://wa.me/573001234567'
  },
  {
    id: 'email',
    title: 'Correo Electrónico',
    description: 'Consultas y solicitudes generales',
    value: '<EMAIL>',
    icon: EnvelopeIcon,
    available: 'Respuesta en 24-48h',
    action: 'mailto:<EMAIL>'
  }
];

const emergencyContacts = [
  { name: 'Bomberos', phone: '119', description: 'Emergencias de incendio y rescate' },
  { name: 'Policía Nacional', phone: '123', description: 'Seguridad y orden público' },
  { name: 'Cruz Roja', phone: '132', description: 'Emergencias médicas' },
  { name: 'Defensa Civil', phone: '144', description: 'Desastres naturales' }
];

const departments = [
  { name: 'Secretaría General', email: '<EMAIL>', phone: 'Ext. 101' },
  { name: 'Hacienda Municipal', email: '<EMAIL>', phone: 'Ext. 102' },
  { name: 'Planeación Municipal', email: '<EMAIL>', phone: 'Ext. 103' },
  { name: 'Obras Públicas', email: '<EMAIL>', phone: 'Ext. 104' },
  { name: 'Desarrollo Social', email: '<EMAIL>', phone: 'Ext. 105' }
];

export default function ContactoPage() {
  const currentTime = new Date().toLocaleTimeString('es-CO', {
    timeZone: 'America/Bogota',
    hour: '2-digit',
    minute: '2-digit'
  });

  const isOfficeOpen = () => {
    const now = new Date();
    const hour = now.getHours();
    const day = now.getDay();
    
    // Monday to Friday 8AM-5PM, Saturday 8AM-12PM
    if (day >= 1 && day <= 5) {
      return hour >= 8 && hour < 17;
    } else if (day === 6) {
      return hour >= 8 && hour < 12;
    }
    return false;
  };

  return (
    <PageLayout className="bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">
              Contacto
            </h1>
            <p className="text-xl text-primary-100 mb-8 max-w-3xl mx-auto">
              Estamos aquí para ayudarte. Encuentra toda la información de contacto 
              de la Alcaldía de Chía y nuestros canales de atención.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Contact Channels */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {contactChannels.map((channel) => (
            <a
              key={channel.id}
              href={channel.action}
              className="bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow p-6 text-center group"
            >
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-200 transition-colors">
                <channel.icon className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {channel.title}
              </h3>
              <p className="text-gray-600 mb-3">
                {channel.description}
              </p>
              <p className="text-lg font-medium text-primary-600 mb-2">
                {channel.value}
              </p>
              <p className="text-sm text-gray-500">
                {channel.available}
              </p>
            </a>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Office Hours & Location */}
          <div className="space-y-8">
            {/* Office Hours */}
            <div className="bg-white rounded-xl shadow-md p-6">
              <div className="flex items-center gap-3 mb-6">
                <ClockIcon className="h-6 w-6 text-primary-600" />
                <h2 className="text-2xl font-bold text-gray-900">
                  Horarios de Atención
                </h2>
              </div>
              
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="font-medium text-gray-900">Lunes a Viernes</span>
                  <span className="text-gray-600">8:00 AM - 5:00 PM</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="font-medium text-gray-900">Sábados</span>
                  <span className="text-gray-600">8:00 AM - 12:00 PM</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="font-medium text-gray-900">Domingos y Festivos</span>
                  <span className="text-red-600">Cerrado</span>
                </div>
              </div>

              <div className="mt-6 p-4 bg-primary-50 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <div className={`w-3 h-3 rounded-full ${isOfficeOpen() ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className="font-medium text-gray-900">
                    {isOfficeOpen() ? 'Abierto ahora' : 'Cerrado ahora'}
                  </span>
                </div>
                <p className="text-sm text-gray-600">
                  Hora actual: {currentTime} (Hora de Colombia)
                </p>
              </div>
            </div>

            {/* Location */}
            <div className="bg-white rounded-xl shadow-md p-6">
              <div className="flex items-center gap-3 mb-6">
                <MapPinIcon className="h-6 w-6 text-primary-600" />
                <h2 className="text-2xl font-bold text-gray-900">
                  Ubicación
                </h2>
              </div>
              
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Alcaldía Municipal de Chía</h3>
                  <p className="text-gray-600">
                    Carrera 11 No. 17-25<br />
                    Chía, Cundinamarca<br />
                    Colombia
                  </p>
                </div>
                
                <a
                  href="https://maps.google.com/?q=Alcaldía+Chía+Cundinamarca"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-2 text-primary-600 hover:text-primary-700 font-medium"
                >
                  Ver en Google Maps
                  <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
              </div>
            </div>
          </div>

          {/* Departments & Emergency */}
          <div className="space-y-8">
            {/* Departments */}
            <div className="bg-white rounded-xl shadow-md p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                Dependencias
              </h2>
              
              <div className="space-y-4">
                {departments.map((dept, index) => (
                  <div key={index} className="border-b border-gray-200 pb-4 last:border-b-0">
                    <h3 className="font-semibold text-gray-900 mb-1">
                      {dept.name}
                    </h3>
                    <div className="text-sm text-gray-600 space-y-1">
                      <p>📧 {dept.email}</p>
                      <p>📞 {dept.phone}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Emergency Contacts */}
            <div className="bg-red-50 rounded-xl shadow-md p-6 border border-red-200">
              <div className="flex items-center gap-3 mb-6">
                <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
                <h2 className="text-2xl font-bold text-red-900">
                  Contactos de Emergencia
                </h2>
              </div>
              
              <div className="space-y-3">
                {emergencyContacts.map((contact, index) => (
                  <div key={index} className="flex justify-between items-center p-3 bg-white rounded-lg">
                    <div>
                      <h3 className="font-semibold text-gray-900">{contact.name}</h3>
                      <p className="text-sm text-gray-600">{contact.description}</p>
                    </div>
                    <a
                      href={`tel:${contact.phone}`}
                      className="text-xl font-bold text-red-600 hover:text-red-700"
                    >
                      {contact.phone}
                    </a>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Contact Form CTA */}
        <div className="mt-12 bg-white rounded-xl shadow-md p-8 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            ¿Tienes una consulta específica?
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Nuestro asistente de IA puede ayudarte de inmediato, o puedes contactarnos 
            directamente a través de nuestros canales oficiales.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/chat"
              className="inline-flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
            >
              Hablar con el Asistente IA
            </a>
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center gap-2 bg-white hover:bg-gray-50 text-primary-600 border border-primary-600 px-6 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
            >
              Enviar Email
            </a>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
