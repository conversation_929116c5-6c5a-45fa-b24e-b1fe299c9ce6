(()=>{var e={};e.id=202,e.ids=[202],e.modules={1241:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56734:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>d,serverHooks:()=>f,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>u});var i=r(48106),a=r(48819),n=r(12050),o=r(4235),c=r(73474);async function p(e){try{let t=(0,c.y8)(),{searchParams:r}=new URL(e.url),s=r.get("q")||r.get("query"),i=parseInt(r.get("limit")||"20"),a=parseInt(r.get("offset")||"0"),n=r.get("tipo");if(!s||0===s.trim().length)return o.NextResponse.json({error:"Search query is required"},{status:400});let{data:p,error:u}=await t.schema("ingestion").rpc("buscar_contenido",{p_query:s.trim(),p_limite:i,p_offset:a});if(u)return console.error("Error in unified search:",u),o.NextResponse.json({error:"Search failed",details:u.message},{status:500});let d=p||[];n&&(d=d.filter(e=>e.tipo===n));let l=d.map(e=>({id:e.id,tipo:e.tipo,titulo:e.titulo,contenido:e.contenido,dependencia:e.dependencia,subdependencia:e.subdependencia,relevancia:e.relevancia,url:function(e,t){switch(e){case"faq":return`/faqs/${t}`;case"tramite":return`/servicios/tramite/${t}`;case"opa":return`/procedimientos/${t}`;default:return"#"}}(e.tipo,e.id),categoria:function(e){switch(e){case"faq":return"Preguntas Frecuentes";case"tramite":return"Tr\xe1mites y Servicios";case"opa":return"Procedimientos Administrativos";default:return"General"}}(e.tipo)})),m={faqs:l.filter(e=>"faq"===e.tipo),tramites:l.filter(e=>"tramite"===e.tipo),opas:l.filter(e=>"opa"===e.tipo)};return o.NextResponse.json({success:!0,query:s,data:l,grouped:m,pagination:{limit:i,offset:a,total:l.length},stats:{totalResults:l.length,faqCount:m.faqs.length,tramiteCount:m.tramites.length,opaCount:m.opas.length}})}catch(e){return console.error("Unexpected error in search API:",e),o.NextResponse.json({error:"Internal server error",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function u(e){try{let{query:t,filters:r={},limit:s=20,offset:i=0}=await e.json();if(!t||0===t.trim().length)return o.NextResponse.json({error:"Search query is required"},{status:400});let a=(0,c.y8)(),n=[];if(!r.tipo||"faq"===r.tipo){let e=a.schema("ingestion").from("faqs").select(`
          id,
          tema,
          pregunta,
          respuesta,
          dependencias:dependencia_id (nombre),
          subdependencias:subdependencia_id (nombre)
        `).eq("activo",!0).textSearch("vector_busqueda",t,{config:"spanish"});r.dependencia&&(e=e.eq("dependencia_id",r.dependencia)),n.push(e.then(({data:e,error:t})=>({type:"faq",data:e?.map(e=>({...e,tipo:"faq",titulo:e.pregunta,contenido:e.respuesta}))||[],error:t})))}if(!r.tipo||"tramite"===r.tipo){let e=a.schema("ingestion").from("tramites").select(`
          id,
          nombre,
          descripcion,
          categoria,
          dependencias:dependencia_id (nombre),
          subdependencias:subdependencia_id (nombre)
        `).eq("activo",!0).textSearch("vector_busqueda",t,{config:"spanish"});r.dependencia&&(e=e.eq("dependencia_id",r.dependencia)),r.categoria&&(e=e.eq("categoria",r.categoria)),n.push(e.then(({data:e,error:t})=>({type:"tramite",data:e?.map(e=>({...e,tipo:"tramite",titulo:e.nombre,contenido:e.descripcion}))||[],error:t})))}let p=await Promise.all(n),u=p.filter(e=>e.error);if(u.length>0)return console.error("Search errors:",u),o.NextResponse.json({error:"Search failed",details:u.map(e=>e.error?.message)},{status:500});let d=p.flatMap(e=>e.data),l=d.slice(i,i+s);return o.NextResponse.json({success:!0,query:t,filters:r,data:l,pagination:{limit:s,offset:i,total:d.length}})}catch(e){return console.error("Error in advanced search:",e),o.NextResponse.json({error:"Search failed"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/search/route",pathname:"/api/search",filename:"route",bundlePath:"app/api/search/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\api\\search\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:f}=d;function h(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72088:()=>{},73474:(e,t,r)=>{"use strict";r.d(t,{y8:()=>n});var s=r(2492);let i=process.env.NEXT_PUBLIC_SUPABASE_URL,a=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;if(process.env.SUPABASE_SERVICE_ROLE_KEY,!i||!a)throw Error("Missing Supabase environment variables");let n=()=>(0,s.UU)(i,a);n()},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80408:()=>{},81630:e=>{"use strict";e.exports=require("http")},87032:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[191,111,744],()=>r(56734));module.exports=s})();