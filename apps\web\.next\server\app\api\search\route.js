/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/search/route";
exports.ids = ["app/api/search/route"];
exports.modules = {

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsearch%2Froute&page=%2Fapi%2Fsearch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsearch%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsearch%2Froute&page=%2Fapi%2Fsearch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsearch%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/../../node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/../../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Juan_Pulgarin_Documents_augment_projects_chia_next_apps_web_app_api_search_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/search/route.ts */ \"(rsc)/./app/api/search/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/search/route\",\n        pathname: \"/api/search\",\n        filename: \"route\",\n        bundlePath: \"app/api/search/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\api\\\\search\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Juan_Pulgarin_Documents_augment_projects_chia_next_apps_web_app_api_search_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsearch%2Froute&page=%2Fapi%2Fsearch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsearch%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!**********************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \**********************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./app/api/search/route.ts":
/*!*********************************!*\
  !*** ./app/api/search/route.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/../../node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase-server */ \"(rsc)/./lib/supabase-server.ts\");\n\n\nasync function GET(request) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabase)();\n        const { searchParams } = new URL(request.url);\n        // Get query parameters\n        const query = searchParams.get('q') || searchParams.get('query');\n        const limit = parseInt(searchParams.get('limit') || '20');\n        const offset = parseInt(searchParams.get('offset') || '0');\n        const tipo = searchParams.get('tipo'); // 'faq', 'tramite', 'opa'\n        if (!query || query.trim().length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Search query is required'\n            }, {\n                status: 400\n            });\n        }\n        // Use the database function for unified search\n        const { data: searchResults, error } = await supabase.schema('ingestion').rpc('buscar_contenido', {\n            p_query: query.trim(),\n            p_limite: limit,\n            p_offset: offset\n        });\n        if (error) {\n            console.error('Error in unified search:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Search failed',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        // Filter by type if specified\n        let filteredResults = searchResults || [];\n        if (tipo) {\n            filteredResults = filteredResults.filter((result)=>result.tipo === tipo);\n        }\n        // Transform results for frontend\n        const transformedResults = filteredResults.map((result)=>({\n                id: result.id,\n                tipo: result.tipo,\n                titulo: result.titulo,\n                contenido: result.contenido,\n                dependencia: result.dependencia,\n                subdependencia: result.subdependencia,\n                relevancia: result.relevancia,\n                // Add type-specific URLs or actions\n                url: getResultUrl(result.tipo, result.id),\n                categoria: getResultCategory(result.tipo)\n            }));\n        // Group results by type for better presentation\n        const groupedResults = {\n            faqs: transformedResults.filter((r)=>r.tipo === 'faq'),\n            tramites: transformedResults.filter((r)=>r.tipo === 'tramite'),\n            opas: transformedResults.filter((r)=>r.tipo === 'opa')\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            query: query,\n            data: transformedResults,\n            grouped: groupedResults,\n            pagination: {\n                limit,\n                offset,\n                total: transformedResults.length\n            },\n            stats: {\n                totalResults: transformedResults.length,\n                faqCount: groupedResults.faqs.length,\n                tramiteCount: groupedResults.tramites.length,\n                opaCount: groupedResults.opas.length\n            }\n        });\n    } catch (error) {\n        console.error('Unexpected error in search API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n// Helper function to generate URLs for different result types\nfunction getResultUrl(tipo, id) {\n    switch(tipo){\n        case 'faq':\n            return `/faqs/${id}`;\n        case 'tramite':\n            return `/servicios/tramite/${id}`;\n        case 'opa':\n            return `/procedimientos/${id}`;\n        default:\n            return '#';\n    }\n}\n// Helper function to get category for result types\nfunction getResultCategory(tipo) {\n    switch(tipo){\n        case 'faq':\n            return 'Preguntas Frecuentes';\n        case 'tramite':\n            return 'Trámites y Servicios';\n        case 'opa':\n            return 'Procedimientos Administrativos';\n        default:\n            return 'General';\n    }\n}\n// POST endpoint for advanced search with filters\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { query, filters = {}, limit = 20, offset = 0 } = body;\n        if (!query || query.trim().length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Search query is required'\n            }, {\n                status: 400\n            });\n        }\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabase)();\n        // Perform search with additional filters\n        let searchPromises = [];\n        // Search FAQs with filters\n        if (!filters.tipo || filters.tipo === 'faq') {\n            let faqQuery = supabase.schema('ingestion').from('faqs').select(`\n          id,\n          tema,\n          pregunta,\n          respuesta,\n          dependencias:dependencia_id (nombre),\n          subdependencias:subdependencia_id (nombre)\n        `).eq('activo', true).textSearch('vector_busqueda', query, {\n                config: 'spanish'\n            });\n            if (filters.dependencia) {\n                faqQuery = faqQuery.eq('dependencia_id', filters.dependencia);\n            }\n            searchPromises.push(faqQuery.then(({ data, error })=>({\n                    type: 'faq',\n                    data: data?.map((item)=>({\n                            ...item,\n                            tipo: 'faq',\n                            titulo: item.pregunta,\n                            contenido: item.respuesta\n                        })) || [],\n                    error\n                })));\n        }\n        // Search Tramites with filters\n        if (!filters.tipo || filters.tipo === 'tramite') {\n            let tramiteQuery = supabase.schema('ingestion').from('tramites').select(`\n          id,\n          nombre,\n          descripcion,\n          categoria,\n          dependencias:dependencia_id (nombre),\n          subdependencias:subdependencia_id (nombre)\n        `).eq('activo', true).textSearch('vector_busqueda', query, {\n                config: 'spanish'\n            });\n            if (filters.dependencia) {\n                tramiteQuery = tramiteQuery.eq('dependencia_id', filters.dependencia);\n            }\n            if (filters.categoria) {\n                tramiteQuery = tramiteQuery.eq('categoria', filters.categoria);\n            }\n            searchPromises.push(tramiteQuery.then(({ data, error })=>({\n                    type: 'tramite',\n                    data: data?.map((item)=>({\n                            ...item,\n                            tipo: 'tramite',\n                            titulo: item.nombre,\n                            contenido: item.descripcion\n                        })) || [],\n                    error\n                })));\n        }\n        const results = await Promise.all(searchPromises);\n        // Check for errors\n        const errors = results.filter((r)=>r.error);\n        if (errors.length > 0) {\n            console.error('Search errors:', errors);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Search failed',\n                details: errors.map((e)=>e.error?.message)\n            }, {\n                status: 500\n            });\n        }\n        // Combine and sort results\n        const allResults = results.flatMap((r)=>r.data || []);\n        const sortedResults = allResults.slice(offset, offset + limit);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            query,\n            filters,\n            data: sortedResults,\n            pagination: {\n                limit,\n                offset,\n                total: allResults.length\n            }\n        });\n    } catch (error) {\n        console.error('Error in advanced search:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Search failed'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/search/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/supabase-server.ts":
/*!********************************!*\
  !*** ./lib/supabase-server.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAdminSupabase: () => (/* binding */ createAdminSupabase),\n/* harmony export */   createServerSupabase: () => (/* binding */ createServerSupabase),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/../../node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Environment variables validation\nconst supabaseUrl = \"https://hndowofzjzjoljnapokv.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY\";\nconst serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error('Missing Supabase environment variables');\n}\n// Admin client for server-side operations (API routes)\nfunction createAdminSupabase() {\n    // For now, use anon key if service role key is not available\n    const key = serviceRoleKey || supabaseAnonKey;\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, key, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n// Regular client for server-side operations\nfunction createServerSupabase() {\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n}\n// Default export for API routes\nconst supabase = createServerSupabase();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/supabase-server.ts\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!**********************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \**********************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?3713":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?8e41":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/isows","vendor-chunks/tr46","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsearch%2Froute&page=%2Fapi%2Fsearch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsearch%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();