(()=>{var e={};e.id=570,e.ids=[570],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25089:(e,a,t)=>{"use strict";t.d(a,{default:()=>r});let r=(0,t(33952).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\PageLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\components\\layout\\PageLayout.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},53558:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});var r=t(61365);let s=r.forwardRef(function({title:e,titleId:a,...t},s){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},t),e?r.createElement("title",{id:a},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68819:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>g,metadata:()=>x});var r=t(38828),s=t(25089),n=t(61365);let i=n.forwardRef(function({title:e,titleId:a,...t},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":a},t),e?n.createElement("title",{id:a},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))}),o=n.forwardRef(function({title:e,titleId:a,...t},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":a},t),e?n.createElement("title",{id:a},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"}))}),l=n.forwardRef(function({title:e,titleId:a,...t},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":a},t),e?n.createElement("title",{id:a},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))});var c=t(53558);let d=n.forwardRef(function({title:e,titleId:a,...t},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":a},t),e?n.createElement("title",{id:a},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}),n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"}))}),m=n.forwardRef(function({title:e,titleId:a,...t},r){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":a},t),e?n.createElement("title",{id:a},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}),x={title:"Contacto | Portal CHIA",description:"Contacta con la Alcald\xeda de Ch\xeda. Encuentra informaci\xf3n de contacto, horarios de atenci\xf3n, ubicaci\xf3n y canales de comunicaci\xf3n.",keywords:"contacto, alcald\xeda Ch\xeda, tel\xe9fono, direcci\xf3n, horarios atenci\xf3n"},u=[{id:"phone",title:"Tel\xe9fono Principal",description:"L\xednea de atenci\xf3n ciudadana",value:"(*************",icon:i,available:"24/7",action:"tel:+576011234567"},{id:"whatsapp",title:"WhatsApp",description:"Chat directo con funcionarios",value:"+57 ************",icon:o,available:"Lun-Vie 8AM-5PM",action:"https://wa.me/573001234567"},{id:"email",title:"Correo Electr\xf3nico",description:"Consultas y solicitudes generales",value:"<EMAIL>",icon:l,available:"Respuesta en 24-48h",action:"mailto:<EMAIL>"}],p=[{name:"Bomberos",phone:"119",description:"Emergencias de incendio y rescate"},{name:"Polic\xeda Nacional",phone:"123",description:"Seguridad y orden p\xfablico"},{name:"Cruz Roja",phone:"132",description:"Emergencias m\xe9dicas"},{name:"Defensa Civil",phone:"144",description:"Desastres naturales"}],h=[{name:"Secretar\xeda General",email:"<EMAIL>",phone:"Ext. 101"},{name:"Hacienda Municipal",email:"<EMAIL>",phone:"Ext. 102"},{name:"Planeaci\xf3n Municipal",email:"<EMAIL>",phone:"Ext. 103"},{name:"Obras P\xfablicas",email:"<EMAIL>",phone:"Ext. 104"},{name:"Desarrollo Social",email:"<EMAIL>",phone:"Ext. 105"}];function g(){let e=new Date().toLocaleTimeString("es-CO",{timeZone:"America/Bogota",hour:"2-digit",minute:"2-digit"}),a=()=>{let e=new Date,a=e.getHours(),t=e.getDay();return t>=1&&t<=5?a>=8&&a<17:6===t&&a>=8&&a<12};return(0,r.jsxs)(s.default,{className:"bg-gray-50",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-primary-600 to-primary-800 text-white",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"Contacto"}),(0,r.jsx)("p",{className:"text-xl text-primary-100 mb-8 max-w-3xl mx-auto",children:"Estamos aqu\xed para ayudarte. Encuentra toda la informaci\xf3n de contacto de la Alcald\xeda de Ch\xeda y nuestros canales de atenci\xf3n."})]})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12",children:u.map(e=>(0,r.jsxs)("a",{href:e.action,className:"bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow p-6 text-center group",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-200 transition-colors",children:(0,r.jsx)(e.icon,{className:"h-8 w-8 text-primary-600"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:e.title}),(0,r.jsx)("p",{className:"text-gray-600 mb-3",children:e.description}),(0,r.jsx)("p",{className:"text-lg font-medium text-primary-600 mb-2",children:e.value}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:e.available})]},e.id))}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-md p-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,r.jsx)(c.A,{className:"h-6 w-6 text-primary-600"}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Horarios de Atenci\xf3n"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,r.jsx)("span",{className:"font-medium text-gray-900",children:"Lunes a Viernes"}),(0,r.jsx)("span",{className:"text-gray-600",children:"8:00 AM - 5:00 PM"})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,r.jsx)("span",{className:"font-medium text-gray-900",children:"S\xe1bados"}),(0,r.jsx)("span",{className:"text-gray-600",children:"8:00 AM - 12:00 PM"})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,r.jsx)("span",{className:"font-medium text-gray-900",children:"Domingos y Festivos"}),(0,r.jsx)("span",{className:"text-red-600",children:"Cerrado"})]})]}),(0,r.jsxs)("div",{className:"mt-6 p-4 bg-primary-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)("div",{className:`w-3 h-3 rounded-full ${a()?"bg-green-500":"bg-red-500"}`}),(0,r.jsx)("span",{className:"font-medium text-gray-900",children:a()?"Abierto ahora":"Cerrado ahora"})]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Hora actual: ",e," (Hora de Colombia)"]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-md p-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,r.jsx)(d,{className:"h-6 w-6 text-primary-600"}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Ubicaci\xf3n"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Alcald\xeda Municipal de Ch\xeda"}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Carrera 11 No. 17-25",(0,r.jsx)("br",{}),"Ch\xeda, Cundinamarca",(0,r.jsx)("br",{}),"Colombia"]})]}),(0,r.jsxs)("a",{href:"https://maps.google.com/?q=Alcald\xeda+Ch\xeda+Cundinamarca",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-2 text-primary-600 hover:text-primary-700 font-medium",children:["Ver en Google Maps",(0,r.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"})})]})]})]})]}),(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-md p-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Dependencias"}),(0,r.jsx)("div",{className:"space-y-4",children:h.map((e,a)=>(0,r.jsxs)("div",{className:"border-b border-gray-200 pb-4 last:border-b-0",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 mb-1",children:e.name}),(0,r.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,r.jsxs)("p",{children:["\uD83D\uDCE7 ",e.email]}),(0,r.jsxs)("p",{children:["\uD83D\uDCDE ",e.phone]})]})]},a))})]}),(0,r.jsxs)("div",{className:"bg-red-50 rounded-xl shadow-md p-6 border border-red-200",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,r.jsx)(m,{className:"h-6 w-6 text-red-600"}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-red-900",children:"Contactos de Emergencia"})]}),(0,r.jsx)("div",{className:"space-y-3",children:p.map((e,a)=>(0,r.jsxs)("div",{className:"flex justify-between items-center p-3 bg-white rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]}),(0,r.jsx)("a",{href:`tel:${e.phone}`,className:"text-xl font-bold text-red-600 hover:text-red-700",children:e.phone})]},a))})]})]})]}),(0,r.jsxs)("div",{className:"mt-12 bg-white rounded-xl shadow-md p-8 text-center",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"\xbfTienes una consulta espec\xedfica?"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6 max-w-2xl mx-auto",children:"Nuestro asistente de IA puede ayudarte de inmediato, o puedes contactarnos directamente a trav\xe9s de nuestros canales oficiales."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)("a",{href:"/chat",className:"inline-flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",children:"Hablar con el Asistente IA"}),(0,r.jsx)("a",{href:"mailto:<EMAIL>",className:"inline-flex items-center gap-2 bg-white hover:bg-gray-50 text-primary-600 border border-primary-600 px-6 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",children:"Enviar Email"})]})]})]})]})}},85100:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=t(24332),s=t(48819),n=t(67851),i=t.n(n),o=t(97540),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(a,l);let c={children:["",{children:["contacto",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,68819)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\contacto\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,34356)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\contacto\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/contacto/page",pathname:"/contacto",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},88556:(e,a,t)=>{Promise.resolve().then(t.bind(t,25089))},94636:(e,a,t)=>{Promise.resolve().then(t.bind(t,52300))}};var a=require("../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),r=a.X(0,[191,118,114,439],()=>t(85100));module.exports=r})();