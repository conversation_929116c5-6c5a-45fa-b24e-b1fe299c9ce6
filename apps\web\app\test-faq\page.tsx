'use client';

import { useState, useEffect } from 'react';

interface FAQ {
  id: string;
  tema: string;
  pregunta: string;
  respuesta: string;
  dependencia: {
    id: string;
    codigo: string;
    nombre: string;
    sigla: string;
  };
}

export default function TestFAQPage() {
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [topics, setTopics] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testGetFAQs = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch('/api/faqs?limit=5');
      const data = await response.json();
      setFaqs(data.data || []);
    } catch (err) {
      setError('Error al obtener FAQs: ' + (err instanceof Error ? err.message : 'Error desconocido'));
    } finally {
      setLoading(false);
    }
  };

  const testGetTopics = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch('/api/faqs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'get_topics' }),
      });
      const data = await response.json();
      setTopics(data.data || []);
    } catch (err) {
      setError('Error al obtener temas: ' + (err instanceof Error ? err.message : 'Error desconocido'));
    } finally {
      setLoading(false);
    }
  };

  const testSearch = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch('/api/faqs?search=certificado&limit=3');
      const data = await response.json();
      setFaqs(data.data || []);
    } catch (err) {
      setError('Error en búsqueda: ' + (err instanceof Error ? err.message : 'Error desconocido'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    testGetFAQs();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Prueba de Sistema de FAQs
        </h1>

        {/* Test Buttons */}
        <div className="mb-8 space-x-4">
          <button
            onClick={testGetFAQs}
            disabled={loading}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium"
          >
            Obtener FAQs
          </button>
          <button
            onClick={testGetTopics}
            disabled={loading}
            className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium"
          >
            Obtener Temas
          </button>
          <button
            onClick={testSearch}
            disabled={loading}
            className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium"
          >
            Buscar "certificado"
          </button>
        </div>

        {/* Loading */}
        {loading && (
          <div className="text-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Cargando...</p>
          </div>
        )}

        {/* Error */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {/* Topics */}
        {topics.length > 0 && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Temas Disponibles ({topics.length})
            </h2>
            <div className="flex flex-wrap gap-2">
              {topics.map((topic, index) => (
                <span
                  key={index}
                  className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm"
                >
                  {topic}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* FAQs */}
        {faqs.length > 0 && (
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              FAQs ({faqs.length})
            </h2>
            <div className="space-y-4">
              {faqs.map((faq) => (
                <div key={faq.id} className="bg-white rounded-lg shadow-md p-6">
                  <div className="mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
                      {faq.tema}
                    </span>
                    <span className="ml-2 text-sm text-gray-500">
                      {faq.dependencia.nombre}
                    </span>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {faq.pregunta}
                  </h3>
                  <p className="text-gray-700 text-sm">
                    {faq.respuesta.substring(0, 200)}
                    {faq.respuesta.length > 200 ? '...' : ''}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* No Data */}
        {!loading && !error && faqs.length === 0 && topics.length === 0 && (
          <div className="text-center py-8">
            <p className="text-gray-600">No hay datos para mostrar</p>
          </div>
        )}
      </div>
    </div>
  );
}
