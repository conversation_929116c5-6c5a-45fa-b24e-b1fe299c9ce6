(()=>{var e={};e.id=881,e.ids=[881],e.modules={1241:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},18872:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>d,POST:()=>u});var i=t(48106),o=t(48819),a=t(12050),n=t(4235),c=t(73474);async function d(e){try{let r=(0,c.y8)(),{searchParams:t}=new URL(e.url),s=parseInt(t.get("limit")||"20"),i=parseInt(t.get("offset")||"0"),o=t.get("categoria"),a=t.get("dependencia"),d=t.get("search"),u=r.schema("ingestion").from("tramites").select(`
        id,
        nombre,
        descripcion,
        formulario,
        tiempo_respuesta,
        tiene_pago,
        costo_detalle,
        url_suit,
        url_govco,
        categoria,
        modalidad,
        requisitos,
        documentos_requeridos,
        popularidad,
        satisfaccion_promedio,
        dependencias:dependencia_id (
          id,
          codigo,
          nombre,
          sigla
        ),
        subdependencias:subdependencia_id (
          id,
          codigo,
          nombre,
          sigla
        )
      `).eq("activo",!0).order("popularidad",{ascending:!1}).range(i,i+s-1);o&&(u=u.eq("categoria",o)),a&&(u=u.eq("dependencia_id",a)),d&&(u=u.textSearch("vector_busqueda",d,{type:"websearch",config:"spanish"}));let{data:p,error:l,count:g}=await u;if(l)return console.error("Error fetching tramites:",l),n.NextResponse.json({error:"Failed to fetch government procedures",details:l.message},{status:500});let m=p?.map(e=>({id:e.id,nombre:e.nombre,descripcion:e.descripcion||"",categoria:e.categoria||"General",tiempoRespuesta:e.tiempo_respuesta||"No especificado",tienePago:e.tiene_pago||"No especificado",costoDetalle:e.costo_detalle,modalidad:e.modalidad||[],requisitos:e.requisitos||[],documentosRequeridos:e.documentos_requeridos||[],urlSuit:e.url_suit,urlGovco:e.url_govco,popularidad:e.popularidad||0,satisfaccion:e.satisfaccion_promedio||0,dependencia:{id:e.dependencias?.id,codigo:e.dependencias?.codigo,nombre:e.dependencias?.nombre,sigla:e.dependencias?.sigla},subdependencia:e.subdependencias?{id:e.subdependencias.id,codigo:e.subdependencias.codigo,nombre:e.subdependencias.nombre,sigla:e.subdependencias.sigla}:null}))||[];return n.NextResponse.json({success:!0,data:m,pagination:{limit:s,offset:i,total:g||m.length},filters:{categoria:o,dependencia:a,search:d}})}catch(e){return console.error("Unexpected error in tramites API:",e),n.NextResponse.json({error:"Internal server error",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function u(e){try{let{action:r}=await e.json();if("get_categories"===r){let e=(0,c.y8)(),{data:r,error:t}=await e.schema("ingestion").from("tramites").select("categoria").eq("activo",!0).not("categoria","is",null);if(t)throw t;let s=[...new Set(r?.map(e=>e.categoria))].filter(Boolean);return n.NextResponse.json({success:!0,data:s})}return n.NextResponse.json({error:"Invalid action"},{status:400})}catch(e){return console.error("Error in tramites POST:",e),n.NextResponse.json({error:"Failed to process request"},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/tramites/route",pathname:"/api/tramites",filename:"route",bundlePath:"app/api/tramites/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\api\\tramites\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:g,serverHooks:m}=p;function x(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:g})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72088:()=>{},73474:(e,r,t)=>{"use strict";t.d(r,{y8:()=>a});var s=t(2492);let i=process.env.NEXT_PUBLIC_SUPABASE_URL,o=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;if(process.env.SUPABASE_SERVICE_ROLE_KEY,!i||!o)throw Error("Missing Supabase environment variables");let a=()=>(0,s.UU)(i,o);a()},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80408:()=>{},81630:e=>{"use strict";e.exports=require("http")},87032:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[191,111,744],()=>t(18872));module.exports=s})();