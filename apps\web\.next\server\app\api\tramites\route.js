/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/tramites/route";
exports.ids = ["app/api/tramites/route"];
exports.modules = {

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftramites%2Froute&page=%2Fapi%2Ftramites%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftramites%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftramites%2Froute&page=%2Fapi%2Ftramites%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftramites%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/../../node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/../../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Juan_Pulgarin_Documents_augment_projects_chia_next_apps_web_app_api_tramites_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/tramites/route.ts */ \"(rsc)/./app/api/tramites/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/tramites/route\",\n        pathname: \"/api/tramites\",\n        filename: \"route\",\n        bundlePath: \"app/api/tramites/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\api\\\\tramites\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Juan_Pulgarin_Documents_augment_projects_chia_next_apps_web_app_api_tramites_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftramites%2Froute&page=%2Fapi%2Ftramites%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftramites%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!**********************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \**********************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./app/api/tramites/route.ts":
/*!***********************************!*\
  !*** ./app/api/tramites/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/../../node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase-server */ \"(rsc)/./lib/supabase-server.ts\");\n\n\nasync function GET(request) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabase)();\n        const { searchParams } = new URL(request.url);\n        // Get query parameters\n        const limit = parseInt(searchParams.get('limit') || '20');\n        const offset = parseInt(searchParams.get('offset') || '0');\n        const categoria = searchParams.get('categoria');\n        const dependencia = searchParams.get('dependencia');\n        const search = searchParams.get('search');\n        let query = supabase.from('tramites_view').select('*').order('nombre').range(offset, offset + limit - 1);\n        // Apply filters\n        if (categoria) {\n            query = query.eq('categoria', categoria);\n        }\n        if (dependencia) {\n            query = query.eq('dependencia_id', dependencia);\n        }\n        // Apply search filter using ilike for now (since we don't have full-text search in view)\n        if (search) {\n            query = query.or(`nombre.ilike.%${search}%,descripcion.ilike.%${search}%`);\n        }\n        const { data: tramites, error, count } = await query;\n        if (error) {\n            console.error('Error fetching tramites:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch government procedures',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        // Transform data to match frontend expectations\n        const transformedTramites = tramites?.map((tramite)=>({\n                id: tramite.id,\n                nombre: tramite.nombre,\n                descripcion: tramite.descripcion || '',\n                categoria: tramite.categoria || 'General',\n                tiempoRespuesta: tramite.tiempo_estimado || 'No especificado',\n                tienePago: tramite.costo ? 'Sí' : 'No',\n                costoDetalle: tramite.costo,\n                modalidad: tramite.modalidad || 'Presencial',\n                requisitos: tramite.requisitos || [],\n                documentosRequeridos: [],\n                urlSuit: '',\n                urlGovco: '',\n                popularidad: 0,\n                satisfaccion: 0,\n                dependencia: {\n                    id: tramite.dependencia_id,\n                    codigo: tramite.dependencia_codigo,\n                    nombre: tramite.dependencia_nombre,\n                    sigla: tramite.dependencia_sigla\n                }\n            })) || [];\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: transformedTramites,\n            pagination: {\n                limit,\n                offset,\n                total: count || transformedTramites.length\n            },\n            filters: {\n                categoria,\n                dependencia,\n                search\n            }\n        });\n    } catch (error) {\n        console.error('Unexpected error in tramites API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n// Get categories for filtering\nasync function POST(request) {\n    try {\n        const { action } = await request.json();\n        if (action === 'get_categories') {\n            const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabase)();\n            const { data: categories, error } = await supabase.schema('ingestion').from('tramites').select('categoria').eq('activo', true).not('categoria', 'is', null);\n            if (error) {\n                throw error;\n            }\n            const uniqueCategories = [\n                ...new Set(categories?.map((t)=>t.categoria))\n            ].filter(Boolean);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: uniqueCategories\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Invalid action'\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error('Error in tramites POST:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to process request'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL3RyYW1pdGVzL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBd0Q7QUFDSztBQUV0RCxlQUFlRSxJQUFJQyxPQUFvQjtJQUM1QyxJQUFJO1FBQ0YsTUFBTUMsV0FBV0gsMEVBQW9CQTtRQUNyQyxNQUFNLEVBQUVJLFlBQVksRUFBRSxHQUFHLElBQUlDLElBQUlILFFBQVFJLEdBQUc7UUFFNUMsdUJBQXVCO1FBQ3ZCLE1BQU1DLFFBQVFDLFNBQVNKLGFBQWFLLEdBQUcsQ0FBQyxZQUFZO1FBQ3BELE1BQU1DLFNBQVNGLFNBQVNKLGFBQWFLLEdBQUcsQ0FBQyxhQUFhO1FBQ3RELE1BQU1FLFlBQVlQLGFBQWFLLEdBQUcsQ0FBQztRQUNuQyxNQUFNRyxjQUFjUixhQUFhSyxHQUFHLENBQUM7UUFDckMsTUFBTUksU0FBU1QsYUFBYUssR0FBRyxDQUFDO1FBRWhDLElBQUlLLFFBQVFYLFNBQ1RZLElBQUksQ0FBQyxpQkFDTEMsTUFBTSxDQUFDLEtBQ1BDLEtBQUssQ0FBQyxVQUNOQyxLQUFLLENBQUNSLFFBQVFBLFNBQVNILFFBQVE7UUFFbEMsZ0JBQWdCO1FBQ2hCLElBQUlJLFdBQVc7WUFDYkcsUUFBUUEsTUFBTUssRUFBRSxDQUFDLGFBQWFSO1FBQ2hDO1FBRUEsSUFBSUMsYUFBYTtZQUNmRSxRQUFRQSxNQUFNSyxFQUFFLENBQUMsa0JBQWtCUDtRQUNyQztRQUVBLHlGQUF5RjtRQUN6RixJQUFJQyxRQUFRO1lBQ1ZDLFFBQVFBLE1BQU1NLEVBQUUsQ0FBQyxDQUFDLGNBQWMsRUFBRVAsT0FBTyxxQkFBcUIsRUFBRUEsT0FBTyxDQUFDLENBQUM7UUFDM0U7UUFFQSxNQUFNLEVBQUVRLE1BQU1DLFFBQVEsRUFBRUMsS0FBSyxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNVjtRQUUvQyxJQUFJUyxPQUFPO1lBQ1RFLFFBQVFGLEtBQUssQ0FBQyw0QkFBNEJBO1lBQzFDLE9BQU94QixxREFBWUEsQ0FBQzJCLElBQUksQ0FDdEI7Z0JBQUVILE9BQU87Z0JBQXlDSSxTQUFTSixNQUFNSyxPQUFPO1lBQUMsR0FDekU7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLGdEQUFnRDtRQUNoRCxNQUFNQyxzQkFBc0JSLFVBQVVTLElBQUlDLENBQUFBLFVBQVk7Z0JBQ3BEQyxJQUFJRCxRQUFRQyxFQUFFO2dCQUNkQyxRQUFRRixRQUFRRSxNQUFNO2dCQUN0QkMsYUFBYUgsUUFBUUcsV0FBVyxJQUFJO2dCQUNwQ3hCLFdBQVdxQixRQUFRckIsU0FBUyxJQUFJO2dCQUNoQ3lCLGlCQUFpQkosUUFBUUssZUFBZSxJQUFJO2dCQUM1Q0MsV0FBV04sUUFBUU8sS0FBSyxHQUFHLE9BQU87Z0JBQ2xDQyxjQUFjUixRQUFRTyxLQUFLO2dCQUMzQkUsV0FBV1QsUUFBUVMsU0FBUyxJQUFJO2dCQUNoQ0MsWUFBWVYsUUFBUVUsVUFBVSxJQUFJLEVBQUU7Z0JBQ3BDQyxzQkFBc0IsRUFBRTtnQkFDeEJDLFNBQVM7Z0JBQ1RDLFVBQVU7Z0JBQ1ZDLGFBQWE7Z0JBQ2JDLGNBQWM7Z0JBQ2RuQyxhQUFhO29CQUNYcUIsSUFBSUQsUUFBUWdCLGNBQWM7b0JBQzFCQyxRQUFRakIsUUFBUWtCLGtCQUFrQjtvQkFDbENoQixRQUFRRixRQUFRbUIsa0JBQWtCO29CQUNsQ0MsT0FBT3BCLFFBQVFxQixpQkFBaUI7Z0JBQ2xDO1lBQ0YsT0FBTyxFQUFFO1FBRVQsT0FBT3RELHFEQUFZQSxDQUFDMkIsSUFBSSxDQUFDO1lBQ3ZCNEIsU0FBUztZQUNUakMsTUFBTVM7WUFDTnlCLFlBQVk7Z0JBQ1ZoRDtnQkFDQUc7Z0JBQ0E4QyxPQUFPaEMsU0FBU00sb0JBQW9CMkIsTUFBTTtZQUM1QztZQUNBQyxTQUFTO2dCQUNQL0M7Z0JBQ0FDO2dCQUNBQztZQUNGO1FBQ0Y7SUFFRixFQUFFLE9BQU9VLE9BQU87UUFDZEUsUUFBUUYsS0FBSyxDQUFDLHFDQUFxQ0E7UUFDbkQsT0FBT3hCLHFEQUFZQSxDQUFDMkIsSUFBSSxDQUN0QjtZQUNFSCxPQUFPO1lBQ1BJLFNBQVNKLGlCQUFpQm9DLFFBQVFwQyxNQUFNSyxPQUFPLEdBQUc7UUFDcEQsR0FDQTtZQUFFQyxRQUFRO1FBQUk7SUFFbEI7QUFDRjtBQUVBLCtCQUErQjtBQUN4QixlQUFlK0IsS0FBSzFELE9BQW9CO0lBQzdDLElBQUk7UUFDRixNQUFNLEVBQUUyRCxNQUFNLEVBQUUsR0FBRyxNQUFNM0QsUUFBUXdCLElBQUk7UUFFckMsSUFBSW1DLFdBQVcsa0JBQWtCO1lBQy9CLE1BQU0xRCxXQUFXSCwwRUFBb0JBO1lBRXJDLE1BQU0sRUFBRXFCLE1BQU15QyxVQUFVLEVBQUV2QyxLQUFLLEVBQUUsR0FBRyxNQUFNcEIsU0FDdkM0RCxNQUFNLENBQUMsYUFDUGhELElBQUksQ0FBQyxZQUNMQyxNQUFNLENBQUMsYUFDUEcsRUFBRSxDQUFDLFVBQVUsTUFDYjZDLEdBQUcsQ0FBQyxhQUFhLE1BQU07WUFFMUIsSUFBSXpDLE9BQU87Z0JBQ1QsTUFBTUE7WUFDUjtZQUVBLE1BQU0wQyxtQkFBbUI7bUJBQUksSUFBSUMsSUFBSUosWUFBWS9CLElBQUlvQyxDQUFBQSxJQUFLQSxFQUFFeEQsU0FBUzthQUFHLENBQUN5RCxNQUFNLENBQUNDO1lBRWhGLE9BQU90RSxxREFBWUEsQ0FBQzJCLElBQUksQ0FBQztnQkFDdkI0QixTQUFTO2dCQUNUakMsTUFBTTRDO1lBQ1I7UUFDRjtRQUVBLE9BQU9sRSxxREFBWUEsQ0FBQzJCLElBQUksQ0FDdEI7WUFBRUgsT0FBTztRQUFpQixHQUMxQjtZQUFFTSxRQUFRO1FBQUk7SUFHbEIsRUFBRSxPQUFPTixPQUFPO1FBQ2RFLFFBQVFGLEtBQUssQ0FBQywyQkFBMkJBO1FBQ3pDLE9BQU94QixxREFBWUEsQ0FBQzJCLElBQUksQ0FDdEI7WUFBRUgsT0FBTztRQUE0QixHQUNyQztZQUFFTSxRQUFRO1FBQUk7SUFFbEI7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxKdWFuIFB1bGdhcmluXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXGNoaWEtbmV4dFxcYXBwc1xcd2ViXFxhcHBcXGFwaVxcdHJhbWl0ZXNcXHJvdXRlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0LCBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcic7XG5pbXBvcnQgeyBjcmVhdGVTZXJ2ZXJTdXBhYmFzZSB9IGZyb20gJ0AvbGliL3N1cGFiYXNlLXNlcnZlcic7XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZVNlcnZlclN1cGFiYXNlKCk7XG4gICAgY29uc3QgeyBzZWFyY2hQYXJhbXMgfSA9IG5ldyBVUkwocmVxdWVzdC51cmwpO1xuICAgIFxuICAgIC8vIEdldCBxdWVyeSBwYXJhbWV0ZXJzXG4gICAgY29uc3QgbGltaXQgPSBwYXJzZUludChzZWFyY2hQYXJhbXMuZ2V0KCdsaW1pdCcpIHx8ICcyMCcpO1xuICAgIGNvbnN0IG9mZnNldCA9IHBhcnNlSW50KHNlYXJjaFBhcmFtcy5nZXQoJ29mZnNldCcpIHx8ICcwJyk7XG4gICAgY29uc3QgY2F0ZWdvcmlhID0gc2VhcmNoUGFyYW1zLmdldCgnY2F0ZWdvcmlhJyk7XG4gICAgY29uc3QgZGVwZW5kZW5jaWEgPSBzZWFyY2hQYXJhbXMuZ2V0KCdkZXBlbmRlbmNpYScpO1xuICAgIGNvbnN0IHNlYXJjaCA9IHNlYXJjaFBhcmFtcy5nZXQoJ3NlYXJjaCcpO1xuXG4gICAgbGV0IHF1ZXJ5ID0gc3VwYWJhc2VcbiAgICAgIC5mcm9tKCd0cmFtaXRlc192aWV3JylcbiAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgLm9yZGVyKCdub21icmUnKVxuICAgICAgLnJhbmdlKG9mZnNldCwgb2Zmc2V0ICsgbGltaXQgLSAxKTtcblxuICAgIC8vIEFwcGx5IGZpbHRlcnNcbiAgICBpZiAoY2F0ZWdvcmlhKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5LmVxKCdjYXRlZ29yaWEnLCBjYXRlZ29yaWEpO1xuICAgIH1cblxuICAgIGlmIChkZXBlbmRlbmNpYSkge1xuICAgICAgcXVlcnkgPSBxdWVyeS5lcSgnZGVwZW5kZW5jaWFfaWQnLCBkZXBlbmRlbmNpYSk7XG4gICAgfVxuXG4gICAgLy8gQXBwbHkgc2VhcmNoIGZpbHRlciB1c2luZyBpbGlrZSBmb3Igbm93IChzaW5jZSB3ZSBkb24ndCBoYXZlIGZ1bGwtdGV4dCBzZWFyY2ggaW4gdmlldylcbiAgICBpZiAoc2VhcmNoKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5Lm9yKGBub21icmUuaWxpa2UuJSR7c2VhcmNofSUsZGVzY3JpcGNpb24uaWxpa2UuJSR7c2VhcmNofSVgKTtcbiAgICB9XG5cbiAgICBjb25zdCB7IGRhdGE6IHRyYW1pdGVzLCBlcnJvciwgY291bnQgfSA9IGF3YWl0IHF1ZXJ5O1xuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyB0cmFtaXRlczonLCBlcnJvcik7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgZXJyb3I6ICdGYWlsZWQgdG8gZmV0Y2ggZ292ZXJubWVudCBwcm9jZWR1cmVzJywgZGV0YWlsczogZXJyb3IubWVzc2FnZSB9LFxuICAgICAgICB7IHN0YXR1czogNTAwIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgLy8gVHJhbnNmb3JtIGRhdGEgdG8gbWF0Y2ggZnJvbnRlbmQgZXhwZWN0YXRpb25zXG4gICAgY29uc3QgdHJhbnNmb3JtZWRUcmFtaXRlcyA9IHRyYW1pdGVzPy5tYXAodHJhbWl0ZSA9PiAoe1xuICAgICAgaWQ6IHRyYW1pdGUuaWQsXG4gICAgICBub21icmU6IHRyYW1pdGUubm9tYnJlLFxuICAgICAgZGVzY3JpcGNpb246IHRyYW1pdGUuZGVzY3JpcGNpb24gfHwgJycsXG4gICAgICBjYXRlZ29yaWE6IHRyYW1pdGUuY2F0ZWdvcmlhIHx8ICdHZW5lcmFsJyxcbiAgICAgIHRpZW1wb1Jlc3B1ZXN0YTogdHJhbWl0ZS50aWVtcG9fZXN0aW1hZG8gfHwgJ05vIGVzcGVjaWZpY2FkbycsXG4gICAgICB0aWVuZVBhZ286IHRyYW1pdGUuY29zdG8gPyAnU8OtJyA6ICdObycsXG4gICAgICBjb3N0b0RldGFsbGU6IHRyYW1pdGUuY29zdG8sXG4gICAgICBtb2RhbGlkYWQ6IHRyYW1pdGUubW9kYWxpZGFkIHx8ICdQcmVzZW5jaWFsJyxcbiAgICAgIHJlcXVpc2l0b3M6IHRyYW1pdGUucmVxdWlzaXRvcyB8fCBbXSxcbiAgICAgIGRvY3VtZW50b3NSZXF1ZXJpZG9zOiBbXSxcbiAgICAgIHVybFN1aXQ6ICcnLFxuICAgICAgdXJsR292Y286ICcnLFxuICAgICAgcG9wdWxhcmlkYWQ6IDAsXG4gICAgICBzYXRpc2ZhY2Npb246IDAsXG4gICAgICBkZXBlbmRlbmNpYToge1xuICAgICAgICBpZDogdHJhbWl0ZS5kZXBlbmRlbmNpYV9pZCxcbiAgICAgICAgY29kaWdvOiB0cmFtaXRlLmRlcGVuZGVuY2lhX2NvZGlnbyxcbiAgICAgICAgbm9tYnJlOiB0cmFtaXRlLmRlcGVuZGVuY2lhX25vbWJyZSxcbiAgICAgICAgc2lnbGE6IHRyYW1pdGUuZGVwZW5kZW5jaWFfc2lnbGFcbiAgICAgIH1cbiAgICB9KSkgfHwgW107XG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIGRhdGE6IHRyYW5zZm9ybWVkVHJhbWl0ZXMsXG4gICAgICBwYWdpbmF0aW9uOiB7XG4gICAgICAgIGxpbWl0LFxuICAgICAgICBvZmZzZXQsXG4gICAgICAgIHRvdGFsOiBjb3VudCB8fCB0cmFuc2Zvcm1lZFRyYW1pdGVzLmxlbmd0aFxuICAgICAgfSxcbiAgICAgIGZpbHRlcnM6IHtcbiAgICAgICAgY2F0ZWdvcmlhLFxuICAgICAgICBkZXBlbmRlbmNpYSxcbiAgICAgICAgc2VhcmNoXG4gICAgICB9XG4gICAgfSk7XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdVbmV4cGVjdGVkIGVycm9yIGluIHRyYW1pdGVzIEFQSTonLCBlcnJvcik7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBcbiAgICAgICAgZXJyb3I6ICdJbnRlcm5hbCBzZXJ2ZXIgZXJyb3InLCBcbiAgICAgICAgZGV0YWlsczogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnVW5rbm93biBlcnJvcicgXG4gICAgICB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKTtcbiAgfVxufVxuXG4vLyBHZXQgY2F0ZWdvcmllcyBmb3IgZmlsdGVyaW5nXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVChyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IHsgYWN0aW9uIH0gPSBhd2FpdCByZXF1ZXN0Lmpzb24oKTtcbiAgICBcbiAgICBpZiAoYWN0aW9uID09PSAnZ2V0X2NhdGVnb3JpZXMnKSB7XG4gICAgICBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZVNlcnZlclN1cGFiYXNlKCk7XG4gICAgICBcbiAgICAgIGNvbnN0IHsgZGF0YTogY2F0ZWdvcmllcywgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgIC5zY2hlbWEoJ2luZ2VzdGlvbicpXG4gICAgICAgIC5mcm9tKCd0cmFtaXRlcycpXG4gICAgICAgIC5zZWxlY3QoJ2NhdGVnb3JpYScpXG4gICAgICAgIC5lcSgnYWN0aXZvJywgdHJ1ZSlcbiAgICAgICAgLm5vdCgnY2F0ZWdvcmlhJywgJ2lzJywgbnVsbCk7XG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgIH1cblxuICAgICAgY29uc3QgdW5pcXVlQ2F0ZWdvcmllcyA9IFsuLi5uZXcgU2V0KGNhdGVnb3JpZXM/Lm1hcCh0ID0+IHQuY2F0ZWdvcmlhKSldLmZpbHRlcihCb29sZWFuKTtcbiAgICAgIFxuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgZGF0YTogdW5pcXVlQ2F0ZWdvcmllc1xuICAgICAgfSk7XG4gICAgfVxuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBlcnJvcjogJ0ludmFsaWQgYWN0aW9uJyB9LFxuICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgKTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluIHRyYW1pdGVzIFBPU1Q6JywgZXJyb3IpO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdGYWlsZWQgdG8gcHJvY2VzcyByZXF1ZXN0JyB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsImNyZWF0ZVNlcnZlclN1cGFiYXNlIiwiR0VUIiwicmVxdWVzdCIsInN1cGFiYXNlIiwic2VhcmNoUGFyYW1zIiwiVVJMIiwidXJsIiwibGltaXQiLCJwYXJzZUludCIsImdldCIsIm9mZnNldCIsImNhdGVnb3JpYSIsImRlcGVuZGVuY2lhIiwic2VhcmNoIiwicXVlcnkiLCJmcm9tIiwic2VsZWN0Iiwib3JkZXIiLCJyYW5nZSIsImVxIiwib3IiLCJkYXRhIiwidHJhbWl0ZXMiLCJlcnJvciIsImNvdW50IiwiY29uc29sZSIsImpzb24iLCJkZXRhaWxzIiwibWVzc2FnZSIsInN0YXR1cyIsInRyYW5zZm9ybWVkVHJhbWl0ZXMiLCJtYXAiLCJ0cmFtaXRlIiwiaWQiLCJub21icmUiLCJkZXNjcmlwY2lvbiIsInRpZW1wb1Jlc3B1ZXN0YSIsInRpZW1wb19lc3RpbWFkbyIsInRpZW5lUGFnbyIsImNvc3RvIiwiY29zdG9EZXRhbGxlIiwibW9kYWxpZGFkIiwicmVxdWlzaXRvcyIsImRvY3VtZW50b3NSZXF1ZXJpZG9zIiwidXJsU3VpdCIsInVybEdvdmNvIiwicG9wdWxhcmlkYWQiLCJzYXRpc2ZhY2Npb24iLCJkZXBlbmRlbmNpYV9pZCIsImNvZGlnbyIsImRlcGVuZGVuY2lhX2NvZGlnbyIsImRlcGVuZGVuY2lhX25vbWJyZSIsInNpZ2xhIiwiZGVwZW5kZW5jaWFfc2lnbGEiLCJzdWNjZXNzIiwicGFnaW5hdGlvbiIsInRvdGFsIiwibGVuZ3RoIiwiZmlsdGVycyIsIkVycm9yIiwiUE9TVCIsImFjdGlvbiIsImNhdGVnb3JpZXMiLCJzY2hlbWEiLCJub3QiLCJ1bmlxdWVDYXRlZ29yaWVzIiwiU2V0IiwidCIsImZpbHRlciIsIkJvb2xlYW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/api/tramites/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/supabase-server.ts":
/*!********************************!*\
  !*** ./lib/supabase-server.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAdminSupabase: () => (/* binding */ createAdminSupabase),\n/* harmony export */   createServerSupabase: () => (/* binding */ createServerSupabase),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/../../node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Environment variables validation\nconst supabaseUrl = \"https://hndowofzjzjoljnapokv.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY\";\nconst serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error('Missing Supabase environment variables');\n}\n// Admin client for server-side operations (API routes)\nfunction createAdminSupabase() {\n    // For now, use anon key if service role key is not available\n    const key = serviceRoleKey || supabaseAnonKey;\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, key, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n// Regular client for server-side operations\nfunction createServerSupabase() {\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n}\n// Default export for API routes\nconst supabase = createServerSupabase();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/supabase-server.ts\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!**********************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \**********************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?3713":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?8e41":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftramites%2Froute&page=%2Fapi%2Ftramites%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftramites%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();