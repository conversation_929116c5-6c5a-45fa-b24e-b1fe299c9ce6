(()=>{var e={};e.id=71,e.ids=[71],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3842:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(33952).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\(auth)\\\\verify-email\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\(auth)\\verify-email\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14181:!1,19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28495:(e,r,t)=>{Promise.resolve().then(t.bind(t,3842))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36743:(e,r,t)=>{Promise.resolve().then(t.bind(t,14181))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},89494:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>l,routeModule:()=>c,tree:()=>p});var s=t(24332),n=t(48819),i=t(67851),o=t.n(i),a=t(97540),u={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>a[e]);t.d(r,u);let p={children:["",{children:["(auth)",{children:["verify-email",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,3842)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\(auth)\\verify-email\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,86580)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\(auth)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,92341,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,34356)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\(auth)\\verify-email\\page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(auth)/verify-email/page",pathname:"/verify-email",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[191,118,114,85,638,900,145],()=>t(89494));module.exports=s})();