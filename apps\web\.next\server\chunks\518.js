"use strict";exports.id=518,exports.ids=[518],exports.modules={2007:e=>{var t={decodeValues:!0,map:!1,silent:!1};function r(e){return"string"==typeof e&&!!e.trim()}function o(e,o){var i,n,s,a,l=e.split(";").filter(r),u=(i=l.shift(),n="",s="",(a=i.split("=")).length>1?(n=a.shift(),s=a.join("=")):s=i,{name:n,value:s}),c=u.name,p=u.value;o=o?Object.assign({},t,o):t;try{p=o.decodeValues?decodeURIComponent(p):p}catch(e){console.error("set-cookie-parser encountered an error while decoding a cookie with value '"+p+"'. Set options.decodeValues to false to disable this feature.",e)}var d={name:c,value:p};return l.forEach(function(e){var t=e.split("="),r=t.shift().trimLeft().toLowerCase(),o=t.join("=");"expires"===r?d.expires=new Date(o):"max-age"===r?d.maxAge=parseInt(o,10):"secure"===r?d.secure=!0:"httponly"===r?d.httpOnly=!0:"samesite"===r?d.sameSite=o:"partitioned"===r?d.partitioned=!0:d[r]=o}),d}function i(e,i){if(i=i?Object.assign({},t,i):t,!e)if(!i.map)return[];else return{};if(e.headers)if("function"==typeof e.headers.getSetCookie)e=e.headers.getSetCookie();else if(e.headers["set-cookie"])e=e.headers["set-cookie"];else{var n=e.headers[Object.keys(e.headers).find(function(e){return"set-cookie"===e.toLowerCase()})];n||!e.headers.cookie||i.silent||console.warn("Warning: set-cookie-parser appears to have been called on a request object. It is designed to parse Set-Cookie headers from responses, not Cookie headers from requests. Set the option {silent: true} to suppress this warning."),e=n}return(Array.isArray(e)||(e=[e]),i.map)?e.filter(r).reduce(function(e,t){var r=o(t,i);return e[r.name]=r,e},{}):e.filter(r).map(function(e){return o(e,i)})}e.exports=i,e.exports.parse=i,e.exports.parseString=o,e.exports.splitCookiesString=function(e){if(Array.isArray(e))return e;if("string"!=typeof e)return[];var t,r,o,i,n,s=[],a=0;function l(){for(;a<e.length&&/\s/.test(e.charAt(a));)a+=1;return a<e.length}for(;a<e.length;){for(t=a,n=!1;l();)if(","===(r=e.charAt(a))){for(o=a,a+=1,l(),i=a;a<e.length&&"="!==(r=e.charAt(a))&&";"!==r&&","!==r;)a+=1;a<e.length&&"="===e.charAt(a)?(n=!0,a=i,s.push(e.substring(t,o)),t=a):a=o+1}else a+=1;(!n||a>=e.length)&&s.push(e.substring(t,e.length))}return s}},45189:(e,t,r)=>{r.r(t),r.d(t,{BrowserCookieAuthStorageAdapter:()=>E,CookieAuthStorageAdapter:()=>m,DEFAULT_COOKIE_OPTIONS:()=>A,createSupabaseClient:()=>U,isBrowser:()=>k,parseCookies:()=>y,parseSupabaseCookie:()=>_,serializeCookie:()=>x,stringifySupabaseSession:()=>S});var o=r(79428);new TextEncoder;let i=new TextDecoder;o.Buffer.isEncoding("base64url");let n=e=>o.Buffer.from(function(e){let t=e;return t instanceof Uint8Array&&(t=i.decode(t)),t}(e),"base64");var s=r(2492),a=Object.create,l=Object.defineProperty,u=Object.getOwnPropertyDescriptor,c=Object.getOwnPropertyNames,p=Object.getPrototypeOf,d=Object.prototype.hasOwnProperty,h=(e,t,r,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let i of c(t))d.call(e,i)||i===r||l(e,i,{get:()=>t[i],enumerable:!(o=u(t,i))||o.enumerable});return e},f=(e,t,r)=>(r=null!=e?a(p(e)):{},h(!t&&e&&e.__esModule?r:l(r,"default",{value:e,enumerable:!0}),e)),v=((e,t)=>function(){return t||(0,e[c(e)[0]])((t={exports:{}}).exports,t),t.exports})({"../../node_modules/.pnpm/cookie@0.5.0/node_modules/cookie/index.js"(e){e.parse=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");for(var r={},i=(t||{}).decode||o,n=0;n<e.length;){var s=e.indexOf("=",n);if(-1===s)break;var a=e.indexOf(";",n);if(-1===a)a=e.length;else if(a<s){n=e.lastIndexOf(";",s-1)+1;continue}var l=e.slice(n,s).trim();if(void 0===r[l]){var u=e.slice(s+1,a).trim();34===u.charCodeAt(0)&&(u=u.slice(1,-1)),r[l]=function(e,t){try{return t(e)}catch(t){return e}}(u,i)}n=a+1}return r},e.serialize=function(e,o,n){var s=n||{},a=s.encode||i;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!r.test(e))throw TypeError("argument name is invalid");var l=a(o);if(l&&!r.test(l))throw TypeError("argument val is invalid");var u=e+"="+l;if(null!=s.maxAge){var c=s.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");u+="; Max-Age="+Math.floor(c)}if(s.domain){if(!r.test(s.domain))throw TypeError("option domain is invalid");u+="; Domain="+s.domain}if(s.path){if(!r.test(s.path))throw TypeError("option path is invalid");u+="; Path="+s.path}if(s.expires){var p,d=s.expires;if(p=d,"[object Date]"!==t.call(p)&&!(p instanceof Date)||isNaN(d.valueOf()))throw TypeError("option expires is invalid");u+="; Expires="+d.toUTCString()}if(s.httpOnly&&(u+="; HttpOnly"),s.secure&&(u+="; Secure"),s.priority)switch("string"==typeof s.priority?s.priority.toLowerCase():s.priority){case"low":u+="; Priority=Low";break;case"medium":u+="; Priority=Medium";break;case"high":u+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":u+="; SameSite=Strict";break;case"lax":u+="; SameSite=Lax";break;case"none":u+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return u};var t=Object.prototype.toString,r=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;function o(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}function i(e){return encodeURIComponent(e)}}}),g=f(v()),C=f(v());function _(e){if(!e)return null;try{let t=JSON.parse(e);if(!t)return null;if("Object"===t.constructor.name)return t;if("Array"!==t.constructor.name)throw Error(`Unexpected format: ${t.constructor.name}`);let[r,o,i]=t[0].split("."),s=n(o),a=new TextDecoder,{exp:l,sub:u,...c}=JSON.parse(a.decode(s));return{expires_at:l,expires_in:l-Math.round(Date.now()/1e3),token_type:"bearer",access_token:t[0],refresh_token:t[1],provider_token:t[2],provider_refresh_token:t[3],user:{id:u,factors:t[4],...c}}}catch(e){return console.warn("Failed to parse cookie string:",e),null}}function S(e){var t;return JSON.stringify([e.access_token,e.refresh_token,e.provider_token,e.provider_refresh_token,(null==(t=e.user)?void 0:t.factors)??null])}function k(){return"undefined"!=typeof window&&void 0!==window.document}var A={path:"/",sameSite:"lax",maxAge:31536e6},b=RegExp(".{1,3180}","g"),m=class{constructor(e){this.cookieOptions={...A,...e,maxAge:A.maxAge}}getItem(e){let t=this.getCookie(e);if(e.endsWith("-code-verifier")&&t)return t;if(t)return JSON.stringify(_(t));let r=function(e,t=()=>null){let r=[];for(let o=0;;o++){let i=t(`${e}.${o}`);if(!i)break;r.push(i)}return r.length?r.join(""):null}(e,e=>this.getCookie(e));return null!==r?JSON.stringify(_(r)):null}setItem(e,t){if(e.endsWith("-code-verifier"))return void this.setCookie(e,t);(function(e,t,r){if(1===Math.ceil(t.length/((void 0)??3180)))return[{name:e,value:t}];let o=[],i=t.match(b);return null==i||i.forEach((t,r)=>{let i=`${e}.${r}`;o.push({name:i,value:t})}),o})(e,S(JSON.parse(t))).forEach(e=>{this.setCookie(e.name,e.value)})}removeItem(e){this._deleteSingleCookie(e),this._deleteChunkedCookies(e)}_deleteSingleCookie(e){this.getCookie(e)&&this.deleteCookie(e)}_deleteChunkedCookies(e,t=0){for(let r=t;;r++){let t=`${e}.${r}`;if(void 0===this.getCookie(t))break;this.deleteCookie(t)}}},E=class extends m{constructor(e){super(e)}getCookie(e){return k()?(0,g.parse)(document.cookie)[e]:null}setCookie(e,t){if(!k())return null;document.cookie=(0,g.serialize)(e,t,{...this.cookieOptions,httpOnly:!1})}deleteCookie(e){if(!k())return null;document.cookie=(0,g.serialize)(e,"",{...this.cookieOptions,maxAge:0,httpOnly:!1})}};function U(e,t,r){var o;let i=k();return(0,s.UU)(e,t,{...r,auth:{flowType:"pkce",autoRefreshToken:i,detectSessionInUrl:i,persistSession:!0,storage:r.auth.storage,...(null==(o=r.auth)?void 0:o.storageKey)?{storageKey:r.auth.storageKey}:{}}})}var y=C.parse,x=C.serialize},62518:(e,t,r)=>{var o,i=Object.defineProperty,n=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{createBrowserSupabaseClient:()=>x,createClientComponentClient:()=>c,createMiddlewareClient:()=>S,createMiddlewareSupabaseClient:()=>w,createPagesBrowserClient:()=>p,createPagesServerClient:()=>v,createRouteHandlerClient:()=>U,createServerActionClient:()=>y,createServerComponentClient:()=>b,createServerSupabaseClient:()=>P}),e.exports=((e,t,r,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let r of s(t))a.call(e,r)||void 0===r||i(e,r,{get:()=>t[r],enumerable:!(o=n(t,r))||o.enumerable});return e})(i({},"__esModule",{value:!0}),l);var u=r(45189);function c({supabaseUrl:e=process.env.NEXT_PUBLIC_SUPABASE_URL,supabaseKey:t=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,options:r,cookieOptions:i,isSingleton:n=!0}={}){if(!e||!t)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");let s=()=>{var o;return(0,u.createSupabaseClient)(e,t,{...r,global:{...null==r?void 0:r.global,headers:{...null==(o=null==r?void 0:r.global)?void 0:o.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.8.7"}},auth:{storage:new u.BrowserCookieAuthStorageAdapter(i)}})};if(n){let e=o??s();return"undefined"==typeof window?e:(o||(o=e),o)}return s()}var p=c,d=r(45189),h=r(2007),f=class extends d.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t,r,o;return(0,h.splitCookiesString)((null==(r=null==(t=this.context.res)?void 0:t.getHeader("set-cookie"))?void 0:r.toString())??"").map(t=>(0,d.parseCookies)(t)[e]).find(e=>!!e)??(null==(o=this.context.req)?void 0:o.cookies[e])}setCookie(e,t){this._setCookie(e,t)}deleteCookie(e){this._setCookie(e,"",{maxAge:0})}_setCookie(e,t,r){var o;let i=(0,h.splitCookiesString)((null==(o=this.context.res.getHeader("set-cookie"))?void 0:o.toString())??"").filter(t=>!(e in(0,d.parseCookies)(t))),n=(0,d.serializeCookie)(e,t,{...this.cookieOptions,...r,httpOnly:!1});this.context.res.setHeader("set-cookie",[...i,n])}};function v(e,{supabaseUrl:t=process.env.NEXT_PUBLIC_SUPABASE_URL,supabaseKey:r=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,options:o,cookieOptions:i}={}){var n;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,d.createSupabaseClient)(t,r,{...o,global:{...null==o?void 0:o.global,headers:{...null==(n=null==o?void 0:o.global)?void 0:n.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.8.7"}},auth:{storage:new f(e,i)}})}var g=r(45189),C=r(2007),_=class extends g.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;let r=(0,C.splitCookiesString)((null==(t=this.context.res.headers.get("set-cookie"))?void 0:t.toString())??"").map(t=>(0,g.parseCookies)(t)[e]).find(e=>!!e);return r||(0,g.parseCookies)(this.context.req.headers.get("cookie")??"")[e]}setCookie(e,t){this._setCookie(e,t)}deleteCookie(e){this._setCookie(e,"",{maxAge:0})}_setCookie(e,t,r){let o=(0,g.serializeCookie)(e,t,{...this.cookieOptions,...r,httpOnly:!1});this.context.res.headers&&this.context.res.headers.append("set-cookie",o)}};function S(e,{supabaseUrl:t=process.env.NEXT_PUBLIC_SUPABASE_URL,supabaseKey:r=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,options:o,cookieOptions:i}={}){var n;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,g.createSupabaseClient)(t,r,{...o,global:{...null==o?void 0:o.global,headers:{...null==(n=null==o?void 0:o.global)?void 0:n.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.8.7"}},auth:{storage:new _(e,i)}})}var k=r(45189),A=class extends k.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;return null==(t=this.context.cookies().get(e))?void 0:t.value}setCookie(e,t){}deleteCookie(e){}};function b(e,{supabaseUrl:t=process.env.NEXT_PUBLIC_SUPABASE_URL,supabaseKey:r=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,options:o,cookieOptions:i}={}){var n;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,k.createSupabaseClient)(t,r,{...o,global:{...null==o?void 0:o.global,headers:{...null==(n=null==o?void 0:o.global)?void 0:n.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.8.7"}},auth:{storage:new A(e,i)}})}var m=r(45189),E=class extends m.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;return null==(t=this.context.cookies().get(e))?void 0:t.value}setCookie(e,t){this.context.cookies().set(e,t,this.cookieOptions)}deleteCookie(e){this.context.cookies().set(e,"",{...this.cookieOptions,maxAge:0})}};function U(e,{supabaseUrl:t=process.env.NEXT_PUBLIC_SUPABASE_URL,supabaseKey:r=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,options:o,cookieOptions:i}={}){var n;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,m.createSupabaseClient)(t,r,{...o,global:{...null==o?void 0:o.global,headers:{...null==(n=null==o?void 0:o.global)?void 0:n.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.8.7"}},auth:{storage:new E(e,i)}})}var y=U;function x({supabaseUrl:e=process.env.NEXT_PUBLIC_SUPABASE_URL,supabaseKey:t=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,options:r,cookieOptions:o}={}){return console.warn("Please utilize the `createPagesBrowserClient` function instead of the deprecated `createBrowserSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages"),p({supabaseUrl:e,supabaseKey:t,options:r,cookieOptions:o})}function P(e,{supabaseUrl:t=process.env.NEXT_PUBLIC_SUPABASE_URL,supabaseKey:r=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,options:o,cookieOptions:i}={}){return console.warn("Please utilize the `createPagesServerClient` function instead of the deprecated `createServerSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages"),v(e,{supabaseUrl:t,supabaseKey:r,options:o,cookieOptions:i})}function w(e,{supabaseUrl:t=process.env.NEXT_PUBLIC_SUPABASE_URL,supabaseKey:r=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,options:o,cookieOptions:i}={}){return console.warn("Please utilize the `createMiddlewareClient` function instead of the deprecated `createMiddlewareSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs#middleware"),S(e,{supabaseUrl:t,supabaseKey:r,options:o,cookieOptions:i})}}};