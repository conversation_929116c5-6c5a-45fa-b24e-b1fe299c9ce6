'use client';

import { Metadata } from 'next';
import PageLayout from '@/components/layout/PageLayout';
import {
  BuildingOffice2Icon,
  UsersIcon,
  ShieldCheckIcon,
  GlobeAltIcon,
  HeartIcon,
  AcademicCapIcon
} from '@heroicons/react/24/outline';

export const metadata: Metadata = {
  title: 'Acerca de | Portal CHIA',
  description: 'Conoce más sobre el Portal Ciudadano Digital de Chía, nuestra misión, visión y compromiso con la transformación digital municipal.',
  keywords: 'acerca de, Portal CHIA, municipio Chía, transformación digital, gobierno digital',
};

const features = [
  {
    icon: ShieldCheckIcon,
    title: 'Seguridad y Confianza',
    description: 'Protegemos tus datos con los más altos estándares de seguridad digital y cumplimiento normativo.'
  },
  {
    icon: GlobeAltIcon,
    title: 'Acceso 24/7',
    description: 'Servicios disponibles las 24 horas del día, los 7 días de la semana, desde cualquier dispositivo.'
  },
  {
    icon: UsersIcon,
    title: 'Centrado en el Ciudadano',
    description: 'Diseñado pensando en las necesidades reales de los ciudadanos de Chía y su experiencia de usuario.'
  },
  {
    icon: AcademicCapIcon,
    title: 'Innovación Tecnológica',
    description: 'Utilizamos inteligencia artificial y tecnologías modernas para mejorar la atención ciudadana.'
  }
];

const stats = [
  { label: 'Servicios Digitales', value: '25+' },
  { label: 'Ciudadanos Registrados', value: '15,000+' },
  { label: 'Trámites Procesados', value: '50,000+' },
  { label: 'Satisfacción del Usuario', value: '95%' }
];

const timeline = [
  {
    year: '2023',
    title: 'Inicio del Proyecto',
    description: 'Planificación y diseño del Portal Ciudadano Digital de Chía'
  },
  {
    year: '2024',
    title: 'Desarrollo y Pruebas',
    description: 'Implementación de servicios digitales y pruebas con ciudadanos'
  },
  {
    year: '2025',
    title: 'Lanzamiento Oficial',
    description: 'Portal completamente operativo con todos los servicios municipales'
  }
];

export default function AcercaPage() {
  return (
    <PageLayout className="bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">
              Acerca del Portal CHIA
            </h1>
            <p className="text-xl text-primary-100 mb-8 max-w-3xl mx-auto">
              Transformando la relación entre ciudadanos y gobierno a través de la 
              innovación digital y la tecnología al servicio de la comunidad.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Mission & Vision */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          <div className="bg-white rounded-xl shadow-md p-8">
            <div className="flex items-center gap-3 mb-6">
              <HeartIcon className="h-8 w-8 text-primary-600" />
              <h2 className="text-3xl font-bold text-gray-900">Nuestra Misión</h2>
            </div>
            <p className="text-gray-600 text-lg leading-relaxed">
              Facilitar el acceso de los ciudadanos de Chía a los servicios municipales 
              mediante una plataforma digital segura, eficiente e inclusiva que promueva 
              la transparencia, reduzca los tiempos de gestión y mejore la calidad de vida 
              de nuestra comunidad.
            </p>
          </div>

          <div className="bg-white rounded-xl shadow-md p-8">
            <div className="flex items-center gap-3 mb-6">
              <BuildingOffice2Icon className="h-8 w-8 text-primary-600" />
              <h2 className="text-3xl font-bold text-gray-900">Nuestra Visión</h2>
            </div>
            <p className="text-gray-600 text-lg leading-relaxed">
              Ser el referente nacional en transformación digital municipal, 
              consolidando a Chía como una ciudad inteligente que utiliza la tecnología 
              para crear un gobierno más cercano, eficiente y transparente al servicio 
              de todos sus ciudadanos.
            </p>
          </div>
        </div>

        {/* Features */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              ¿Por qué elegir el Portal CHIA?
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Nuestro portal está diseñado con los más altos estándares de calidad, 
              seguridad y usabilidad para ofrecerte la mejor experiencia digital.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white rounded-xl shadow-md p-6 text-center">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <feature.icon className="h-8 w-8 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Stats */}
        <div className="bg-primary-600 rounded-xl text-white p-8 mb-16">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold mb-4">
              Nuestro Impacto en Números
            </h2>
            <p className="text-primary-100">
              Datos que reflejan nuestro compromiso con la excelencia en el servicio
            </p>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl font-bold mb-2">{stat.value}</div>
                <div className="text-primary-100">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Timeline */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Nuestro Camino hacia la Transformación Digital
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Un recorrido de innovación y mejora continua en el servicio a la ciudadanía
            </p>
          </div>

          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-primary-200"></div>
            
            <div className="space-y-12">
              {timeline.map((item, index) => (
                <div key={index} className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}>
                  <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                    <div className="bg-white rounded-xl shadow-md p-6">
                      <div className="text-2xl font-bold text-primary-600 mb-2">
                        {item.year}
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        {item.title}
                      </h3>
                      <p className="text-gray-600">
                        {item.description}
                      </p>
                    </div>
                  </div>
                  
                  <div className="relative z-10">
                    <div className="w-4 h-4 bg-primary-600 rounded-full border-4 border-white shadow-lg"></div>
                  </div>
                  
                  <div className="w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Team & Contact */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          <div className="bg-white rounded-xl shadow-md p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              Nuestro Equipo
            </h2>
            <p className="text-gray-600 mb-6">
              El Portal CHIA es el resultado del trabajo conjunto entre funcionarios 
              municipales, desarrolladores especializados y expertos en experiencia 
              de usuario, todos comprometidos con la excelencia en el servicio público.
            </p>
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                <span className="text-gray-700">Equipo de Desarrollo Tecnológico</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                <span className="text-gray-700">Especialistas en Experiencia de Usuario</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                <span className="text-gray-700">Funcionarios Municipales</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                <span className="text-gray-700">Expertos en Seguridad Digital</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-md p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              Comprometidos con la Mejora Continua
            </h2>
            <p className="text-gray-600 mb-6">
              Tu opinión es fundamental para nosotros. Trabajamos constantemente 
              en mejorar nuestros servicios basándonos en las necesidades y 
              sugerencias de la ciudadanía.
            </p>
            <div className="space-y-4">
              <a
                href="/contacto"
                className="block w-full text-center bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              >
                Contáctanos
              </a>
              <a
                href="/chat"
                className="block w-full text-center bg-white hover:bg-gray-50 text-primary-600 border border-primary-600 px-6 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              >
                Habla con nuestro Asistente IA
              </a>
            </div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
