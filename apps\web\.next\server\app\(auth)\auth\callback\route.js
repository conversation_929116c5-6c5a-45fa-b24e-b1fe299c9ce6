(()=>{var e={};e.id=495,e.ids=[495],e.modules={1241:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},24768:(e,r,t)=>{"use strict";t.d(r,{y8:()=>n});var s=t(2492),u=t(62518),a=t(65208);let i=process.env.NEXT_PUBLIC_SUPABASE_URL,o=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;if(!i||!o)throw Error("Missing Supabase environment variables");let n=()=>{let e=(0,a.UL)();return(0,u.createServerComponentClient)({cookies:()=>e})};(0,s.UU)(i,o)},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57252:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>c});var u=t(48106),a=t(48819),i=t(12050),o=t(24768),n=t(4235);async function c(e){let r=new URL(e.url),t=r.searchParams.get("code");if(t){let e=(0,o.y8)(),{error:s}=await e.auth.exchangeCodeForSession(t);if(s)return console.error("Auth callback error:",s),n.NextResponse.redirect(`${r.origin}/auth/login?error=callback_error`)}return n.NextResponse.redirect(`${r.origin}/dashboard`)}let p=new u.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/(auth)/auth/callback/route",pathname:"/auth/callback",filename:"route",bundlePath:"app/(auth)/auth/callback/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\(auth)\\auth\\callback\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:x}=p;function h(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72088:()=>{},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80408:()=>{},81630:e=>{"use strict";e.exports=require("http")},87032:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[363,525,744],()=>t(57252));module.exports=s})();