
> @chia/web@1.0.0 build
> next build

   ▲ Next.js 15.3.5

   Creating an optimized production build ...
Failed to compile.

./app/acerca/page.tsx
Error:   [31mx[0m You are attempting to export "metadata" from a component marked with "use client", which is disallowed. Either remove the export, or the "use client" directive. Read more: https://nextjs.org/docs/app/api-reference/directives/use-client
  [31m|[0m

    ,-[[36;1;4mC:\Users\<USER>\Documents\augment-projects\chia-next\apps\web\app\acerca\page.tsx[0m:14:1]
 [2m11[0m |   AcademicCapIcon
 [2m12[0m | } from '@heroicons/react/24/outline';
 [2m13[0m | 
 [2m14[0m | export const metadata: Metadata = {
    : [35;1m             ^^^^^^^^[0m
 [2m15[0m |   title: 'Acerca de | Portal CHIA',
 [2m16[0m |   description: 'Conoce más sobre el Portal Ciudadano Digital de Chía, nuestra misión, visión y compromiso con la transformación digital municipal.',
 [2m17[0m |   keywords: 'acerca de, Portal CHIA, municipio Chía, transformación digital, gobierno digital',
    `----

Import trace for requested module:
./app/acerca/page.tsx

./app/chat/page.tsx
Error:   [31mx[0m You are attempting to export "metadata" from a component marked with "use client", which is disallowed. Either remove the export, or the "use client" directive. Read more: https://nextjs.org/docs/app/api-reference/directives/use-client
  [31m|[0m

    ,-[[36;1;4mC:\Users\<USER>\Documents\augment-projects\chia-next\apps\web\app\chat\page.tsx[0m:7:1]
 [2m 4[0m | import PageLayout from '@/components/layout/PageLayout';
 [2m 5[0m | import ChatInterface from '@/components/chat/ChatInterface';
 [2m 6[0m | 
 [2m 7[0m | export const metadata: Metadata = {
    : [35;1m             ^^^^^^^^[0m
 [2m 8[0m |   title: 'Asistente IA | Portal CHIA',
 [2m 9[0m |   description: 'Chatea con nuestro asistente de inteligencia artificial para obtener ayuda con servicios municipales, información y trámites.',
 [2m10[0m |   keywords: 'asistente IA, chat, ayuda, servicios municipales, Chía',
    `----

Import trace for requested module:
./app/chat/page.tsx

./app/contacto/page.tsx
Error:   [31mx[0m You are attempting to export "metadata" from a component marked with "use client", which is disallowed. Either remove the export, or the "use client" directive. Read more: https://nextjs.org/docs/app/api-reference/directives/use-client
  [31m|[0m

    ,-[[36;1;4mC:\Users\<USER>\Documents\augment-projects\chia-next\apps\web\app\contacto\page.tsx[0m:14:1]
 [2m11[0m |   ExclamationTriangleIcon
 [2m12[0m | } from '@heroicons/react/24/outline';
 [2m13[0m | 
 [2m14[0m | export const metadata: Metadata = {
    : [35;1m             ^^^^^^^^[0m
 [2m15[0m |   title: 'Contacto | Portal CHIA',
 [2m16[0m |   description: 'Contacta con la Alcaldía de Chía. Encuentra información de contacto, horarios de atención, ubicación y canales de comunicación.',
 [2m17[0m |   keywords: 'contacto, alcaldía Chía, teléfono, dirección, horarios atención',
    `----

Import trace for requested module:
./app/contacto/page.tsx

./app/servicios/certificados/page.tsx
Error:   [31mx[0m You are attempting to export "metadata" from a component marked with "use client", which is disallowed. Either remove the export, or the "use client" directive. Read more: https://nextjs.org/docs/app/api-reference/directives/use-client
  [31m|[0m

    ,-[[36;1;4mC:\Users\<USER>\Documents\augment-projects\chia-next\apps\web\app\servicios\certificados\page.tsx[0m:14:1]
 [2m11[0m |   ArrowRightIcon
 [2m12[0m | } from '@heroicons/react/24/outline';
 [2m13[0m | 
 [2m14[0m | export const metadata: Metadata = {
    : [35;1m             ^^^^^^^^[0m
 [2m15[0m |   title: 'Certificados | Portal CHIA',
 [2m16[0m |   description: 'Solicita certificados de residencia, nacimiento, defunción y otros documentos oficiales del municipio de Chía.',
 [2m17[0m |   keywords: 'certificados, residencia, nacimiento, defunción, documentos oficiales, Chía',
    `----

Import trace for requested module:
./app/servicios/certificados/page.tsx

./app/servicios/page.tsx
Error:   [31mx[0m You are attempting to export "metadata" from a component marked with "use client", which is disallowed. Either remove the export, or the "use client" directive. Read more: https://nextjs.org/docs/app/api-reference/directives/use-client
  [31m|[0m

    ,-[[36;1;4mC:\Users\<USER>\Documents\augment-projects\chia-next\apps\web\app\servicios\page.tsx[0m:16:1]
 [2m13[0m |   StarIcon
 [2m14[0m | } from '@heroicons/react/24/outline';
 [2m15[0m | 
 [2m16[0m | export const metadata: Metadata = {
    : [35;1m             ^^^^^^^^[0m
 [2m17[0m |   title: 'Servicios Ciudadanos | Portal CHIA',
 [2m18[0m |   description: 'Accede a todos los servicios digitales del municipio de Chía. Certificados, pagos, licencias y más.',
 [2m19[0m |   keywords: 'servicios municipales, certificados, pagos en línea, licencias, Chía',
    `----

Import trace for requested module:
./app/servicios/page.tsx


> Build failed because of webpack errors
npm error Lifecycle script `build` failed with error:
npm error code 1
npm error path C:\Users\<USER>\Documents\augment-projects\chia-next\apps\web
npm error workspace @chia/web@1.0.0
npm error location C:\Users\<USER>\Documents\augment-projects\chia-next\apps\web
npm error command failed
npm error command C:\WINDOWS\system32\cmd.exe /d /s /c next build
