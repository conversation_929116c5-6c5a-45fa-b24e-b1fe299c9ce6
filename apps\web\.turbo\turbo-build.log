
> @chia/web@1.0.0 build
> next build

   ▲ Next.js 15.3.5

   Creating an optimized production build ...
Failed to compile.

./lib/supabase.ts
Error:   [31mx[0m You're importing a component that needs "next/headers". That only works in a Server Component which is not supported in the pages/ directory. Read more: https://nextjs.org/docs/app/building-your-application/rendering/server-components
  [31m|[0m

   ,-[[36;1;4mC:\Users\<USER>\Documents\augment-projects\chia-next\apps\web\lib\supabase.ts[0m:3:1]
 [2m1[0m | import { createClient } from '@supabase/supabase-js';
 [2m2[0m | import { createClientComponentClient, createServerComponentClient } from '@supabase/auth-helpers-nextjs';
 [2m3[0m | import { cookies } from 'next/headers';
   : [35;1m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[0m
 [2m4[0m | 
 [2m5[0m | // Environment variables validation
 [2m6[0m | const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
   `----

Import trace for requested module:
./lib/supabase.ts
./components/auth/LoginForm.tsx

./app/(auth)/login/login-client.tsx + 28 modules
Unexpected end of JSON input

./app/(auth)/verify-email/page.tsx + 3 modules
Unexpected end of JSON input

./app/page.tsx + 10 modules
Unexpected end of JSON input

./components/chat/ChatInterface.tsx + 1 modules
Unexpected end of JSON input


> Build failed because of webpack errors
npm error Lifecycle script `build` failed with error:
npm error code 1
npm error path C:\Users\<USER>\Documents\augment-projects\chia-next\apps\web
npm error workspace @chia/web@1.0.0
npm error location C:\Users\<USER>\Documents\augment-projects\chia-next\apps\web
npm error command failed
npm error command C:\WINDOWS\system32\cmd.exe /d /s /c next build
