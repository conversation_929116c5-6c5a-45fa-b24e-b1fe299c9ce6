"use strict";exports.id=111,exports.ids=[111],exports.modules={2492:(e,a,p)=>{p.d(a,{UU:()=>aI});var d,t,i,s,r,l,o,n,m,h,u,c={};p.r(c),p.d(c,{Receiver:()=>V,Sender:()=>O,WebSocket:()=>C,WebSocketServer:()=>A,createWebSocketStream:()=>D,default:()=>x});let v=e=>{let a;return a=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(p.t.bind(p,83899,23)).then(({default:a})=>a(...e)):fetch),(...e)=>a(...e)};class f extends Error{constructor(e,a="FunctionsError",p){super(e),this.name=a,this.context=p}}class w extends f{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class _ extends f{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class g extends f{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}!function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"}(d||(d={}));class y{constructor(e,{headers:a={},customFetch:p,region:t=d.Any}={}){this.url=e,this.headers=a,this.region=t,this.fetch=v(p)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,a={}){var p,d,t,i,s;return d=this,t=void 0,i=void 0,s=function*(){try{let d,t,{headers:i,method:s,body:r}=a,l={},{region:o}=a;o||(o=this.region);let n=new URL(`${this.url}/${e}`);o&&"any"!==o&&(l["x-region"]=o,n.searchParams.set("forceFunctionRegion",o)),r&&(i&&!Object.prototype.hasOwnProperty.call(i,"Content-Type")||!i)&&("undefined"!=typeof Blob&&r instanceof Blob||r instanceof ArrayBuffer?(l["Content-Type"]="application/octet-stream",d=r):"string"==typeof r?(l["Content-Type"]="text/plain",d=r):"undefined"!=typeof FormData&&r instanceof FormData?d=r:(l["Content-Type"]="application/json",d=JSON.stringify(r)));let m=yield this.fetch(n.toString(),{method:s||"POST",headers:Object.assign(Object.assign(Object.assign({},l),this.headers),i),body:d}).catch(e=>{throw new w(e)}),h=m.headers.get("x-relay-error");if(h&&"true"===h)throw new _(m);if(!m.ok)throw new g(m);let u=(null!=(p=m.headers.get("Content-Type"))?p:"text/plain").split(";")[0].trim();return{data:"application/json"===u?yield m.json():"application/octet-stream"===u?yield m.blob():"text/event-stream"===u?m:"multipart/form-data"===u?yield m.formData():yield m.text(),error:null,response:m}}catch(e){return{data:null,error:e,response:e instanceof g||e instanceof _?e.context:void 0}}},new(i||(i=Promise))(function(e,a){function p(e){try{l(s.next(e))}catch(e){a(e)}}function r(e){try{l(s.throw(e))}catch(e){a(e)}}function l(a){var d;a.done?e(a.value):((d=a.value)instanceof i?d:new i(function(e){e(d)})).then(p,r)}l((s=s.apply(d,t||[])).next())})}}let{PostgrestClient:b,PostgrestQueryBuilder:S,PostgrestFilterBuilder:T,PostgrestTransformBuilder:N,PostgrestBuilder:k,PostgrestError:E}=p(44059);var D=p(90939),V=p(36122),O=p(8630),C=p(97628),A=p(67438);let x=C,P=(()=>{try{if("undefined"!=typeof WebSocket)return WebSocket;if(void 0!==global.WebSocket)return global.WebSocket;if(void 0!==window.WebSocket)return window.WebSocket;if(void 0!==self.WebSocket)return self.WebSocket;throw Error("`WebSocket` is not supported in this environment")}catch{if(C)return C;return c}})();!function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"}(t||(t={})),function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"}(i||(i={})),function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"}(s||(s={})),(r||(r={})).websocket="websocket",function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"}(l||(l={}));class j{constructor(){this.HEADER_LENGTH=1}decode(e,a){return e.constructor===ArrayBuffer?a(this._binaryDecode(e)):"string"==typeof e?a(JSON.parse(e)):a({})}_binaryDecode(e){let a=new DataView(e),p=new TextDecoder;return this._decodeBroadcast(e,a,p)}_decodeBroadcast(e,a,p){let d=a.getUint8(1),t=a.getUint8(2),i=this.HEADER_LENGTH+2,s=p.decode(e.slice(i,i+d));i+=d;let r=p.decode(e.slice(i,i+t));return i+=t,{ref:null,topic:s,event:r,payload:JSON.parse(p.decode(e.slice(i,e.byteLength)))}}}class R{constructor(e,a){this.callback=e,this.timerCalc=a,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=a}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}!function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"}(o||(o={}));let L=(e,a,p={})=>{var d;let t=null!=(d=p.skipTypes)?d:[];return Object.keys(a).reduce((p,d)=>(p[d]=B(d,e,a,t),p),{})},B=(e,a,p,d)=>{let t=a.find(a=>a.name===e),i=null==t?void 0:t.type,s=p[e];return i&&!d.includes(i)?I(i,s):$(s)},I=(e,a)=>{if("_"===e.charAt(0))return q(a,e.slice(1,e.length));switch(e){case o.bool:return U(a);case o.float4:case o.float8:case o.int2:case o.int4:case o.int8:case o.numeric:case o.oid:return F(a);case o.json:case o.jsonb:return M(a);case o.timestamp:return W(a);case o.abstime:case o.date:case o.daterange:case o.int4range:case o.int8range:case o.money:case o.reltime:case o.text:case o.time:case o.timestamptz:case o.timetz:case o.tsrange:case o.tstzrange:default:return $(a)}},$=e=>e,U=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},F=e=>{if("string"==typeof e){let a=parseFloat(e);if(!Number.isNaN(a))return a}return e},M=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){console.log(`JSON parse error: ${e}`)}return e},q=(e,a)=>{if("string"!=typeof e)return e;let p=e.length-1,d=e[p];if("{"===e[0]&&"}"===d){let d,t=e.slice(1,p);try{d=JSON.parse("["+t+"]")}catch(e){d=t?t.split(","):[]}return d.map(e=>I(a,e))}return e},W=e=>"string"==typeof e?e.replace(" ","T"):e,z=e=>{let a=e;return(a=(a=a.replace(/^ws/i,"http")).replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,"")).replace(/\/+$/,"")};class G{constructor(e,a,p={},d=1e4){this.channel=e,this.event=a,this.payload=p,this.timeout=d,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,a){var p;return this._hasReceived(e)&&a(null==(p=this.receivedResp)?void 0:p.response),this.recHooks.push({status:e,callback:a}),this}startTimeout(){this.timeoutTimer||(this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref),this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout))}trigger(e,a){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:a})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:a}){this.recHooks.filter(a=>a.status===e).forEach(e=>e.callback(a))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}!function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"}(n||(n={}));class H{constructor(e,a){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};let p=(null==a?void 0:a.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(p.state,{},e=>{let{onJoin:a,onLeave:p,onSync:d}=this.caller;this.joinRef=this.channel._joinRef(),this.state=H.syncState(this.state,e,a,p),this.pendingDiffs.forEach(e=>{this.state=H.syncDiff(this.state,e,a,p)}),this.pendingDiffs=[],d()}),this.channel._on(p.diff,{},e=>{let{onJoin:a,onLeave:p,onSync:d}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=H.syncDiff(this.state,e,a,p),d())}),this.onJoin((e,a,p)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:a,newPresences:p})}),this.onLeave((e,a,p)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:a,leftPresences:p})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,a,p,d){let t=this.cloneDeep(e),i=this.transformState(a),s={},r={};return this.map(t,(e,a)=>{i[e]||(r[e]=a)}),this.map(i,(e,a)=>{let p=t[e];if(p){let d=a.map(e=>e.presence_ref),t=p.map(e=>e.presence_ref),i=a.filter(e=>0>t.indexOf(e.presence_ref)),l=p.filter(e=>0>d.indexOf(e.presence_ref));i.length>0&&(s[e]=i),l.length>0&&(r[e]=l)}else s[e]=a}),this.syncDiff(t,{joins:s,leaves:r},p,d)}static syncDiff(e,a,p,d){let{joins:t,leaves:i}={joins:this.transformState(a.joins),leaves:this.transformState(a.leaves)};return p||(p=()=>{}),d||(d=()=>{}),this.map(t,(a,d)=>{var t;let i=null!=(t=e[a])?t:[];if(e[a]=this.cloneDeep(d),i.length>0){let p=e[a].map(e=>e.presence_ref),d=i.filter(e=>0>p.indexOf(e.presence_ref));e[a].unshift(...d)}p(a,i,d)}),this.map(i,(a,p)=>{let t=e[a];if(!t)return;let i=p.map(e=>e.presence_ref);t=t.filter(e=>0>i.indexOf(e.presence_ref)),e[a]=t,d(a,t,p),0===t.length&&delete e[a]}),e}static map(e,a){return Object.getOwnPropertyNames(e).map(p=>a(p,e[p]))}static transformState(e){return Object.getOwnPropertyNames(e=this.cloneDeep(e)).reduce((a,p)=>{let d=e[p];return"metas"in d?a[p]=d.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):a[p]=d,a},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}!function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"}(m||(m={})),function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"}(h||(h={})),function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"}(u||(u={}));class K{constructor(e,a={config:{}},p){this.topic=e,this.params=a,this.socket=p,this.bindings={},this.state=i.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},a.config),this.timeout=this.socket.timeout,this.joinPush=new G(this,s.join,this.params,this.timeout),this.rejoinTimer=new R(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=i.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=i.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=i.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=i.errored,this.rejoinTimer.scheduleTimeout())}),this._on(s.reply,{},(e,a)=>{this._trigger(this._replyEventName(a),e)}),this.presence=new H(this),this.broadcastEndpointURL=z(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,a=this.timeout){var p,d;if(this.socket.isConnected()||this.socket.connect(),this.state==i.closed){let{config:{broadcast:t,presence:s,private:r}}=this.params;this._onError(a=>null==e?void 0:e(u.CHANNEL_ERROR,a)),this._onClose(()=>null==e?void 0:e(u.CLOSED));let l={},o={broadcast:t,presence:s,postgres_changes:null!=(d=null==(p=this.bindings.postgres_changes)?void 0:p.map(e=>e.filter))?d:[],private:r};this.socket.accessTokenValue&&(l.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:o},l)),this.joinedOnce=!0,this._rejoin(a),this.joinPush.receive("ok",async({postgres_changes:a})=>{var p;if(this.socket.setAuth(),void 0===a){null==e||e(u.SUBSCRIBED);return}{let d=this.bindings.postgres_changes,t=null!=(p=null==d?void 0:d.length)?p:0,s=[];for(let p=0;p<t;p++){let t=d[p],{filter:{event:r,schema:l,table:o,filter:n}}=t,m=a&&a[p];if(m&&m.event===r&&m.schema===l&&m.table===o&&m.filter===n)s.push(Object.assign(Object.assign({},t),{id:m.id}));else{this.unsubscribe(),this.state=i.errored,null==e||e(u.CHANNEL_ERROR,Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=s,e&&e(u.SUBSCRIBED);return}}).receive("error",a=>{this.state=i.errored,null==e||e(u.CHANNEL_ERROR,Error(JSON.stringify(Object.values(a).join(", ")||"error")))}).receive("timeout",()=>{null==e||e(u.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,a={}){return await this.send({type:"presence",event:"track",payload:e},a.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,a,p){return this._on(e,a,p)}async send(e,a={}){var p,d;if(this._canPush()||"broadcast"!==e.type)return new Promise(p=>{var d,t,i;let s=this._push(e.type,e,a.timeout||this.timeout);"broadcast"!==e.type||(null==(i=null==(t=null==(d=this.params)?void 0:d.config)?void 0:t.broadcast)?void 0:i.ack)||p("ok"),s.receive("ok",()=>p("ok")),s.receive("error",()=>p("error")),s.receive("timeout",()=>p("timed out"))});{let{event:t,payload:i}=e,s={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:t,payload:i,private:this.private}]})};try{let e=await this._fetchWithTimeout(this.broadcastEndpointURL,s,null!=(p=a.timeout)?p:this.timeout);return await (null==(d=e.body)?void 0:d.cancel()),e.ok?"ok":"error"}catch(e){if("AbortError"===e.name)return"timed out";return"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=i.leaving;let a=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(s.close,"leave",this._joinRef())};this.joinPush.destroy();let p=null;return new Promise(d=>{(p=new G(this,s.leave,{},e)).receive("ok",()=>{a(),d("ok")}).receive("timeout",()=>{a(),d("timed out")}).receive("error",()=>{d("error")}),p.send(),this._canPush()||p.trigger("ok",{})}).finally(()=>{null==p||p.destroy()})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(e,a,p){let d=new AbortController,t=setTimeout(()=>d.abort(),p),i=await this.socket.fetch(e,Object.assign(Object.assign({},a),{signal:d.signal}));return clearTimeout(t),i}_push(e,a,p=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let d=new G(this,e,a,p);return this._canPush()?d.send():(d.startTimeout(),this.pushBuffer.push(d)),d}_onMessage(e,a,p){return a}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,a,p){var d,t;let i=e.toLocaleLowerCase(),{close:r,error:l,leave:o,join:n}=s;if(p&&[r,l,o,n].indexOf(i)>=0&&p!==this._joinRef())return;let m=this._onMessage(i,a,p);if(a&&!m)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(i)?null==(d=this.bindings.postgres_changes)||d.filter(e=>{var a,p,d;return(null==(a=e.filter)?void 0:a.event)==="*"||(null==(d=null==(p=e.filter)?void 0:p.event)?void 0:d.toLocaleLowerCase())===i}).map(e=>e.callback(m,p)):null==(t=this.bindings[i])||t.filter(e=>{var p,d,t,s,r,l;if(!["broadcast","presence","postgres_changes"].includes(i))return e.type.toLocaleLowerCase()===i;if("id"in e){let i=e.id,s=null==(p=e.filter)?void 0:p.event;return i&&(null==(d=a.ids)?void 0:d.includes(i))&&("*"===s||(null==s?void 0:s.toLocaleLowerCase())===(null==(t=a.data)?void 0:t.type.toLocaleLowerCase()))}{let p=null==(r=null==(s=null==e?void 0:e.filter)?void 0:s.event)?void 0:r.toLocaleLowerCase();return"*"===p||p===(null==(l=null==a?void 0:a.event)?void 0:l.toLocaleLowerCase())}}).map(e=>{if("object"==typeof m&&"ids"in m){let e=m.data,{schema:a,table:p,commit_timestamp:d,type:t,errors:i}=e;m=Object.assign(Object.assign({},{schema:a,table:p,commit_timestamp:d,eventType:t,new:{},old:{},errors:i}),this._getPayloadRecords(e))}e.callback(m,p)})}_isClosed(){return this.state===i.closed}_isJoined(){return this.state===i.joined}_isJoining(){return this.state===i.joining}_isLeaving(){return this.state===i.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,a,p){let d=e.toLocaleLowerCase(),t={type:d,filter:a,callback:p};return this.bindings[d]?this.bindings[d].push(t):this.bindings[d]=[t],this}_off(e,a){let p=e.toLocaleLowerCase();return this.bindings[p]=this.bindings[p].filter(e=>{var d;return!((null==(d=e.type)?void 0:d.toLocaleLowerCase())===p&&K.isEqual(e.filter,a))}),this}static isEqual(e,a){if(Object.keys(e).length!==Object.keys(a).length)return!1;for(let p in e)if(e[p]!==a[p])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(s.close,{},e)}_onError(e){this._on(s.error,{},a=>e(a))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=i.joining,this.joinPush.resend(e))}_getPayloadRecords(e){let a={new:{},old:{}};return("INSERT"===e.type||"UPDATE"===e.type)&&(a.new=L(e.columns,e.record)),("UPDATE"===e.type||"DELETE"===e.type)&&(a.old=L(e.columns,e.old_record)),a}}let J=()=>{},Y=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class X{constructor(e,a){var d;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=1e4,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=J,this.ref=0,this.logger=J,this.conn=null,this.sendBuffer=[],this.serializer=new j,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let a;return a=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(p.t.bind(p,83899,23)).then(({default:a})=>a(...e)):fetch),(...e)=>a(...e)},this.endPoint=`${e}/${r.websocket}`,this.httpEndpoint=z(e),(null==a?void 0:a.transport)?this.transport=a.transport:this.transport=null,(null==a?void 0:a.params)&&(this.params=a.params),(null==a?void 0:a.timeout)&&(this.timeout=a.timeout),(null==a?void 0:a.logger)&&(this.logger=a.logger),((null==a?void 0:a.logLevel)||(null==a?void 0:a.log_level))&&(this.logLevel=a.logLevel||a.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),(null==a?void 0:a.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=a.heartbeatIntervalMs);let t=null==(d=null==a?void 0:a.params)?void 0:d.apikey;if(t&&(this.accessTokenValue=t,this.apiKey=t),this.reconnectAfterMs=(null==a?void 0:a.reconnectAfterMs)?a.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==a?void 0:a.encode)?a.encode:(e,a)=>a(JSON.stringify(e)),this.decode=(null==a?void 0:a.decode)?a.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new R(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null==a?void 0:a.fetch),null==a?void 0:a.worker){if("undefined"!=typeof window&&!window.Worker)throw Error("Web Worker is not supported");this.worker=(null==a?void 0:a.worker)||!1,this.workerUrl=null==a?void 0:a.workerUrl}this.accessToken=(null==a?void 0:a.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=P),!this.transport)throw Error("No transport provided");this.conn=new this.transport(this.endpointURL()),this.setupConnection()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,a){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=a?a:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(e=>e.teardown()))}getChannels(){return this.channels}async removeChannel(e){let a=await e.unsubscribe();return 0===this.channels.length&&this.disconnect(),a}async removeAllChannels(){let e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.channels=[],this.disconnect(),e}log(e,a,p){this.logger(e,a,p)}connectionState(){switch(this.conn&&this.conn.readyState){case t.connecting:return l.Connecting;case t.open:return l.Open;case t.closing:return l.Closing;default:return l.Closed}}isConnected(){return this.connectionState()===l.Open}channel(e,a={config:{}}){let p=`realtime:${e}`,d=this.getChannels().find(e=>e.topic===p);if(d)return d;{let p=new K(`realtime:${e}`,a,this);return this.channels.push(p),p}}push(e){let{topic:a,event:p,payload:d,ref:t}=e,i=()=>{this.encode(e,e=>{var a;null==(a=this.conn)||a.send(e)})};this.log("push",`${a} ${p} (${t})`,d),this.isConnected()?i():this.sendBuffer.push(i)}async setAuth(e=null){let a=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=a&&(this.accessTokenValue=a,this.channels.forEach(e=>{a&&e.updateJoinPayload({access_token:a,version:"realtime-js/2.11.15"}),e.joinedOnce&&e._isJoined()&&e._push(s.access_token,{access_token:a})}))}async sendHeartbeat(){var e;if(!this.isConnected())return void this.heartbeatCallback("disconnected");if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),null==(e=this.conn)||e.close(1e3,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let a=this.channels.find(a=>a.topic===e&&(a._isJoined()||a._isJoining()));a&&(this.log("transport",`leaving duplicate topic "${e}"`),a.unsubscribe())}_remove(e){this.channels=this.channels.filter(a=>a.topic!==e.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,e=>{let{topic:a,event:p,payload:d,ref:t}=e;"phoenix"===a&&"phx_reply"===p&&this.heartbeatCallback("ok"==e.payload.status?"ok":"error"),t&&t===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${d.status||""} ${a} ${p} ${t&&"("+t+")"||""}`,d),Array.from(this.channels).filter(e=>e._isMember(a)).forEach(e=>e._trigger(p,d,t)),this.stateChangeCallbacks.message.forEach(a=>a(e))})}_onConnOpen(){this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this.stateChangeCallbacks.open.forEach(e=>e())}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");let e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(a=>a(e))}_onConnError(e){this.log("transport",`${e}`),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(a=>a(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(s.error))}_appendParams(e,a){if(0===Object.keys(a).length)return e;let p=e.match(/\?/)?"&":"?",d=new URLSearchParams(a);return`${e}${p}${d}`}_workerObjectUrl(e){let a;if(e)a=e;else{let e=new Blob([Y],{type:"application/javascript"});a=URL.createObjectURL(e)}return a}}class Z extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function Q(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class ee extends Z{constructor(e,a){super(e),this.name="StorageApiError",this.status=a}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class ea extends Z{constructor(e,a){super(e),this.name="StorageUnknownError",this.originalError=a}}let ep=e=>{let a;return a=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(p.t.bind(p,83899,23)).then(({default:a})=>a(...e)):fetch),(...e)=>a(...e)},ed=()=>(function(e,a,p,d){return new(p||(p=Promise))(function(t,i){function s(e){try{l(d.next(e))}catch(e){i(e)}}function r(e){try{l(d.throw(e))}catch(e){i(e)}}function l(e){var a;e.done?t(e.value):((a=e.value)instanceof p?a:new p(function(e){e(a)})).then(s,r)}l((d=d.apply(e,a||[])).next())})})(void 0,void 0,void 0,function*(){return"undefined"==typeof Response?(yield Promise.resolve().then(p.t.bind(p,83899,23))).Response:Response}),et=e=>{if(Array.isArray(e))return e.map(e=>et(e));if("function"==typeof e||e!==Object(e))return e;let a={};return Object.entries(e).forEach(([e,p])=>{a[e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""))]=et(p)}),a};var ei=function(e,a,p,d){return new(p||(p=Promise))(function(t,i){function s(e){try{l(d.next(e))}catch(e){i(e)}}function r(e){try{l(d.throw(e))}catch(e){i(e)}}function l(e){var a;e.done?t(e.value):((a=e.value)instanceof p?a:new p(function(e){e(a)})).then(s,r)}l((d=d.apply(e,a||[])).next())})};let es=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),er=(e,a,p)=>ei(void 0,void 0,void 0,function*(){e instanceof(yield ed())&&!(null==p?void 0:p.noResolveJson)?e.json().then(p=>{a(new ee(es(p),e.status||500))}).catch(e=>{a(new ea(es(e),e))}):a(new ea(es(e),e))}),el=(e,a,p,d)=>{let t={method:e,headers:(null==a?void 0:a.headers)||{}};return"GET"===e?t:(t.headers=Object.assign({"Content-Type":"application/json"},null==a?void 0:a.headers),d&&(t.body=JSON.stringify(d)),Object.assign(Object.assign({},t),p))};function eo(e,a,p,d,t,i){return ei(this,void 0,void 0,function*(){return new Promise((s,r)=>{e(p,el(a,d,t,i)).then(e=>{if(!e.ok)throw e;return(null==d?void 0:d.noResolveJson)?e:e.json()}).then(e=>s(e)).catch(e=>er(e,r,d))})})}function en(e,a,p,d){return ei(this,void 0,void 0,function*(){return eo(e,"GET",a,p,d)})}function em(e,a,p,d,t){return ei(this,void 0,void 0,function*(){return eo(e,"POST",a,d,t,p)})}function eh(e,a,p,d,t){return ei(this,void 0,void 0,function*(){return eo(e,"DELETE",a,d,t,p)})}var eu=function(e,a,p,d){return new(p||(p=Promise))(function(t,i){function s(e){try{l(d.next(e))}catch(e){i(e)}}function r(e){try{l(d.throw(e))}catch(e){i(e)}}function l(e){var a;e.done?t(e.value):((a=e.value)instanceof p?a:new p(function(e){e(a)})).then(s,r)}l((d=d.apply(e,a||[])).next())})};let ec={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},ev={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class ef{constructor(e,a={},p,d){this.url=e,this.headers=a,this.bucketId=p,this.fetch=ep(d)}uploadOrUpdate(e,a,p,d){return eu(this,void 0,void 0,function*(){try{let t,i=Object.assign(Object.assign({},ev),d),s=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(i.upsert)}),r=i.metadata;"undefined"!=typeof Blob&&p instanceof Blob?((t=new FormData).append("cacheControl",i.cacheControl),r&&t.append("metadata",this.encodeMetadata(r)),t.append("",p)):"undefined"!=typeof FormData&&p instanceof FormData?((t=p).append("cacheControl",i.cacheControl),r&&t.append("metadata",this.encodeMetadata(r))):(t=p,s["cache-control"]=`max-age=${i.cacheControl}`,s["content-type"]=i.contentType,r&&(s["x-metadata"]=this.toBase64(this.encodeMetadata(r)))),(null==d?void 0:d.headers)&&(s=Object.assign(Object.assign({},s),d.headers));let l=this._removeEmptyFolders(a),o=this._getFinalPath(l),n=yield this.fetch(`${this.url}/object/${o}`,Object.assign({method:e,body:t,headers:s},(null==i?void 0:i.duplex)?{duplex:i.duplex}:{})),m=yield n.json();if(n.ok)return{data:{path:l,id:m.Id,fullPath:m.Key},error:null};return{data:null,error:m}}catch(e){if(Q(e))return{data:null,error:e};throw e}})}upload(e,a,p){return eu(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,a,p)})}uploadToSignedUrl(e,a,p,d){return eu(this,void 0,void 0,function*(){let t=this._removeEmptyFolders(e),i=this._getFinalPath(t),s=new URL(this.url+`/object/upload/sign/${i}`);s.searchParams.set("token",a);try{let e,a=Object.assign({upsert:ev.upsert},d),i=Object.assign(Object.assign({},this.headers),{"x-upsert":String(a.upsert)});"undefined"!=typeof Blob&&p instanceof Blob?((e=new FormData).append("cacheControl",a.cacheControl),e.append("",p)):"undefined"!=typeof FormData&&p instanceof FormData?(e=p).append("cacheControl",a.cacheControl):(e=p,i["cache-control"]=`max-age=${a.cacheControl}`,i["content-type"]=a.contentType);let r=yield this.fetch(s.toString(),{method:"PUT",body:e,headers:i}),l=yield r.json();if(r.ok)return{data:{path:t,fullPath:l.Key},error:null};return{data:null,error:l}}catch(e){if(Q(e))return{data:null,error:e};throw e}})}createSignedUploadUrl(e,a){return eu(this,void 0,void 0,function*(){try{let p=this._getFinalPath(e),d=Object.assign({},this.headers);(null==a?void 0:a.upsert)&&(d["x-upsert"]="true");let t=yield em(this.fetch,`${this.url}/object/upload/sign/${p}`,{},{headers:d}),i=new URL(this.url+t.url),s=i.searchParams.get("token");if(!s)throw new Z("No token returned by API");return{data:{signedUrl:i.toString(),path:e,token:s},error:null}}catch(e){if(Q(e))return{data:null,error:e};throw e}})}update(e,a,p){return eu(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,a,p)})}move(e,a,p){return eu(this,void 0,void 0,function*(){try{return{data:yield em(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:a,destinationBucket:null==p?void 0:p.destinationBucket},{headers:this.headers}),error:null}}catch(e){if(Q(e))return{data:null,error:e};throw e}})}copy(e,a,p){return eu(this,void 0,void 0,function*(){try{return{data:{path:(yield em(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:a,destinationBucket:null==p?void 0:p.destinationBucket},{headers:this.headers})).Key},error:null}}catch(e){if(Q(e))return{data:null,error:e};throw e}})}createSignedUrl(e,a,p){return eu(this,void 0,void 0,function*(){try{let d=this._getFinalPath(e),t=yield em(this.fetch,`${this.url}/object/sign/${d}`,Object.assign({expiresIn:a},(null==p?void 0:p.transform)?{transform:p.transform}:{}),{headers:this.headers}),i=(null==p?void 0:p.download)?`&download=${!0===p.download?"":p.download}`:"";return{data:t={signedUrl:encodeURI(`${this.url}${t.signedURL}${i}`)},error:null}}catch(e){if(Q(e))return{data:null,error:e};throw e}})}createSignedUrls(e,a,p){return eu(this,void 0,void 0,function*(){try{let d=yield em(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:a,paths:e},{headers:this.headers}),t=(null==p?void 0:p.download)?`&download=${!0===p.download?"":p.download}`:"";return{data:d.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${t}`):null})),error:null}}catch(e){if(Q(e))return{data:null,error:e};throw e}})}download(e,a){return eu(this,void 0,void 0,function*(){let p=void 0!==(null==a?void 0:a.transform),d=this.transformOptsToQueryString((null==a?void 0:a.transform)||{}),t=d?`?${d}`:"";try{let a=this._getFinalPath(e),d=yield en(this.fetch,`${this.url}/${p?"render/image/authenticated":"object"}/${a}${t}`,{headers:this.headers,noResolveJson:!0});return{data:yield d.blob(),error:null}}catch(e){if(Q(e))return{data:null,error:e};throw e}})}info(e){return eu(this,void 0,void 0,function*(){let a=this._getFinalPath(e);try{let e=yield en(this.fetch,`${this.url}/object/info/${a}`,{headers:this.headers});return{data:et(e),error:null}}catch(e){if(Q(e))return{data:null,error:e};throw e}})}exists(e){return eu(this,void 0,void 0,function*(){let a=this._getFinalPath(e);try{return yield function(e,a,p,d){return ei(this,void 0,void 0,function*(){return eo(e,"HEAD",a,Object.assign(Object.assign({},p),{noResolveJson:!0}),void 0)})}(this.fetch,`${this.url}/object/${a}`,{headers:this.headers}),{data:!0,error:null}}catch(e){if(Q(e)&&e instanceof ea){let a=e.originalError;if([400,404].includes(null==a?void 0:a.status))return{data:!1,error:e}}throw e}})}getPublicUrl(e,a){let p=this._getFinalPath(e),d=[],t=(null==a?void 0:a.download)?`download=${!0===a.download?"":a.download}`:"";""!==t&&d.push(t);let i=void 0!==(null==a?void 0:a.transform),s=this.transformOptsToQueryString((null==a?void 0:a.transform)||{});""!==s&&d.push(s);let r=d.join("&");return""!==r&&(r=`?${r}`),{data:{publicUrl:encodeURI(`${this.url}/${i?"render/image":"object"}/public/${p}${r}`)}}}remove(e){return eu(this,void 0,void 0,function*(){try{return{data:yield eh(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(e){if(Q(e))return{data:null,error:e};throw e}})}list(e,a,p){return eu(this,void 0,void 0,function*(){try{let d=Object.assign(Object.assign(Object.assign({},ec),a),{prefix:e||""});return{data:yield em(this.fetch,`${this.url}/object/list/${this.bucketId}`,d,{headers:this.headers},p),error:null}}catch(e){if(Q(e))return{data:null,error:e};throw e}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return"undefined"!=typeof Buffer?Buffer.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){let a=[];return e.width&&a.push(`width=${e.width}`),e.height&&a.push(`height=${e.height}`),e.resize&&a.push(`resize=${e.resize}`),e.format&&a.push(`format=${e.format}`),e.quality&&a.push(`quality=${e.quality}`),a.join("&")}}let ew={"X-Client-Info":"storage-js/2.7.1"};var e_=function(e,a,p,d){return new(p||(p=Promise))(function(t,i){function s(e){try{l(d.next(e))}catch(e){i(e)}}function r(e){try{l(d.throw(e))}catch(e){i(e)}}function l(e){var a;e.done?t(e.value):((a=e.value)instanceof p?a:new p(function(e){e(a)})).then(s,r)}l((d=d.apply(e,a||[])).next())})};class eg{constructor(e,a={},p){this.url=e,this.headers=Object.assign(Object.assign({},ew),a),this.fetch=ep(p)}listBuckets(){return e_(this,void 0,void 0,function*(){try{return{data:yield en(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(Q(e))return{data:null,error:e};throw e}})}getBucket(e){return e_(this,void 0,void 0,function*(){try{return{data:yield en(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(e){if(Q(e))return{data:null,error:e};throw e}})}createBucket(e,a={public:!1}){return e_(this,void 0,void 0,function*(){try{return{data:yield em(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:a.public,file_size_limit:a.fileSizeLimit,allowed_mime_types:a.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(Q(e))return{data:null,error:e};throw e}})}updateBucket(e,a){return e_(this,void 0,void 0,function*(){try{return{data:yield function(e,a,p,d,t){return ei(this,void 0,void 0,function*(){return eo(e,"PUT",a,d,void 0,p)})}(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:a.public,file_size_limit:a.fileSizeLimit,allowed_mime_types:a.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(Q(e))return{data:null,error:e};throw e}})}emptyBucket(e){return e_(this,void 0,void 0,function*(){try{return{data:yield em(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(e){if(Q(e))return{data:null,error:e};throw e}})}deleteBucket(e){return e_(this,void 0,void 0,function*(){try{return{data:yield eh(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(e){if(Q(e))return{data:null,error:e};throw e}})}}class ey extends eg{constructor(e,a={},p){super(e,a,p)}from(e){return new ef(this.url,this.headers,e,this.fetch)}}let eb="";eb="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";let eS={headers:{"X-Client-Info":`supabase-js-${eb}/2.50.3`}},eT={schema:"public"},eN={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},ek={};var eE=p(83899),eD=p.n(eE);let eV=e=>{let a;return a=e||("undefined"==typeof fetch?eD():fetch),(...e)=>a(...e)},eO=()=>"undefined"==typeof Headers?eE.Headers:Headers,eC=(e,a,p)=>{let d=eV(p),t=eO();return(p,i)=>(function(e,a,p,d){return new(p||(p=Promise))(function(t,i){function s(e){try{l(d.next(e))}catch(e){i(e)}}function r(e){try{l(d.throw(e))}catch(e){i(e)}}function l(e){var a;e.done?t(e.value):((a=e.value)instanceof p?a:new p(function(e){e(a)})).then(s,r)}l((d=d.apply(e,a||[])).next())})})(void 0,void 0,void 0,function*(){var s;let r=null!=(s=yield a())?s:e,l=new t(null==i?void 0:i.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization",`Bearer ${r}`),d(p,Object.assign(Object.assign({},i),{headers:l}))})},eA="2.70.0",ex={"X-Client-Info":`gotrue-js/${eA}`},eP="X-Supabase-Api-Version",ej={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},eR=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class eL extends Error{constructor(e,a,p){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=a,this.code=p}}function eB(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class eI extends eL{constructor(e,a,p){super(e,a,p),this.name="AuthApiError",this.status=a,this.code=p}}class e$ extends eL{constructor(e,a){super(e),this.name="AuthUnknownError",this.originalError=a}}class eU extends eL{constructor(e,a,p,d){super(e,p,d),this.name=a,this.status=p}}class eF extends eU{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class eM extends eU{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class eq extends eU{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class eW extends eU{constructor(e,a=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=a}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class ez extends eU{constructor(e,a=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=a}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class eG extends eU{constructor(e,a){super(e,"AuthRetryableFetchError",a,void 0)}}function eH(e){return eB(e)&&"AuthRetryableFetchError"===e.name}class eK extends eU{constructor(e,a,p){super(e,"AuthWeakPasswordError",a,"weak_password"),this.reasons=p}}class eJ extends eU{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}let eY="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),eX=" 	\n\r=".split(""),eZ=(()=>{let e=Array(128);for(let a=0;a<e.length;a+=1)e[a]=-1;for(let a=0;a<eX.length;a+=1)e[eX[a].charCodeAt(0)]=-2;for(let a=0;a<eY.length;a+=1)e[eY[a].charCodeAt(0)]=a;return e})();function eQ(e,a,p){if(null!==e)for(a.queue=a.queue<<8|e,a.queuedBits+=8;a.queuedBits>=6;)p(eY[a.queue>>a.queuedBits-6&63]),a.queuedBits-=6;else if(a.queuedBits>0)for(a.queue=a.queue<<6-a.queuedBits,a.queuedBits=6;a.queuedBits>=6;)p(eY[a.queue>>a.queuedBits-6&63]),a.queuedBits-=6}function e1(e,a,p){let d=eZ[e];if(d>-1)for(a.queue=a.queue<<6|d,a.queuedBits+=6;a.queuedBits>=8;)p(a.queue>>a.queuedBits-8&255),a.queuedBits-=8;else if(-2===d)return;else throw Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}function e2(e){let a=[],p=e=>{a.push(String.fromCodePoint(e))},d={utf8seq:0,codepoint:0},t={queue:0,queuedBits:0},i=e=>{!function(e,a,p){if(0===a.utf8seq){if(e<=127)return p(e);for(let p=1;p<6;p+=1)if((e>>7-p&1)==0){a.utf8seq=p;break}if(2===a.utf8seq)a.codepoint=31&e;else if(3===a.utf8seq)a.codepoint=15&e;else if(4===a.utf8seq)a.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");a.utf8seq-=1}else if(a.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");a.codepoint=a.codepoint<<6|63&e,a.utf8seq-=1,0===a.utf8seq&&p(a.codepoint)}}(e,d,p)};for(let a=0;a<e.length;a+=1)e1(e.charCodeAt(a),t,i);return a.join("")}let e0=()=>"undefined"!=typeof window&&"undefined"!=typeof document,e6={tested:!1,writable:!1},e4=()=>{if(!e0())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(e){return!1}if(e6.tested)return e6.writable;let e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),e6.tested=!0,e6.writable=!0}catch(e){e6.tested=!0,e6.writable=!1}return e6.writable},e9=e=>{let a;return a=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(p.t.bind(p,83899,23)).then(({default:a})=>a(...e)):fetch),(...e)=>a(...e)},e3=e=>"object"==typeof e&&null!==e&&"status"in e&&"ok"in e&&"json"in e&&"function"==typeof e.json,e5=async(e,a,p)=>{await e.setItem(a,JSON.stringify(p))},e8=async(e,a)=>{let p=await e.getItem(a);if(!p)return null;try{return JSON.parse(p)}catch(e){return p}},e7=async(e,a)=>{await e.removeItem(a)};class ae{constructor(){this.promise=new ae.promiseConstructor((e,a)=>{this.resolve=e,this.reject=a})}}function aa(e){let a=e.split(".");if(3!==a.length)throw new eJ("Invalid JWT structure");for(let e=0;e<a.length;e++)if(!eR.test(a[e]))throw new eJ("JWT not in base64url format");return{header:JSON.parse(e2(a[0])),payload:JSON.parse(e2(a[1])),signature:function(e){let a=[],p={queue:0,queuedBits:0},d=e=>{a.push(e)};for(let a=0;a<e.length;a+=1)e1(e.charCodeAt(a),p,d);return new Uint8Array(a)}(a[2]),raw:{header:a[0],payload:a[1]}}}async function ap(e){return await new Promise(a=>{setTimeout(()=>a(null),e)})}function ad(e){return("0"+e.toString(16)).substr(-2)}async function at(e){let a=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",a))).map(e=>String.fromCharCode(e)).join("")}async function ai(e){return"undefined"==typeof crypto||void 0===crypto.subtle||"undefined"==typeof TextEncoder?(console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e):btoa(await at(e)).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function as(e,a,p=!1){let d=function(){let e=new Uint32Array(56);if("undefined"==typeof crypto){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",a=e.length,p="";for(let d=0;d<56;d++)p+=e.charAt(Math.floor(Math.random()*a));return p}return crypto.getRandomValues(e),Array.from(e,ad).join("")}(),t=d;p&&(t+="/PASSWORD_RECOVERY"),await e5(e,`${a}-code-verifier`,t);let i=await ai(d),s=d===i?"plain":"s256";return[i,s]}ae.promiseConstructor=Promise;let ar=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i,al=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function ao(e){if(!al.test(e))throw Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var an=function(e,a){var p={};for(var d in e)Object.prototype.hasOwnProperty.call(e,d)&&0>a.indexOf(d)&&(p[d]=e[d]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var t=0,d=Object.getOwnPropertySymbols(e);t<d.length;t++)0>a.indexOf(d[t])&&Object.prototype.propertyIsEnumerable.call(e,d[t])&&(p[d[t]]=e[d[t]]);return p};let am=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),ah=[502,503,504];async function au(e){var a;let p,d;if(!e3(e))throw new eG(am(e),0);if(ah.includes(e.status))throw new eG(am(e),e.status);try{p=await e.json()}catch(e){throw new e$(am(e),e)}let t=function(e){let a=e.headers.get(eP);if(!a||!a.match(ar))return null;try{return new Date(`${a}T00:00:00.0Z`)}catch(e){return null}}(e);if(t&&t.getTime()>=ej["2024-01-01"].timestamp&&"object"==typeof p&&p&&"string"==typeof p.code?d=p.code:"object"==typeof p&&p&&"string"==typeof p.error_code&&(d=p.error_code),d){if("weak_password"===d)throw new eK(am(p),e.status,(null==(a=p.weak_password)?void 0:a.reasons)||[]);else if("session_not_found"===d)throw new eF}else if("object"==typeof p&&p&&"object"==typeof p.weak_password&&p.weak_password&&Array.isArray(p.weak_password.reasons)&&p.weak_password.reasons.length&&p.weak_password.reasons.reduce((e,a)=>e&&"string"==typeof a,!0))throw new eK(am(p),e.status,p.weak_password.reasons);throw new eI(am(p),e.status||500,d)}let ac=(e,a,p,d)=>{let t={method:e,headers:(null==a?void 0:a.headers)||{}};return"GET"===e?t:(t.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==a?void 0:a.headers),t.body=JSON.stringify(d),Object.assign(Object.assign({},t),p))};async function av(e,a,p,d){var t;let i=Object.assign({},null==d?void 0:d.headers);i[eP]||(i[eP]=ej["2024-01-01"].name),(null==d?void 0:d.jwt)&&(i.Authorization=`Bearer ${d.jwt}`);let s=null!=(t=null==d?void 0:d.query)?t:{};(null==d?void 0:d.redirectTo)&&(s.redirect_to=d.redirectTo);let r=Object.keys(s).length?"?"+new URLSearchParams(s).toString():"",l=await af(e,a,p+r,{headers:i,noResolveJson:null==d?void 0:d.noResolveJson},{},null==d?void 0:d.body);return(null==d?void 0:d.xform)?null==d?void 0:d.xform(l):{data:Object.assign({},l),error:null}}async function af(e,a,p,d,t,i){let s,r=ac(a,d,t,i);try{s=await e(p,Object.assign({},r))}catch(e){throw console.error(e),new eG(am(e),0)}if(s.ok||await au(s),null==d?void 0:d.noResolveJson)return s;try{return await s.json()}catch(e){await au(e)}}function aw(e){var a,p,d;let t=null;(d=e).access_token&&d.refresh_token&&d.expires_in&&(t=Object.assign({},e),e.expires_at||(t.expires_at=(p=e.expires_in,Math.round(Date.now()/1e3)+p)));return{data:{session:t,user:null!=(a=e.user)?a:e},error:null}}function a_(e){let a=aw(e);return!a.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,a)=>e&&"string"==typeof a,!0)&&(a.data.weak_password=e.weak_password),a}function ag(e){var a;return{data:{user:null!=(a=e.user)?a:e},error:null}}function ay(e){return{data:e,error:null}}function ab(e){let{action_link:a,email_otp:p,hashed_token:d,redirect_to:t,verification_type:i}=e;return{data:{properties:{action_link:a,email_otp:p,hashed_token:d,redirect_to:t,verification_type:i},user:Object.assign({},an(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]))},error:null}}function aS(e){return e}let aT=["global","local","others"];var aN=function(e,a){var p={};for(var d in e)Object.prototype.hasOwnProperty.call(e,d)&&0>a.indexOf(d)&&(p[d]=e[d]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var t=0,d=Object.getOwnPropertySymbols(e);t<d.length;t++)0>a.indexOf(d[t])&&Object.prototype.propertyIsEnumerable.call(e,d[t])&&(p[d[t]]=e[d[t]]);return p};class ak{constructor({url:e="",headers:a={},fetch:p}){this.url=e,this.headers=a,this.fetch=e9(p),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,a=aT[0]){if(0>aT.indexOf(a))throw Error(`@supabase/auth-js: Parameter scope must be one of ${aT.join(", ")}`);try{return await av(this.fetch,"POST",`${this.url}/logout?scope=${a}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(e){if(eB(e))return{data:null,error:e};throw e}}async inviteUserByEmail(e,a={}){try{return await av(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:a.data},headers:this.headers,redirectTo:a.redirectTo,xform:ag})}catch(e){if(eB(e))return{data:{user:null},error:e};throw e}}async generateLink(e){try{let{options:a}=e,p=aN(e,["options"]),d=Object.assign(Object.assign({},p),a);return"newEmail"in p&&(d.new_email=null==p?void 0:p.newEmail,delete d.newEmail),await av(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:d,headers:this.headers,xform:ab,redirectTo:null==a?void 0:a.redirectTo})}catch(e){if(eB(e))return{data:{properties:null,user:null},error:e};throw e}}async createUser(e){try{return await av(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:ag})}catch(e){if(eB(e))return{data:{user:null},error:e};throw e}}async listUsers(e){var a,p,d,t,i,s,r;try{let l={nextPage:null,lastPage:0,total:0},o=await av(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!=(p=null==(a=null==e?void 0:e.page)?void 0:a.toString())?p:"",per_page:null!=(t=null==(d=null==e?void 0:e.perPage)?void 0:d.toString())?t:""},xform:aS});if(o.error)throw o.error;let n=await o.json(),m=null!=(i=o.headers.get("x-total-count"))?i:0,h=null!=(r=null==(s=o.headers.get("link"))?void 0:s.split(","))?r:[];return h.length>0&&(h.forEach(e=>{let a=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),p=JSON.parse(e.split(";")[1].split("=")[1]);l[`${p}Page`]=a}),l.total=parseInt(m)),{data:Object.assign(Object.assign({},n),l),error:null}}catch(e){if(eB(e))return{data:{users:[]},error:e};throw e}}async getUserById(e){ao(e);try{return await av(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:ag})}catch(e){if(eB(e))return{data:{user:null},error:e};throw e}}async updateUserById(e,a){ao(e);try{return await av(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:a,headers:this.headers,xform:ag})}catch(e){if(eB(e))return{data:{user:null},error:e};throw e}}async deleteUser(e,a=!1){ao(e);try{return await av(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:a},xform:ag})}catch(e){if(eB(e))return{data:{user:null},error:e};throw e}}async _listFactors(e){ao(e.userId);try{let{data:a,error:p}=await av(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:a,error:p}}catch(e){if(eB(e))return{data:null,error:e};throw e}}async _deleteFactor(e){ao(e.userId),ao(e.id);try{return{data:await av(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(e){if(eB(e))return{data:null,error:e};throw e}}}let aE={getItem:e=>e4()?globalThis.localStorage.getItem(e):null,setItem:(e,a)=>{e4()&&globalThis.localStorage.setItem(e,a)},removeItem:e=>{e4()&&globalThis.localStorage.removeItem(e)}};function aD(e={}){return{getItem:a=>e[a]||null,setItem:(a,p)=>{e[a]=p},removeItem:a=>{delete e[a]}}}let aV={debug:!!(globalThis&&e4()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"))};class aO extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class aC extends aO{}async function aA(e,a,p){aV.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,a);let d=new globalThis.AbortController;return a>0&&setTimeout(()=>{d.abort(),aV.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},a),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,0===a?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:d.signal},async d=>{if(d){aV.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,d.name);try{return await p()}finally{aV.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,d.name)}}if(0===a)throw aV.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new aC(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(aV.debug)try{let e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(e){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",e)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await p()}))}if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}let ax={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:ex,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function aP(e,a,p){return await p()}class aj{constructor(e){var a,p;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=aj.nextInstanceID,aj.nextInstanceID+=1,this.instanceID>0&&e0()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");let d=Object.assign(Object.assign({},ax),e);if(this.logDebugMessages=!!d.debug,"function"==typeof d.debug&&(this.logger=d.debug),this.persistSession=d.persistSession,this.storageKey=d.storageKey,this.autoRefreshToken=d.autoRefreshToken,this.admin=new ak({url:d.url,headers:d.headers,fetch:d.fetch}),this.url=d.url,this.headers=d.headers,this.fetch=e9(d.fetch),this.lock=d.lock||aP,this.detectSessionInUrl=d.detectSessionInUrl,this.flowType=d.flowType,this.hasCustomAuthorizationHeader=d.hasCustomAuthorizationHeader,d.lock?this.lock=d.lock:e0()&&(null==(a=null==globalThis?void 0:globalThis.navigator)?void 0:a.locks)?this.lock=aA:this.lock=aP,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?d.storage?this.storage=d.storage:e4()?this.storage=aE:(this.memoryStorage={},this.storage=aD(this.memoryStorage)):(this.memoryStorage={},this.storage=aD(this.memoryStorage)),e0()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(e){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",e)}null==(p=this.broadcastChannel)||p.addEventListener("message",async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)})}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${eA}) ${new Date().toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var e;try{let a=function(e){let a={},p=new URL(e);if(p.hash&&"#"===p.hash[0])try{new URLSearchParams(p.hash.substring(1)).forEach((e,p)=>{a[p]=e})}catch(e){}return p.searchParams.forEach((e,p)=>{a[p]=e}),a}(window.location.href),p="none";if(this._isImplicitGrantCallback(a)?p="implicit":await this._isPKCECallback(a)&&(p="pkce"),e0()&&this.detectSessionInUrl&&"none"!==p){let{data:d,error:t}=await this._getSessionFromURL(a,p);if(t){if(this._debug("#_initialize()","error detecting session from URL",t),eB(t)&&"AuthImplicitGrantRedirectError"===t.name){let a=null==(e=t.details)?void 0:e.code;if("identity_already_exists"===a||"identity_not_found"===a||"single_identity_not_deletable"===a)return{error:t}}return await this._removeSession(),{error:t}}let{session:i,redirectType:s}=d;return this._debug("#_initialize()","detected session in URL",i,"redirect type",s),await this._saveSession(i),setTimeout(async()=>{"recovery"===s?await this._notifyAllSubscribers("PASSWORD_RECOVERY",i):await this._notifyAllSubscribers("SIGNED_IN",i)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(e){if(eB(e))return{error:e};return{error:new e$("Unexpected error during initialization",e)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var a,p,d;try{let{data:t,error:i}=await av(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!=(p=null==(a=null==e?void 0:e.options)?void 0:a.data)?p:{},gotrue_meta_security:{captcha_token:null==(d=null==e?void 0:e.options)?void 0:d.captchaToken}},xform:aw});if(i||!t)return{data:{user:null,session:null},error:i};let s=t.session,r=t.user;return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",s)),{data:{user:r,session:s},error:null}}catch(e){if(eB(e))return{data:{user:null,session:null},error:e};throw e}}async signUp(e){var a,p,d;try{let t;if("email"in e){let{email:p,password:d,options:i}=e,s=null,r=null;"pkce"===this.flowType&&([s,r]=await as(this.storage,this.storageKey)),t=await av(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==i?void 0:i.emailRedirectTo,body:{email:p,password:d,data:null!=(a=null==i?void 0:i.data)?a:{},gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken},code_challenge:s,code_challenge_method:r},xform:aw})}else if("phone"in e){let{phone:a,password:i,options:s}=e;t=await av(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:a,password:i,data:null!=(p=null==s?void 0:s.data)?p:{},channel:null!=(d=null==s?void 0:s.channel)?d:"sms",gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}},xform:aw})}else throw new eq("You must provide either an email or phone number and a password");let{data:i,error:s}=t;if(s||!i)return{data:{user:null,session:null},error:s};let r=i.session,l=i.user;return i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",r)),{data:{user:l,session:r},error:null}}catch(e){if(eB(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithPassword(e){try{let a;if("email"in e){let{email:p,password:d,options:t}=e;a=await av(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:p,password:d,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:a_})}else if("phone"in e){let{phone:p,password:d,options:t}=e;a=await av(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:p,password:d,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:a_})}else throw new eq("You must provide either an email or phone number and a password");let{data:p,error:d}=a;if(d)return{data:{user:null,session:null},error:d};if(!p||!p.session||!p.user)return{data:{user:null,session:null},error:new eM};return p.session&&(await this._saveSession(p.session),await this._notifyAllSubscribers("SIGNED_IN",p.session)),{data:Object.assign({user:p.user,session:p.session},p.weak_password?{weakPassword:p.weak_password}:null),error:d}}catch(e){if(eB(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOAuth(e){var a,p,d,t;return await this._handleProviderSignIn(e.provider,{redirectTo:null==(a=e.options)?void 0:a.redirectTo,scopes:null==(p=e.options)?void 0:p.scopes,queryParams:null==(d=e.options)?void 0:d.queryParams,skipBrowserRedirect:null==(t=e.options)?void 0:t.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async signInWithWeb3(e){let{chain:a}=e;if("solana"===a)return await this.signInWithSolana(e);throw Error(`@supabase/auth-js: Unsupported chain "${a}"`)}async signInWithSolana(e){var a,p,d,t,i,s,r,l,o,n,m,h;let u,c;if("message"in e)u=e.message,c=e.signature;else{let m,{chain:h,wallet:v,statement:f,options:w}=e;if(e0())if("object"==typeof v)m=v;else{let e=window;if("solana"in e&&"object"==typeof e.solana&&("signIn"in e.solana&&"function"==typeof e.solana.signIn||"signMessage"in e.solana&&"function"==typeof e.solana.signMessage))m=e.solana;else throw Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}else{if("object"!=typeof v||!(null==w?void 0:w.url))throw Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");m=v}let _=new URL(null!=(a=null==w?void 0:w.url)?a:window.location.href);if("signIn"in m&&m.signIn){let e,a=await m.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},null==w?void 0:w.signInWithSolana),{version:"1",domain:_.host,uri:_.href}),f?{statement:f}:null));if(Array.isArray(a)&&a[0]&&"object"==typeof a[0])e=a[0];else if(a&&"object"==typeof a&&"signedMessage"in a&&"signature"in a)e=a;else throw Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in e&&"signature"in e&&("string"==typeof e.signedMessage||e.signedMessage instanceof Uint8Array)&&e.signature instanceof Uint8Array)u="string"==typeof e.signedMessage?e.signedMessage:new TextDecoder().decode(e.signedMessage),c=e.signature;else throw Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in m)||"function"!=typeof m.signMessage||!("publicKey"in m)||"object"!=typeof m||!m.publicKey||!("toBase58"in m.publicKey)||"function"!=typeof m.publicKey.toBase58)throw Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");u=[`${_.host} wants you to sign in with your Solana account:`,m.publicKey.toBase58(),...f?["",f,""]:[""],"Version: 1",`URI: ${_.href}`,`Issued At: ${null!=(d=null==(p=null==w?void 0:w.signInWithSolana)?void 0:p.issuedAt)?d:new Date().toISOString()}`,...(null==(t=null==w?void 0:w.signInWithSolana)?void 0:t.notBefore)?[`Not Before: ${w.signInWithSolana.notBefore}`]:[],...(null==(i=null==w?void 0:w.signInWithSolana)?void 0:i.expirationTime)?[`Expiration Time: ${w.signInWithSolana.expirationTime}`]:[],...(null==(s=null==w?void 0:w.signInWithSolana)?void 0:s.chainId)?[`Chain ID: ${w.signInWithSolana.chainId}`]:[],...(null==(r=null==w?void 0:w.signInWithSolana)?void 0:r.nonce)?[`Nonce: ${w.signInWithSolana.nonce}`]:[],...(null==(l=null==w?void 0:w.signInWithSolana)?void 0:l.requestId)?[`Request ID: ${w.signInWithSolana.requestId}`]:[],...(null==(n=null==(o=null==w?void 0:w.signInWithSolana)?void 0:o.resources)?void 0:n.length)?["Resources",...w.signInWithSolana.resources.map(e=>`- ${e}`)]:[]].join("\n");let e=await m.signMessage(new TextEncoder().encode(u),"utf8");if(!e||!(e instanceof Uint8Array))throw Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");c=e}}try{let{data:a,error:p}=await av(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:u,signature:function(e){let a=[],p={queue:0,queuedBits:0},d=e=>{a.push(e)};return e.forEach(e=>eQ(e,p,d)),eQ(null,p,d),a.join("")}(c)},(null==(m=e.options)?void 0:m.captchaToken)?{gotrue_meta_security:{captcha_token:null==(h=e.options)?void 0:h.captchaToken}}:null),xform:aw});if(p)throw p;if(!a||!a.session||!a.user)return{data:{user:null,session:null},error:new eM};return a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",a.session)),{data:Object.assign({},a),error:p}}catch(e){if(eB(e))return{data:{user:null,session:null},error:e};throw e}}async _exchangeCodeForSession(e){let a=await e8(this.storage,`${this.storageKey}-code-verifier`),[p,d]=(null!=a?a:"").split("/");try{let{data:a,error:t}=await av(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:p},xform:aw});if(await e7(this.storage,`${this.storageKey}-code-verifier`),t)throw t;if(!a||!a.session||!a.user)return{data:{user:null,session:null,redirectType:null},error:new eM};return a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",a.session)),{data:Object.assign(Object.assign({},a),{redirectType:null!=d?d:null}),error:t}}catch(e){if(eB(e))return{data:{user:null,session:null,redirectType:null},error:e};throw e}}async signInWithIdToken(e){try{let{options:a,provider:p,token:d,access_token:t,nonce:i}=e,{data:s,error:r}=await av(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:p,id_token:d,access_token:t,nonce:i,gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken}},xform:aw});if(r)return{data:{user:null,session:null},error:r};if(!s||!s.session||!s.user)return{data:{user:null,session:null},error:new eM};return s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",s.session)),{data:s,error:r}}catch(e){if(eB(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOtp(e){var a,p,d,t,i;try{if("email"in e){let{email:d,options:t}=e,i=null,s=null;"pkce"===this.flowType&&([i,s]=await as(this.storage,this.storageKey));let{error:r}=await av(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:d,data:null!=(a=null==t?void 0:t.data)?a:{},create_user:null==(p=null==t?void 0:t.shouldCreateUser)||p,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken},code_challenge:i,code_challenge_method:s},redirectTo:null==t?void 0:t.emailRedirectTo});return{data:{user:null,session:null},error:r}}if("phone"in e){let{phone:a,options:p}=e,{data:s,error:r}=await av(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:a,data:null!=(d=null==p?void 0:p.data)?d:{},create_user:null==(t=null==p?void 0:p.shouldCreateUser)||t,gotrue_meta_security:{captcha_token:null==p?void 0:p.captchaToken},channel:null!=(i=null==p?void 0:p.channel)?i:"sms"}});return{data:{user:null,session:null,messageId:null==s?void 0:s.message_id},error:r}}throw new eq("You must provide either an email or phone number.")}catch(e){if(eB(e))return{data:{user:null,session:null},error:e};throw e}}async verifyOtp(e){var a,p;try{let d,t;"options"in e&&(d=null==(a=e.options)?void 0:a.redirectTo,t=null==(p=e.options)?void 0:p.captchaToken);let{data:i,error:s}=await av(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:t}}),redirectTo:d,xform:aw});if(s)throw s;if(!i)throw Error("An error occurred on token verification.");let r=i.session,l=i.user;return(null==r?void 0:r.access_token)&&(await this._saveSession(r),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",r)),{data:{user:l,session:r},error:null}}catch(e){if(eB(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithSSO(e){var a,p,d;try{let t=null,i=null;return"pkce"===this.flowType&&([t,i]=await as(this.storage,this.storageKey)),await av(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!=(p=null==(a=e.options)?void 0:a.redirectTo)?p:void 0}),(null==(d=null==e?void 0:e.options)?void 0:d.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:t,code_challenge_method:i}),headers:this.headers,xform:ay})}catch(e){if(eB(e))return{data:null,error:e};throw e}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{let{data:{session:a},error:p}=e;if(p)throw p;if(!a)throw new eF;let{error:d}=await av(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:a.access_token});return{data:{user:null,session:null},error:d}})}catch(e){if(eB(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{let a=`${this.url}/resend`;if("email"in e){let{email:p,type:d,options:t}=e,{error:i}=await av(this.fetch,"POST",a,{headers:this.headers,body:{email:p,type:d,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},redirectTo:null==t?void 0:t.emailRedirectTo});return{data:{user:null,session:null},error:i}}if("phone"in e){let{phone:p,type:d,options:t}=e,{data:i,error:s}=await av(this.fetch,"POST",a,{headers:this.headers,body:{phone:p,type:d,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}}});return{data:{user:null,session:null,messageId:null==i?void 0:i.message_id},error:s}}throw new eq("You must provide either an email or phone number and a type")}catch(e){if(eB(e))return{data:{user:null,session:null},error:e};throw e}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async e=>e))}async _acquireLock(e,a){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){let e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),p=(async()=>(await e,await a()))();return this.pendingInLock.push((async()=>{try{await p}catch(e){}})()),p}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;let e=a();for(this.pendingInLock.push((async()=>{try{await e}catch(e){}})()),await e;this.pendingInLock.length;){let e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{let a=await this.__loadSession();return await e(a)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",Error().stack);try{let e=null,a=await e8(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",a),null!==a&&(this._isValidSession(a)?e=a:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};let p=!!e.expires_at&&1e3*e.expires_at-Date.now()<9e4;if(this._debug("#__loadSession()",`session has${p?"":" not"} expired`,"expires_at",e.expires_at),!p){if(this.storage.isServer){let a=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,p,d)=>(a||"user"!==p||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),a=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,p,d))})}return{data:{session:e},error:null}}let{session:d,error:t}=await this._callRefreshToken(e.refresh_token);if(t)return{data:{session:null},error:t};return{data:{session:d},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){return e?await this._getUser(e):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(e){try{if(e)return await av(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:ag});return await this._useSession(async e=>{var a,p,d;let{data:t,error:i}=e;if(i)throw i;return(null==(a=t.session)?void 0:a.access_token)||this.hasCustomAuthorizationHeader?await av(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!=(d=null==(p=t.session)?void 0:p.access_token)?d:void 0,xform:ag}):{data:{user:null},error:new eF}})}catch(e){if(eB(e))return eB(e)&&"AuthSessionMissingError"===e.name&&(await this._removeSession(),await e7(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:e};throw e}}async updateUser(e,a={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,a))}async _updateUser(e,a={}){try{return await this._useSession(async p=>{let{data:d,error:t}=p;if(t)throw t;if(!d.session)throw new eF;let i=d.session,s=null,r=null;"pkce"===this.flowType&&null!=e.email&&([s,r]=await as(this.storage,this.storageKey));let{data:l,error:o}=await av(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==a?void 0:a.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:s,code_challenge_method:r}),jwt:i.access_token,xform:ag});if(o)throw o;return i.user=l.user,await this._saveSession(i),await this._notifyAllSubscribers("USER_UPDATED",i),{data:{user:i.user},error:null}})}catch(e){if(eB(e))return{data:{user:null},error:e};throw e}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new eF;let a=Date.now()/1e3,p=a,d=!0,t=null,{payload:i}=aa(e.access_token);if(i.exp&&(d=(p=i.exp)<=a),d){let{session:a,error:p}=await this._callRefreshToken(e.refresh_token);if(p)return{data:{user:null,session:null},error:p};if(!a)return{data:{user:null,session:null},error:null};t=a}else{let{data:d,error:i}=await this._getUser(e.access_token);if(i)throw i;t={access_token:e.access_token,refresh_token:e.refresh_token,user:d.user,token_type:"bearer",expires_in:p-a,expires_at:p},await this._saveSession(t),await this._notifyAllSubscribers("SIGNED_IN",t)}return{data:{user:t.user,session:t},error:null}}catch(e){if(eB(e))return{data:{session:null,user:null},error:e};throw e}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async a=>{var p;if(!e){let{data:d,error:t}=a;if(t)throw t;e=null!=(p=d.session)?p:void 0}if(!(null==e?void 0:e.refresh_token))throw new eF;let{session:d,error:t}=await this._callRefreshToken(e.refresh_token);return t?{data:{user:null,session:null},error:t}:d?{data:{user:d.user,session:d},error:null}:{data:{user:null,session:null},error:null}})}catch(e){if(eB(e))return{data:{user:null,session:null},error:e};throw e}}async _getSessionFromURL(e,a){try{if(!e0())throw new eW("No browser detected.");if(e.error||e.error_description||e.error_code)throw new eW(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(a){case"implicit":if("pkce"===this.flowType)throw new ez("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new eW("Not a valid implicit grant flow url.")}if("pkce"===a){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new ez("No code detected.");let{data:a,error:p}=await this._exchangeCodeForSession(e.code);if(p)throw p;let d=new URL(window.location.href);return d.searchParams.delete("code"),window.history.replaceState(window.history.state,"",d.toString()),{data:{session:a.session,redirectType:null},error:null}}let{provider_token:p,provider_refresh_token:d,access_token:t,refresh_token:i,expires_in:s,expires_at:r,token_type:l}=e;if(!t||!s||!i||!l)throw new eW("No session defined in URL");let o=Math.round(Date.now()/1e3),n=parseInt(s),m=o+n;r&&(m=parseInt(r));let h=m-o;1e3*h<=3e4&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${h}s, should have been closer to ${n}s`);let u=m-n;o-u>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",u,m,o):o-u<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",u,m,o);let{data:c,error:v}=await this._getUser(t);if(v)throw v;let f={provider_token:p,provider_refresh_token:d,access_token:t,expires_in:n,expires_at:m,refresh_token:i,token_type:l,user:c.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:f,redirectType:e.type},error:null}}catch(e){if(eB(e))return{data:{session:null,redirectType:null},error:e};throw e}}_isImplicitGrantCallback(e){return!!(e.access_token||e.error_description)}async _isPKCECallback(e){let a=await e8(this.storage,`${this.storageKey}-code-verifier`);return!!(e.code&&a)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async a=>{var p;let{data:d,error:t}=a;if(t)return{error:t};let i=null==(p=d.session)?void 0:p.access_token;if(i){let{error:a}=await this.admin.signOut(i,e);if(a&&!(eB(a)&&"AuthApiError"===a.name&&(404===a.status||401===a.status||403===a.status)))return{error:a}}return"others"!==e&&(await this._removeSession(),await e7(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){let a="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let a=16*Math.random()|0;return("x"==e?a:3&a|8).toString(16)}),p={id:a,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",a),this.stateChangeEmitters.delete(a)}};return this._debug("#onAuthStateChange()","registered callback with id",a),this.stateChangeEmitters.set(a,p),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(a)})})(),{data:{subscription:p}}}async _emitInitialSession(e){return await this._useSession(async a=>{var p,d;try{let{data:{session:d},error:t}=a;if(t)throw t;await (null==(p=this.stateChangeEmitters.get(e))?void 0:p.callback("INITIAL_SESSION",d)),this._debug("INITIAL_SESSION","callback id",e,"session",d)}catch(a){await (null==(d=this.stateChangeEmitters.get(e))?void 0:d.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",a),console.error(a)}})}async resetPasswordForEmail(e,a={}){let p=null,d=null;"pkce"===this.flowType&&([p,d]=await as(this.storage,this.storageKey,!0));try{return await av(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:p,code_challenge_method:d,gotrue_meta_security:{captcha_token:a.captchaToken}},headers:this.headers,redirectTo:a.redirectTo})}catch(e){if(eB(e))return{data:null,error:e};throw e}}async getUserIdentities(){var e;try{let{data:a,error:p}=await this.getUser();if(p)throw p;return{data:{identities:null!=(e=a.user.identities)?e:[]},error:null}}catch(e){if(eB(e))return{data:null,error:e};throw e}}async linkIdentity(e){var a;try{let{data:p,error:d}=await this._useSession(async a=>{var p,d,t,i,s;let{data:r,error:l}=a;if(l)throw l;let o=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null==(p=e.options)?void 0:p.redirectTo,scopes:null==(d=e.options)?void 0:d.scopes,queryParams:null==(t=e.options)?void 0:t.queryParams,skipBrowserRedirect:!0});return await av(this.fetch,"GET",o,{headers:this.headers,jwt:null!=(s=null==(i=r.session)?void 0:i.access_token)?s:void 0})});if(d)throw d;return!e0()||(null==(a=e.options)?void 0:a.skipBrowserRedirect)||window.location.assign(null==p?void 0:p.url),{data:{provider:e.provider,url:null==p?void 0:p.url},error:null}}catch(a){if(eB(a))return{data:{provider:e.provider,url:null},error:a};throw a}}async unlinkIdentity(e){try{return await this._useSession(async a=>{var p,d;let{data:t,error:i}=a;if(i)throw i;return await av(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!=(d=null==(p=t.session)?void 0:p.access_token)?d:void 0})})}catch(e){if(eB(e))return{data:null,error:e};throw e}}async _refreshAccessToken(e){let a=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(a,"begin");try{var p,d;let t=Date.now();return await (p=async p=>(p>0&&await ap(200*Math.pow(2,p-1)),this._debug(a,"refreshing attempt",p),await av(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:aw})),d=(e,a)=>{let p=200*Math.pow(2,e);return a&&eH(a)&&Date.now()+p-t<3e4},new Promise((e,a)=>{(async()=>{for(let t=0;t<1/0;t++)try{let a=await p(t);if(!d(t,null,a))return void e(a)}catch(e){if(!d(t,e))return void a(e)}})()}))}catch(e){if(this._debug(a,"error",e),eB(e))return{data:{session:null,user:null},error:e};throw e}finally{this._debug(a,"end")}}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,a){let p=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:a.redirectTo,scopes:a.scopes,queryParams:a.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",a,"url",p),e0()&&!a.skipBrowserRedirect&&window.location.assign(p),{data:{provider:e,url:p},error:null}}async _recoverAndRefresh(){var e;let a="#_recoverAndRefresh()";this._debug(a,"begin");try{let p=await e8(this.storage,this.storageKey);if(this._debug(a,"session from storage",p),!this._isValidSession(p)){this._debug(a,"session is not valid"),null!==p&&await this._removeSession();return}let d=(null!=(e=p.expires_at)?e:1/0)*1e3-Date.now()<9e4;if(this._debug(a,`session has${d?"":" not"} expired with margin of 90000s`),d){if(this.autoRefreshToken&&p.refresh_token){let{error:e}=await this._callRefreshToken(p.refresh_token);e&&(console.error(e),eH(e)||(this._debug(a,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",p)}catch(e){this._debug(a,"error",e),console.error(e);return}finally{this._debug(a,"end")}}async _callRefreshToken(e){var a,p;if(!e)throw new eF;if(this.refreshingDeferred)return this.refreshingDeferred.promise;let d=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(d,"begin");try{this.refreshingDeferred=new ae;let{data:a,error:p}=await this._refreshAccessToken(e);if(p)throw p;if(!a.session)throw new eF;await this._saveSession(a.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",a.session);let d={session:a.session,error:null};return this.refreshingDeferred.resolve(d),d}catch(e){if(this._debug(d,"error",e),eB(e)){let p={session:null,error:e};return eH(e)||await this._removeSession(),null==(a=this.refreshingDeferred)||a.resolve(p),p}throw null==(p=this.refreshingDeferred)||p.reject(e),e}finally{this.refreshingDeferred=null,this._debug(d,"end")}}async _notifyAllSubscribers(e,a,p=!0){let d=`#_notifyAllSubscribers(${e})`;this._debug(d,"begin",a,`broadcast = ${p}`);try{this.broadcastChannel&&p&&this.broadcastChannel.postMessage({event:e,session:a});let d=[],t=Array.from(this.stateChangeEmitters.values()).map(async p=>{try{await p.callback(e,a)}catch(e){d.push(e)}});if(await Promise.all(t),d.length>0){for(let e=0;e<d.length;e+=1)console.error(d[e]);throw d[0]}}finally{this._debug(d,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await e5(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await e7(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");let e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&e0()&&(null==window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(e){console.error("removing visibilitychange callback failed",e)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");let e=setInterval(()=>this._autoRefreshTokenTick(),3e4);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");let e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{let e=Date.now();try{return await this._useSession(async a=>{let{data:{session:p}}=a;if(!p||!p.refresh_token||!p.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");let d=Math.floor((1e3*p.expires_at-e)/3e4);this._debug("#_autoRefreshTokenTick()",`access token expires in ${d} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),d<=3&&await this._callRefreshToken(p.refresh_token)})}catch(e){console.error("Auto refresh tick failed with error. This is likely a transient error.",e)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(e.isAcquireTimeout||e instanceof aO)this._debug("auto refresh token tick lock not available");else throw e}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!e0()||!(null==window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null==window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){let a=`#_onVisibilityChanged(${e})`;this._debug(a,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{if("visible"!==document.visibilityState)return void this._debug(a,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");await this._recoverAndRefresh()}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,a,p){let d=[`provider=${encodeURIComponent(a)}`];if((null==p?void 0:p.redirectTo)&&d.push(`redirect_to=${encodeURIComponent(p.redirectTo)}`),(null==p?void 0:p.scopes)&&d.push(`scopes=${encodeURIComponent(p.scopes)}`),"pkce"===this.flowType){let[e,a]=await as(this.storage,this.storageKey),p=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(a)}`});d.push(p.toString())}if(null==p?void 0:p.queryParams){let e=new URLSearchParams(p.queryParams);d.push(e.toString())}return(null==p?void 0:p.skipBrowserRedirect)&&d.push(`skip_http_redirect=${p.skipBrowserRedirect}`),`${e}?${d.join("&")}`}async _unenroll(e){try{return await this._useSession(async a=>{var p;let{data:d,error:t}=a;return t?{data:null,error:t}:await av(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null==(p=null==d?void 0:d.session)?void 0:p.access_token})})}catch(e){if(eB(e))return{data:null,error:e};throw e}}async _enroll(e){try{return await this._useSession(async a=>{var p,d;let{data:t,error:i}=a;if(i)return{data:null,error:i};let s=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:r,error:l}=await av(this.fetch,"POST",`${this.url}/factors`,{body:s,headers:this.headers,jwt:null==(p=null==t?void 0:t.session)?void 0:p.access_token});return l?{data:null,error:l}:("totp"===e.factorType&&(null==(d=null==r?void 0:r.totp)?void 0:d.qr_code)&&(r.totp.qr_code=`data:image/svg+xml;utf-8,${r.totp.qr_code}`),{data:r,error:null})})}catch(e){if(eB(e))return{data:null,error:e};throw e}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async a=>{var p;let{data:d,error:t}=a;if(t)return{data:null,error:t};let{data:i,error:s}=await av(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null==(p=null==d?void 0:d.session)?void 0:p.access_token});return s?{data:null,error:s}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+i.expires_in},i)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",i),{data:i,error:s})})}catch(e){if(eB(e))return{data:null,error:e};throw e}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async a=>{var p;let{data:d,error:t}=a;return t?{data:null,error:t}:await av(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null==(p=null==d?void 0:d.session)?void 0:p.access_token})})}catch(e){if(eB(e))return{data:null,error:e};throw e}})}async _challengeAndVerify(e){let{data:a,error:p}=await this._challenge({factorId:e.factorId});return p?{data:null,error:p}:await this._verify({factorId:e.factorId,challengeId:a.id,code:e.code})}async _listFactors(){let{data:{user:e},error:a}=await this.getUser();if(a)return{data:null,error:a};let p=(null==e?void 0:e.factors)||[],d=p.filter(e=>"totp"===e.factor_type&&"verified"===e.status),t=p.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:p,totp:d,phone:t},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var a,p;let{data:{session:d},error:t}=e;if(t)return{data:null,error:t};if(!d)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};let{payload:i}=aa(d.access_token),s=null;i.aal&&(s=i.aal);let r=s;return(null!=(p=null==(a=d.user.factors)?void 0:a.filter(e=>"verified"===e.status))?p:[]).length>0&&(r="aal2"),{data:{currentLevel:s,nextLevel:r,currentAuthenticationMethods:i.amr||[]},error:null}}))}async fetchJwk(e,a={keys:[]}){let p=a.keys.find(a=>a.kid===e);if(p||(p=this.jwks.keys.find(a=>a.kid===e))&&this.jwks_cached_at+6e5>Date.now())return p;let{data:d,error:t}=await av(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(t)throw t;if(!d.keys||0===d.keys.length)throw new eJ("JWKS is empty");if(this.jwks=d,this.jwks_cached_at=Date.now(),!(p=d.keys.find(a=>a.kid===e)))throw new eJ("No matching signing key found in JWKS");return p}async getClaims(e,a={keys:[]}){try{let d=e;if(!d){let{data:e,error:a}=await this.getSession();if(a||!e.session)return{data:null,error:a};d=e.session.access_token}let{header:t,payload:i,signature:s,raw:{header:r,payload:l}}=aa(d);var p=i.exp;if(!p)throw Error("Missing exp claim");if(p<=Math.floor(Date.now()/1e3))throw Error("JWT has expired");if(!t.kid||"HS256"===t.alg||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){let{error:e}=await this.getUser(d);if(e)throw e;return{data:{claims:i,header:t,signature:s},error:null}}let o=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw Error("Invalid alg claim")}}(t.alg),n=await this.fetchJwk(t.kid,a),m=await crypto.subtle.importKey("jwk",n,o,!0,["verify"]);if(!await crypto.subtle.verify(o,m,s,function(e){let a=[];return function(e,a){for(let p=0;p<e.length;p+=1){let d=e.charCodeAt(p);if(d>55295&&d<=56319){let a=(d-55296)*1024&65535;d=(e.charCodeAt(p+1)-56320&65535|a)+65536,p+=1}!function(e,a){if(e<=127)return a(e);if(e<=2047){a(192|e>>6),a(128|63&e);return}if(e<=65535){a(224|e>>12),a(128|e>>6&63),a(128|63&e);return}if(e<=1114111){a(240|e>>18),a(128|e>>12&63),a(128|e>>6&63),a(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}(d,a)}}(e,e=>a.push(e)),new Uint8Array(a)}(`${r}.${l}`)))throw new eJ("Invalid JWT signature");return{data:{claims:i,header:t,signature:s},error:null}}catch(e){if(eB(e))return{data:null,error:e};throw e}}}aj.nextInstanceID=0;let aR=aj;class aL extends aR{constructor(e){super(e)}}class aB{constructor(e,a,p){var d,t,i;if(this.supabaseUrl=e,this.supabaseKey=a,!e)throw Error("supabaseUrl is required.");if(!a)throw Error("supabaseKey is required.");let s=new URL(function(e){return e.endsWith("/")?e:e+"/"}(e));this.realtimeUrl=new URL("realtime/v1",s),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",s),this.storageUrl=new URL("storage/v1",s),this.functionsUrl=new URL("functions/v1",s);let r=`sb-${s.hostname.split(".")[0]}-auth-token`,l=function(e,a){var p,d;let{db:t,auth:i,realtime:s,global:r}=e,{db:l,auth:o,realtime:n,global:m}=a,h={db:Object.assign(Object.assign({},l),t),auth:Object.assign(Object.assign({},o),i),realtime:Object.assign(Object.assign({},n),s),global:Object.assign(Object.assign(Object.assign({},m),r),{headers:Object.assign(Object.assign({},null!=(p=null==m?void 0:m.headers)?p:{}),null!=(d=null==r?void 0:r.headers)?d:{})}),accessToken:()=>{var e,a,p,d;return e=this,a=void 0,d=function*(){return""},new(p=void 0,p=Promise)(function(t,i){function s(e){try{l(d.next(e))}catch(e){i(e)}}function r(e){try{l(d.throw(e))}catch(e){i(e)}}function l(e){var a;e.done?t(e.value):((a=e.value)instanceof p?a:new p(function(e){e(a)})).then(s,r)}l((d=d.apply(e,a||[])).next())})}};return e.accessToken?h.accessToken=e.accessToken:delete h.accessToken,h}(null!=p?p:{},{db:eT,realtime:ek,auth:Object.assign(Object.assign({},eN),{storageKey:r}),global:eS});this.storageKey=null!=(d=l.auth.storageKey)?d:"",this.headers=null!=(t=l.global.headers)?t:{},l.accessToken?(this.accessToken=l.accessToken,this.auth=new Proxy({},{get:(e,a)=>{throw Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(a)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!=(i=l.auth)?i:{},this.headers,l.global.fetch),this.fetch=eC(a,this._getAccessToken.bind(this),l.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},l.realtime)),this.rest=new b(new URL("rest/v1",s).href,{headers:this.headers,schema:l.db.schema,fetch:this.fetch}),l.accessToken||this._listenForAuthEvents()}get functions(){return new y(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new ey(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,a={},p={}){return this.rest.rpc(e,a,p)}channel(e,a={config:{}}){return this.realtime.channel(e,a)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,a,p,d,t,i;return p=this,d=void 0,t=void 0,i=function*(){if(this.accessToken)return yield this.accessToken();let{data:p}=yield this.auth.getSession();return null!=(a=null==(e=p.session)?void 0:e.access_token)?a:null},new(t||(t=Promise))(function(e,a){function s(e){try{l(i.next(e))}catch(e){a(e)}}function r(e){try{l(i.throw(e))}catch(e){a(e)}}function l(a){var p;a.done?e(a.value):((p=a.value)instanceof t?p:new t(function(e){e(p)})).then(s,r)}l((i=i.apply(p,d||[])).next())})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:a,detectSessionInUrl:p,storage:d,storageKey:t,flowType:i,lock:s,debug:r},l,o){let n={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new aL({url:this.authUrl.href,headers:Object.assign(Object.assign({},n),l),storageKey:t,autoRefreshToken:e,persistSession:a,detectSessionInUrl:p,storage:d,flowType:i,lock:s,debug:r,fetch:o,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new X(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,a)=>{this._handleTokenChanged(e,"CLIENT",null==a?void 0:a.access_token)})}_handleTokenChanged(e,a,p){("TOKEN_REFRESHED"===e||"SIGNED_IN"===e)&&this.changedAccessToken!==p?this.changedAccessToken=p:"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==a&&this.auth.signOut(),this.changedAccessToken=void 0)}}let aI=(e,a,p)=>new aB(e,a,p)},5092:e=>{e.exports.mixin=function(e,a){let p=Object.getOwnPropertyNames(a);for(let d=0;d<p.length;++d)Object.defineProperty(e,p[d],Object.getOwnPropertyDescriptor(a,p[d]))},e.exports.wrapperSymbol=Symbol("wrapper"),e.exports.implSymbol=Symbol("impl"),e.exports.wrapperForImpl=function(a){return a[e.exports.wrapperSymbol]},e.exports.implForWrapper=function(a){return a[e.exports.implSymbol]}},7367:(e,a,p)=>{let d,t=p(74075),i=p(12942),s=p(89771),{kStatusCode:r}=p(31394),l=Buffer[Symbol.species],o=Buffer.from([0,0,255,255]),n=Symbol("permessage-deflate"),m=Symbol("total-length"),h=Symbol("callback"),u=Symbol("buffers"),c=Symbol("error");class v{constructor(e,a,p){this._maxPayload=0|p,this._options=e||{},this._threshold=void 0!==this._options.threshold?this._options.threshold:1024,this._isServer=!!a,this._deflate=null,this._inflate=null,this.params=null,d||(d=new s(void 0!==this._options.concurrencyLimit?this._options.concurrencyLimit:10))}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:null==this._options.clientMaxWindowBits&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[h];this._deflate.close(),this._deflate=null,e&&e(Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let a=this._options,p=e.find(e=>(!1!==a.serverNoContextTakeover||!e.server_no_context_takeover)&&(!e.server_max_window_bits||!1!==a.serverMaxWindowBits&&("number"!=typeof a.serverMaxWindowBits||!(a.serverMaxWindowBits>e.server_max_window_bits)))&&("number"!=typeof a.clientMaxWindowBits||!!e.client_max_window_bits));if(!p)throw Error("None of the extension offers can be accepted");return a.serverNoContextTakeover&&(p.server_no_context_takeover=!0),a.clientNoContextTakeover&&(p.client_no_context_takeover=!0),"number"==typeof a.serverMaxWindowBits&&(p.server_max_window_bits=a.serverMaxWindowBits),"number"==typeof a.clientMaxWindowBits?p.client_max_window_bits=a.clientMaxWindowBits:(!0===p.client_max_window_bits||!1===a.clientMaxWindowBits)&&delete p.client_max_window_bits,p}acceptAsClient(e){let a=e[0];if(!1===this._options.clientNoContextTakeover&&a.client_no_context_takeover)throw Error('Unexpected parameter "client_no_context_takeover"');if(a.client_max_window_bits){if(!1===this._options.clientMaxWindowBits||"number"==typeof this._options.clientMaxWindowBits&&a.client_max_window_bits>this._options.clientMaxWindowBits)throw Error('Unexpected or invalid parameter "client_max_window_bits"')}else"number"==typeof this._options.clientMaxWindowBits&&(a.client_max_window_bits=this._options.clientMaxWindowBits);return a}normalizeParams(e){return e.forEach(e=>{Object.keys(e).forEach(a=>{let p=e[a];if(p.length>1)throw Error(`Parameter "${a}" must have only a single value`);if(p=p[0],"client_max_window_bits"===a){if(!0!==p){let e=+p;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${a}": ${p}`);p=e}else if(!this._isServer)throw TypeError(`Invalid value for parameter "${a}": ${p}`)}else if("server_max_window_bits"===a){let e=+p;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${a}": ${p}`);p=e}else if("client_no_context_takeover"===a||"server_no_context_takeover"===a){if(!0!==p)throw TypeError(`Invalid value for parameter "${a}": ${p}`)}else throw Error(`Unknown parameter "${a}"`);e[a]=p})}),e}decompress(e,a,p){d.add(d=>{this._decompress(e,a,(e,a)=>{d(),p(e,a)})})}compress(e,a,p){d.add(d=>{this._compress(e,a,(e,a)=>{d(),p(e,a)})})}_decompress(e,a,p){let d=this._isServer?"client":"server";if(!this._inflate){let e=`${d}_max_window_bits`,a="number"!=typeof this.params[e]?t.Z_DEFAULT_WINDOWBITS:this.params[e];this._inflate=t.createInflateRaw({...this._options.zlibInflateOptions,windowBits:a}),this._inflate[n]=this,this._inflate[m]=0,this._inflate[u]=[],this._inflate.on("error",_),this._inflate.on("data",w)}this._inflate[h]=p,this._inflate.write(e),a&&this._inflate.write(o),this._inflate.flush(()=>{let e=this._inflate[c];if(e){this._inflate.close(),this._inflate=null,p(e);return}let t=i.concat(this._inflate[u],this._inflate[m]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[m]=0,this._inflate[u]=[],a&&this.params[`${d}_no_context_takeover`]&&this._inflate.reset()),p(null,t)})}_compress(e,a,p){let d=this._isServer?"server":"client";if(!this._deflate){let e=`${d}_max_window_bits`,a="number"!=typeof this.params[e]?t.Z_DEFAULT_WINDOWBITS:this.params[e];this._deflate=t.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:a}),this._deflate[m]=0,this._deflate[u]=[],this._deflate.on("data",f)}this._deflate[h]=p,this._deflate.write(e),this._deflate.flush(t.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let e=i.concat(this._deflate[u],this._deflate[m]);a&&(e=new l(e.buffer,e.byteOffset,e.length-4)),this._deflate[h]=null,this._deflate[m]=0,this._deflate[u]=[],a&&this.params[`${d}_no_context_takeover`]&&this._deflate.reset(),p(null,e)})}}function f(e){this[u].push(e),this[m]+=e.length}function w(e){if(this[m]+=e.length,this[n]._maxPayload<1||this[m]<=this[n]._maxPayload)return void this[u].push(e);this[c]=RangeError("Max payload size exceeded"),this[c].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[c][r]=1009,this.removeListener("data",w),this.reset()}function _(e){if(this[n]._inflate=null,this[c])return void this[h](this[c]);e[r]=1007,this[h](e)}e.exports=v},8630:(e,a,p)=>{let d,{Duplex:t}=p(27910),{randomFillSync:i}=p(55511),s=p(7367),{EMPTY_BUFFER:r,kWebSocket:l,NOOP:o}=p(31394),{isBlob:n,isValidStatusCode:m}=p(10248),{mask:h,toBuffer:u}=p(12942),c=Symbol("kByteLength"),v=Buffer.alloc(4),f=8192;class w{constructor(e,a,p){this._extensions=a||{},p&&(this._generateMask=p,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._queue=[],this._state=0,this.onerror=o,this[l]=void 0}static frame(e,a){let p,t,s=!1,r=2,l=!1;a.mask&&(p=a.maskBuffer||v,a.generateMask?a.generateMask(p):(8192===f&&(void 0===d&&(d=Buffer.alloc(8192)),i(d,0,8192),f=0),p[0]=d[f++],p[1]=d[f++],p[2]=d[f++],p[3]=d[f++]),l=(p[0]|p[1]|p[2]|p[3])==0,r=6),"string"==typeof e?t=(!a.mask||l)&&void 0!==a[c]?a[c]:(e=Buffer.from(e)).length:(t=e.length,s=a.mask&&a.readOnly&&!l);let o=t;t>=65536?(r+=8,o=127):t>125&&(r+=2,o=126);let n=Buffer.allocUnsafe(s?t+r:r);return(n[0]=a.fin?128|a.opcode:a.opcode,a.rsv1&&(n[0]|=64),n[1]=o,126===o?n.writeUInt16BE(t,2):127===o&&(n[2]=n[3]=0,n.writeUIntBE(t,4,6)),a.mask)?(n[1]|=128,n[r-4]=p[0],n[r-3]=p[1],n[r-2]=p[2],n[r-1]=p[3],l)?[n,e]:s?(h(e,p,n,r,t),[n]):(h(e,p,e,0,t),[n,e]):[n,e]}close(e,a,p,d){let t;if(void 0===e)t=r;else if("number"==typeof e&&m(e))if(void 0!==a&&a.length){let p=Buffer.byteLength(a);if(p>123)throw RangeError("The message must not be greater than 123 bytes");(t=Buffer.allocUnsafe(2+p)).writeUInt16BE(e,0),"string"==typeof a?t.write(a,2):t.set(a,2)}else(t=Buffer.allocUnsafe(2)).writeUInt16BE(e,0);else throw TypeError("First argument must be a valid error code number");let i={[c]:t.length,fin:!0,generateMask:this._generateMask,mask:p,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};0!==this._state?this.enqueue([this.dispatch,t,!1,i,d]):this.sendFrame(w.frame(t,i),d)}ping(e,a,p){let d,t;if("string"==typeof e?(d=Buffer.byteLength(e),t=!1):n(e)?(d=e.size,t=!1):(d=(e=u(e)).length,t=u.readOnly),d>125)throw RangeError("The data size must not be greater than 125 bytes");let i={[c]:d,fin:!0,generateMask:this._generateMask,mask:a,maskBuffer:this._maskBuffer,opcode:9,readOnly:t,rsv1:!1};n(e)?0!==this._state?this.enqueue([this.getBlobData,e,!1,i,p]):this.getBlobData(e,!1,i,p):0!==this._state?this.enqueue([this.dispatch,e,!1,i,p]):this.sendFrame(w.frame(e,i),p)}pong(e,a,p){let d,t;if("string"==typeof e?(d=Buffer.byteLength(e),t=!1):n(e)?(d=e.size,t=!1):(d=(e=u(e)).length,t=u.readOnly),d>125)throw RangeError("The data size must not be greater than 125 bytes");let i={[c]:d,fin:!0,generateMask:this._generateMask,mask:a,maskBuffer:this._maskBuffer,opcode:10,readOnly:t,rsv1:!1};n(e)?0!==this._state?this.enqueue([this.getBlobData,e,!1,i,p]):this.getBlobData(e,!1,i,p):0!==this._state?this.enqueue([this.dispatch,e,!1,i,p]):this.sendFrame(w.frame(e,i),p)}send(e,a,p){let d,t,i=this._extensions[s.extensionName],r=a.binary?2:1,l=a.compress;"string"==typeof e?(d=Buffer.byteLength(e),t=!1):n(e)?(d=e.size,t=!1):(d=(e=u(e)).length,t=u.readOnly),this._firstFragment?(this._firstFragment=!1,l&&i&&i.params[i._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(l=d>=i._threshold),this._compress=l):(l=!1,r=0),a.fin&&(this._firstFragment=!0);let o={[c]:d,fin:a.fin,generateMask:this._generateMask,mask:a.mask,maskBuffer:this._maskBuffer,opcode:r,readOnly:t,rsv1:l};n(e)?0!==this._state?this.enqueue([this.getBlobData,e,this._compress,o,p]):this.getBlobData(e,this._compress,o,p):0!==this._state?this.enqueue([this.dispatch,e,this._compress,o,p]):this.dispatch(e,this._compress,o,p)}getBlobData(e,a,p,d){this._bufferedBytes+=p[c],this._state=2,e.arrayBuffer().then(e=>{if(this._socket.destroyed){let e=Error("The socket was closed while the blob was being read");process.nextTick(_,this,e,d);return}this._bufferedBytes-=p[c];let t=u(e);a?this.dispatch(t,a,p,d):(this._state=0,this.sendFrame(w.frame(t,p),d),this.dequeue())}).catch(e=>{process.nextTick(g,this,e,d)})}dispatch(e,a,p,d){if(!a)return void this.sendFrame(w.frame(e,p),d);let t=this._extensions[s.extensionName];this._bufferedBytes+=p[c],this._state=1,t.compress(e,p.fin,(e,a)=>{if(this._socket.destroyed)return void _(this,Error("The socket was closed while data was being compressed"),d);this._bufferedBytes-=p[c],this._state=0,p.readOnly=!1,this.sendFrame(w.frame(a,p),d),this.dequeue()})}dequeue(){for(;0===this._state&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][c],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][c],this._queue.push(e)}sendFrame(e,a){2===e.length?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],a),this._socket.uncork()):this._socket.write(e[0],a)}}function _(e,a,p){"function"==typeof p&&p(a);for(let p=0;p<e._queue.length;p++){let d=e._queue[p],t=d[d.length-1];"function"==typeof t&&t(a)}}function g(e,a,p){_(e,a,p),e.onerror(a)}e.exports=w},9301:function(e,a,p){var d=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(a,"__esModule",{value:!0});let t=d(p(56537));class i{constructor(e,{headers:a={},schema:p,fetch:d}){this.url=e,this.headers=a,this.schema=p,this.fetch=d}select(e,{head:a=!1,count:p}={}){let d=!1,i=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!d?"":('"'===e&&(d=!d),e)).join("");return this.url.searchParams.set("select",i),p&&(this.headers.Prefer=`count=${p}`),new t.default({method:a?"HEAD":"GET",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:a,defaultToNull:p=!0}={}){let d=[];if(this.headers.Prefer&&d.push(this.headers.Prefer),a&&d.push(`count=${a}`),p||d.push("missing=default"),this.headers.Prefer=d.join(","),Array.isArray(e)){let a=e.reduce((e,a)=>e.concat(Object.keys(a)),[]);if(a.length>0){let e=[...new Set(a)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new t.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:a,ignoreDuplicates:p=!1,count:d,defaultToNull:i=!0}={}){let s=[`resolution=${p?"ignore":"merge"}-duplicates`];if(void 0!==a&&this.url.searchParams.set("on_conflict",a),this.headers.Prefer&&s.push(this.headers.Prefer),d&&s.push(`count=${d}`),i||s.push("missing=default"),this.headers.Prefer=s.join(","),Array.isArray(e)){let a=e.reduce((e,a)=>e.concat(Object.keys(a)),[]);if(a.length>0){let e=[...new Set(a)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new t.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:a}={}){let p=[];return this.headers.Prefer&&p.push(this.headers.Prefer),a&&p.push(`count=${a}`),this.headers.Prefer=p.join(","),new t.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){let a=[];return e&&a.push(`count=${e}`),this.headers.Prefer&&a.unshift(this.headers.Prefer),this.headers.Prefer=a.join(","),new t.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}a.default=i},10248:(e,a,p)=>{let{isUtf8:d}=p(79428),{hasBlob:t}=p(31394);function i(e){let a=e.length,p=0;for(;p<a;)if((128&e[p])==0)p++;else if((224&e[p])==192){if(p+1===a||(192&e[p+1])!=128||(254&e[p])==192)return!1;p+=2}else if((240&e[p])==224){if(p+2>=a||(192&e[p+1])!=128||(192&e[p+2])!=128||224===e[p]&&(224&e[p+1])==128||237===e[p]&&(224&e[p+1])==160)return!1;p+=3}else{if((248&e[p])!=240||p+3>=a||(192&e[p+1])!=128||(192&e[p+2])!=128||(192&e[p+3])!=128||240===e[p]&&(240&e[p+1])==128||244===e[p]&&e[p+1]>143||e[p]>244)return!1;p+=4}return!0}if(e.exports={isBlob:function(e){return t&&"object"==typeof e&&"function"==typeof e.arrayBuffer&&"string"==typeof e.type&&"function"==typeof e.stream&&("Blob"===e[Symbol.toStringTag]||"File"===e[Symbol.toStringTag])},isValidStatusCode:function(e){return e>=1e3&&e<=1014&&1004!==e&&1005!==e&&1006!==e||e>=3e3&&e<=4999},isValidUTF8:i,tokenChars:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0]},d)e.exports.isValidUTF8=function(e){return e.length<24?i(e):d(e)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let a=p(1241);e.exports.isValidUTF8=function(e){return e.length<32?i(e):a(e)}}catch(e){}},12942:(e,a,p)=>{let{EMPTY_BUFFER:d}=p(31394),t=Buffer[Symbol.species];function i(e,a,p,d,t){for(let i=0;i<t;i++)p[d+i]=e[i]^a[3&i]}function s(e,a){for(let p=0;p<e.length;p++)e[p]^=a[3&p]}function r(e){let a;return(r.readOnly=!0,Buffer.isBuffer(e))?e:(e instanceof ArrayBuffer?a=new t(e):ArrayBuffer.isView(e)?a=new t(e.buffer,e.byteOffset,e.byteLength):(a=Buffer.from(e),r.readOnly=!1),a)}if(e.exports={concat:function(e,a){if(0===e.length)return d;if(1===e.length)return e[0];let p=Buffer.allocUnsafe(a),i=0;for(let a=0;a<e.length;a++){let d=e[a];p.set(d,i),i+=d.length}return i<a?new t(p.buffer,p.byteOffset,i):p},mask:i,toArrayBuffer:function(e){return e.length===e.buffer.byteLength?e.buffer:e.buffer.slice(e.byteOffset,e.byteOffset+e.length)},toBuffer:r,unmask:s},!process.env.WS_NO_BUFFER_UTIL)try{let a=p(72088);e.exports.mask=function(e,p,d,t,s){s<48?i(e,p,d,t,s):a.mask(e,p,d,t,s)},e.exports.unmask=function(e,p){e.length<32?s(e,p):a.unmask(e,p)}}catch(e){}},20246:(e,a)=>{Object.defineProperty(a,"__esModule",{value:!0});class p extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}}a.default=p},31394:e=>{let a=["nodebuffer","arraybuffer","fragments"],p="undefined"!=typeof Blob;p&&a.push("blob"),e.exports={BINARY_TYPES:a,EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",hasBlob:p,kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}},32980:(e,a,p)=>{let d=p(11997),t=p(57113),i={ftp:21,file:null,gopher:70,http:80,https:443,ws:80,wss:443},s=Symbol("failure");function r(e){return d.ucs2.decode(e).length}function l(e,a){let p=e[a];return isNaN(p)?void 0:String.fromCodePoint(p)}function o(e){return e>=48&&e<=57}function n(e){return e>=65&&e<=90||e>=97&&e<=122}function m(e){return o(e)||e>=65&&e<=70||e>=97&&e<=102}function h(e){return"."===e||"%2e"===e.toLowerCase()}function u(e){return 2===e.length&&n(e.codePointAt(0))&&(":"===e[1]||"|"===e[1])}function c(e){return void 0!==i[e]}function v(e){return c(e.scheme)}function f(e){let a=e.toString(16).toUpperCase();return 1===a.length&&(a="0"+a),"%"+a}function w(e){return e<=31||e>126}let _=new Set([32,34,35,60,62,63,96,123,125]);function g(e){return w(e)||_.has(e)}let y=new Set([47,58,59,61,64,91,92,93,94,124]);function b(e){return g(e)||y.has(e)}function S(e,a){let p=String.fromCodePoint(e);if(a(e)){let e=new Buffer(p),a="";for(let p=0;p<e.length;++p)a+=f(e[p]);return a}return p}function T(e,a){if("["===e[0])return"]"!==e[e.length-1]?s:function(e){let a=[0,0,0,0,0,0,0,0],p=0,t=null,i=0;if(58===(e=d.ucs2.decode(e))[i]){if(58!==e[i+1])return s;i+=2,t=++p}for(;i<e.length;){if(8===p)return s;if(58===e[i]){if(null!==t)return s;++i,t=++p;continue}let d=0,r=0;for(;r<4&&m(e[i]);)d=16*d+parseInt(l(e,i),16),++i,++r;if(46===e[i]){if(0===r||(i-=r,p>6))return s;let d=0;for(;void 0!==e[i];){let t=null;if(d>0)if(46!==e[i]||!(d<4))return s;else++i;if(!o(e[i]))return s;for(;o(e[i]);){let a=parseInt(l(e,i));if(null===t)t=a;else{if(0===t)return s;t=10*t+a}if(t>255)return s;++i}a[p]=256*a[p]+t,(2==++d||4===d)&&++p}if(4!==d)return s;break}if(58===e[i]){if(void 0===e[++i])return s}else if(void 0!==e[i])return s;a[p]=d,++p}if(null!==t){let e=p-t;for(p=7;0!==p&&e>0;){let d=a[t+e-1];a[t+e-1]=a[p],a[p]=d,--p,--e}}else if(null===t&&8!==p)return s;return a}(e.substring(1,e.length-1));if(!a){var p=e;if(-1!==p.search(/\u0000|\u0009|\u000A|\u000D|\u0020|#|\/|:|\?|@|\[|\\|\]/))return s;let a="",t=d.ucs2.decode(p);for(let e=0;e<t.length;++e)a+=S(t[e],w);return a}let i=function(e){let a=new Buffer(e),p=[];for(let e=0;e<a.length;++e)37!==a[e]?p.push(a[e]):37===a[e]&&m(a[e+1])&&m(a[e+2])?(p.push(parseInt(a.slice(e+1,e+3).toString(),16)),e+=2):p.push(a[e]);return new Buffer(p).toString()}(e),r=t.toASCII(i,!1,t.PROCESSING_OPTIONS.NONTRANSITIONAL,!1);if(null===r||-1!==r.search(/\u0000|\u0009|\u000A|\u000D|\u0020|#|%|\/|:|\?|@|\[|\\|\]/))return s;let n=function(e){let a=e.split(".");if(""===a[a.length-1]&&a.length>1&&a.pop(),a.length>4)return e;let p=[];for(let d of a){if(""===d)return e;let a=function(e){let a=10;return(e.length>=2&&"0"===e.charAt(0)&&"x"===e.charAt(1).toLowerCase()?(e=e.substring(2),a=16):e.length>=2&&"0"===e.charAt(0)&&(e=e.substring(1),a=8),""===e)?0:(10===a?/[^0-9]/:16===a?/[^0-9A-Fa-f]/:/[^0-7]/).test(e)?s:parseInt(e,a)}(d);if(a===s)return e;p.push(a)}for(let e=0;e<p.length-1;++e)if(p[e]>255)return s;if(p[p.length-1]>=Math.pow(256,5-p.length))return s;let d=p.pop(),t=0;for(let e of p)d+=e*Math.pow(256,3-t),++t;return d}(r);return"number"==typeof n||n===s?n:r}function N(e){if("number"==typeof e){let a="",p=e;for(let e=1;e<=4;++e)a=String(p%256)+a,4!==e&&(a="."+a),p=Math.floor(p/256);return a}return e instanceof Array?"["+function(e){let a="",p=function(e){let a=null,p=1,d=null,t=0;for(let i=0;i<e.length;++i)0!==e[i]?(t>p&&(a=d,p=t),d=null,t=0):(null===d&&(d=i),++t);return t>p&&(a=d,p=t),{idx:a,len:p}}(e).idx,d=!1;for(let t=0;t<=7;++t)if(!d||0!==e[t]){if(d&&(d=!1),p===t){a+=0===t?"::":":",d=!0;continue}a+=e[t].toString(16),7!==t&&(a+=":")}return a}(e)+"]":e}function k(e){let a=e.path;if(0!==a.length){var p;"file"===e.scheme&&1===a.length&&(p=a[0],/^[A-Za-z]:$/.test(p))||a.pop()}}function E(e){return""!==e.username||""!==e.password}function D(e,a,p,t,i){if(this.pointer=0,this.input=e,this.base=a||null,this.encodingOverride=p||"utf-8",this.stateOverride=i,this.url=t,this.failure=!1,this.parseError=!1,!this.url){this.url={scheme:"",username:"",password:"",host:null,port:null,path:[],query:null,fragment:null,cannotBeABaseURL:!1};let e=this.input.replace(/^[\u0000-\u001F\u0020]+|[\u0000-\u001F\u0020]+$/g,"");e!==this.input&&(this.parseError=!0),this.input=e}let r=this.input.replace(/\u0009|\u000A|\u000D/g,"");for(r!==this.input&&(this.parseError=!0),this.input=r,this.state=i||"scheme start",this.buffer="",this.atFlag=!1,this.arrFlag=!1,this.passwordTokenSeenFlag=!1,this.input=d.ucs2.decode(this.input);this.pointer<=this.input.length;++this.pointer){let e=this.input[this.pointer],a=isNaN(e)?void 0:String.fromCodePoint(e),p=this["parse "+this.state](e,a);if(p){if(p===s){this.failure=!0;break}}else break}}D.prototype["parse scheme start"]=function(e,a){if(n(e))this.buffer+=a.toLowerCase(),this.state="scheme";else{if(this.stateOverride)return this.parseError=!0,s;this.state="no scheme",--this.pointer}return!0},D.prototype["parse scheme"]=function(e,a){if(n(e)||o(e)||43===e||45===e||46===e)this.buffer+=a.toLowerCase();else if(58===e){if(this.stateOverride&&(v(this.url)&&!c(this.buffer)||!v(this.url)&&c(this.buffer)||(E(this.url)||null!==this.url.port)&&"file"===this.buffer||"file"===this.url.scheme&&(""===this.url.host||null===this.url.host))||(this.url.scheme=this.buffer,this.buffer="",this.stateOverride))return!1;"file"===this.url.scheme?((47!==this.input[this.pointer+1]||47!==this.input[this.pointer+2])&&(this.parseError=!0),this.state="file"):v(this.url)&&null!==this.base&&this.base.scheme===this.url.scheme?this.state="special relative or authority":v(this.url)?this.state="special authority slashes":47===this.input[this.pointer+1]?(this.state="path or authority",++this.pointer):(this.url.cannotBeABaseURL=!0,this.url.path.push(""),this.state="cannot-be-a-base-URL path")}else{if(this.stateOverride)return this.parseError=!0,s;this.buffer="",this.state="no scheme",this.pointer=-1}return!0},D.prototype["parse no scheme"]=function(e){return null===this.base||this.base.cannotBeABaseURL&&35!==e?s:(this.base.cannotBeABaseURL&&35===e?(this.url.scheme=this.base.scheme,this.url.path=this.base.path.slice(),this.url.query=this.base.query,this.url.fragment="",this.url.cannotBeABaseURL=!0,this.state="fragment"):("file"===this.base.scheme?this.state="file":this.state="relative",--this.pointer),!0)},D.prototype["parse special relative or authority"]=function(e){return 47===e&&47===this.input[this.pointer+1]?(this.state="special authority ignore slashes",++this.pointer):(this.parseError=!0,this.state="relative",--this.pointer),!0},D.prototype["parse path or authority"]=function(e){return 47===e?this.state="authority":(this.state="path",--this.pointer),!0},D.prototype["parse relative"]=function(e){return this.url.scheme=this.base.scheme,isNaN(e)?(this.url.username=this.base.username,this.url.password=this.base.password,this.url.host=this.base.host,this.url.port=this.base.port,this.url.path=this.base.path.slice(),this.url.query=this.base.query):47===e?this.state="relative slash":63===e?(this.url.username=this.base.username,this.url.password=this.base.password,this.url.host=this.base.host,this.url.port=this.base.port,this.url.path=this.base.path.slice(),this.url.query="",this.state="query"):35===e?(this.url.username=this.base.username,this.url.password=this.base.password,this.url.host=this.base.host,this.url.port=this.base.port,this.url.path=this.base.path.slice(),this.url.query=this.base.query,this.url.fragment="",this.state="fragment"):v(this.url)&&92===e?(this.parseError=!0,this.state="relative slash"):(this.url.username=this.base.username,this.url.password=this.base.password,this.url.host=this.base.host,this.url.port=this.base.port,this.url.path=this.base.path.slice(0,this.base.path.length-1),this.state="path",--this.pointer),!0},D.prototype["parse relative slash"]=function(e){return v(this.url)&&(47===e||92===e)?(92===e&&(this.parseError=!0),this.state="special authority ignore slashes"):47===e?this.state="authority":(this.url.username=this.base.username,this.url.password=this.base.password,this.url.host=this.base.host,this.url.port=this.base.port,this.state="path",--this.pointer),!0},D.prototype["parse special authority slashes"]=function(e){return 47===e&&47===this.input[this.pointer+1]?(this.state="special authority ignore slashes",++this.pointer):(this.parseError=!0,this.state="special authority ignore slashes",--this.pointer),!0},D.prototype["parse special authority ignore slashes"]=function(e){return 47!==e&&92!==e?(this.state="authority",--this.pointer):this.parseError=!0,!0},D.prototype["parse authority"]=function(e,a){if(64===e){this.parseError=!0,this.atFlag&&(this.buffer="%40"+this.buffer),this.atFlag=!0;let e=r(this.buffer);for(let a=0;a<e;++a){let e=this.buffer.codePointAt(a);if(58===e&&!this.passwordTokenSeenFlag){this.passwordTokenSeenFlag=!0;continue}let p=S(e,b);this.passwordTokenSeenFlag?this.url.password+=p:this.url.username+=p}this.buffer=""}else if(isNaN(e)||47===e||63===e||35===e||v(this.url)&&92===e){if(this.atFlag&&""===this.buffer)return this.parseError=!0,s;this.pointer-=r(this.buffer)+1,this.buffer="",this.state="host"}else this.buffer+=a;return!0},D.prototype["parse hostname"]=D.prototype["parse host"]=function(e,a){if(this.stateOverride&&"file"===this.url.scheme)--this.pointer,this.state="file host";else if(58!==e||this.arrFlag)if(isNaN(e)||47===e||63===e||35===e||v(this.url)&&92===e){if(--this.pointer,v(this.url)&&""===this.buffer)return this.parseError=!0,s;if(this.stateOverride&&""===this.buffer&&(E(this.url)||null!==this.url.port))return this.parseError=!0,!1;let e=T(this.buffer,v(this.url));if(e===s)return s;if(this.url.host=e,this.buffer="",this.state="path start",this.stateOverride)return!1}else 91===e?this.arrFlag=!0:93===e&&(this.arrFlag=!1),this.buffer+=a;else{if(""===this.buffer)return this.parseError=!0,s;let e=T(this.buffer,v(this.url));if(e===s)return s;if(this.url.host=e,this.buffer="",this.state="port","hostname"===this.stateOverride)return!1}return!0},D.prototype["parse port"]=function(e,a){if(o(e))this.buffer+=a;else{if(!(isNaN(e)||47===e||63===e||35===e||v(this.url)&&92===e)&&!this.stateOverride)return this.parseError=!0,s;if(""!==this.buffer){let e=parseInt(this.buffer);if(e>65535)return this.parseError=!0,s;this.url.port=e===i[this.url.scheme]?null:e,this.buffer=""}if(this.stateOverride)return!1;this.state="path start",--this.pointer}return!0};let V=new Set([47,92,63,35]);D.prototype["parse file"]=function(e){if(this.url.scheme="file",47===e||92===e)92===e&&(this.parseError=!0),this.state="file slash";else if(null!==this.base&&"file"===this.base.scheme)if(isNaN(e))this.url.host=this.base.host,this.url.path=this.base.path.slice(),this.url.query=this.base.query;else if(63===e)this.url.host=this.base.host,this.url.path=this.base.path.slice(),this.url.query="",this.state="query";else if(35===e)this.url.host=this.base.host,this.url.path=this.base.path.slice(),this.url.query=this.base.query,this.url.fragment="",this.state="fragment";else{var a;this.input.length-this.pointer-1!=0&&(a=this.input[this.pointer+1],n(e)&&(58===a||124===a))&&(!(this.input.length-this.pointer-1>=2)||V.has(this.input[this.pointer+2]))?this.parseError=!0:(this.url.host=this.base.host,this.url.path=this.base.path.slice(),k(this.url)),this.state="path",--this.pointer}else this.state="path",--this.pointer;return!0},D.prototype["parse file slash"]=function(e){if(47===e||92===e)92===e&&(this.parseError=!0),this.state="file host";else{if(null!==this.base&&"file"===this.base.scheme){var a;2===(a=this.base.path[0]).length&&n(a.codePointAt(0))&&":"===a[1]?this.url.path.push(this.base.path[0]):this.url.host=this.base.host}this.state="path",--this.pointer}return!0},D.prototype["parse file host"]=function(e,a){if(isNaN(e)||47===e||92===e||63===e||35===e)if(--this.pointer,!this.stateOverride&&u(this.buffer))this.parseError=!0,this.state="path";else if(""===this.buffer){if(this.url.host="",this.stateOverride)return!1;this.state="path start"}else{let e=T(this.buffer,v(this.url));if(e===s)return s;if("localhost"===e&&(e=""),this.url.host=e,this.stateOverride)return!1;this.buffer="",this.state="path start"}else this.buffer+=a;return!0},D.prototype["parse path start"]=function(e){return v(this.url)?(92===e&&(this.parseError=!0),this.state="path",47!==e&&92!==e&&--this.pointer):this.stateOverride||63!==e?this.stateOverride||35!==e?void 0!==e&&(this.state="path",47!==e&&--this.pointer):(this.url.fragment="",this.state="fragment"):(this.url.query="",this.state="query"),!0},D.prototype["parse path"]=function(e){if(isNaN(e)||47===e||v(this.url)&&92===e||!this.stateOverride&&(63===e||35===e)){var a;if((v(this.url)&&92===e&&(this.parseError=!0),".."===(a=(a=this.buffer).toLowerCase())||"%2e."===a||".%2e"===a||"%2e%2e"===a)?(k(this.url),47===e||v(this.url)&&92===e||this.url.path.push("")):h(this.buffer)&&47!==e&&!(v(this.url)&&92===e)?this.url.path.push(""):h(this.buffer)||("file"===this.url.scheme&&0===this.url.path.length&&u(this.buffer)&&(""!==this.url.host&&null!==this.url.host&&(this.parseError=!0,this.url.host=""),this.buffer=this.buffer[0]+":"),this.url.path.push(this.buffer)),this.buffer="","file"===this.url.scheme&&(void 0===e||63===e||35===e))for(;this.url.path.length>1&&""===this.url.path[0];)this.parseError=!0,this.url.path.shift();63===e&&(this.url.query="",this.state="query"),35===e&&(this.url.fragment="",this.state="fragment")}else 37!==e||m(this.input[this.pointer+1])&&m(this.input[this.pointer+2])||(this.parseError=!0),this.buffer+=S(e,g);return!0},D.prototype["parse cannot-be-a-base-URL path"]=function(e){return 63===e?(this.url.query="",this.state="query"):35===e?(this.url.fragment="",this.state="fragment"):(isNaN(e)||37===e||(this.parseError=!0),37!==e||m(this.input[this.pointer+1])&&m(this.input[this.pointer+2])||(this.parseError=!0),isNaN(e)||(this.url.path[0]=this.url.path[0]+S(e,w))),!0},D.prototype["parse query"]=function(e,a){if(isNaN(e)||!this.stateOverride&&35===e){v(this.url)&&"ws"!==this.url.scheme&&"wss"!==this.url.scheme||(this.encodingOverride="utf-8");let a=new Buffer(this.buffer);for(let e=0;e<a.length;++e)a[e]<33||a[e]>126||34===a[e]||35===a[e]||60===a[e]||62===a[e]?this.url.query+=f(a[e]):this.url.query+=String.fromCodePoint(a[e]);this.buffer="",35===e&&(this.url.fragment="",this.state="fragment")}else 37!==e||m(this.input[this.pointer+1])&&m(this.input[this.pointer+2])||(this.parseError=!0),this.buffer+=a;return!0},D.prototype["parse fragment"]=function(e){return isNaN(e)||(0===e?this.parseError=!0:(37!==e||m(this.input[this.pointer+1])&&m(this.input[this.pointer+2])||(this.parseError=!0),this.url.fragment+=S(e,w))),!0},e.exports.serializeURL=function(e,a){let p=e.scheme+":";if(null!==e.host?(p+="//",(""!==e.username||""!==e.password)&&(p+=e.username,""!==e.password&&(p+=":"+e.password),p+="@"),p+=N(e.host),null!==e.port&&(p+=":"+e.port)):null===e.host&&"file"===e.scheme&&(p+="//"),e.cannotBeABaseURL)p+=e.path[0];else for(let a of e.path)p+="/"+a;return null!==e.query&&(p+="?"+e.query),a||null===e.fragment||(p+="#"+e.fragment),p},e.exports.serializeURLOrigin=function(a){switch(a.scheme){case"blob":try{return e.exports.serializeURLOrigin(e.exports.parseURL(a.path[0]))}catch(e){return"null"}case"ftp":case"gopher":case"http":case"https":case"ws":case"wss":var p;let d;return d=(p={scheme:a.scheme,host:a.host,port:a.port}).scheme+"://"+N(p.host),null!==p.port&&(d+=":"+p.port),d;case"file":return"file://";default:return"null"}},e.exports.basicURLParse=function(e,a){void 0===a&&(a={});let p=new D(e,a.baseURL,a.encodingOverride,a.url,a.stateOverride);return p.failure?"failure":p.url},e.exports.setTheUsername=function(e,a){e.username="";let p=d.ucs2.decode(a);for(let a=0;a<p.length;++a)e.username+=S(p[a],b)},e.exports.setThePassword=function(e,a){e.password="";let p=d.ucs2.decode(a);for(let a=0;a<p.length;++a)e.password+=S(p[a],b)},e.exports.serializeHost=N,e.exports.cannotHaveAUsernamePasswordPort=function(e){return null===e.host||""===e.host||e.cannotBeABaseURL||"file"===e.scheme},e.exports.serializeInteger=function(e){return String(e)},e.exports.parseURL=function(a,p){return void 0===p&&(p={}),e.exports.basicURLParse(a,{baseURL:p.baseURL,encodingOverride:p.encodingOverride})}},36122:(e,a,p)=>{let{Writable:d}=p(27910),t=p(7367),{BINARY_TYPES:i,EMPTY_BUFFER:s,kStatusCode:r,kWebSocket:l}=p(31394),{concat:o,toArrayBuffer:n,unmask:m}=p(12942),{isValidStatusCode:h,isValidUTF8:u}=p(10248),c=Buffer[Symbol.species];class v extends d{constructor(e={}){super(),this._allowSynchronousEvents=void 0===e.allowSynchronousEvents||e.allowSynchronousEvents,this._binaryType=e.binaryType||i[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=0|e.maxPayload,this._skipUTF8Validation=!!e.skipUTF8Validation,this[l]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=0}_write(e,a,p){if(8===this._opcode&&0==this._state)return p();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(p)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let a=this._buffers[0];return this._buffers[0]=new c(a.buffer,a.byteOffset+e,a.length-e),new c(a.buffer,a.byteOffset,e)}let a=Buffer.allocUnsafe(e);do{let p=this._buffers[0],d=a.length-e;e>=p.length?a.set(this._buffers.shift(),d):(a.set(new Uint8Array(p.buffer,p.byteOffset,e),d),this._buffers[0]=new c(p.buffer,p.byteOffset+e,p.length-e)),e-=p.length}while(e>0);return a}startLoop(e){this._loop=!0;do switch(this._state){case 0:this.getInfo(e);break;case 1:this.getPayloadLength16(e);break;case 2:this.getPayloadLength64(e);break;case 3:this.getMask();break;case 4:this.getData(e);break;case 5:case 6:this._loop=!1;return}while(this._loop);this._errored||e()}getInfo(e){if(this._bufferedBytes<2){this._loop=!1;return}let a=this.consume(2);if((48&a[0])!=0)return void e(this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3"));let p=(64&a[0])==64;if(p&&!this._extensions[t.extensionName])return void e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));if(this._fin=(128&a[0])==128,this._opcode=15&a[0],this._payloadLength=127&a[1],0===this._opcode){if(p)return void e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));if(!this._fragmented)return void e(this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE"));this._opcode=this._fragmented}else if(1===this._opcode||2===this._opcode){if(this._fragmented)return void e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));this._compressed=p}else{if(!(this._opcode>7)||!(this._opcode<11))return void e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));if(!this._fin)return void e(this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN"));if(p)return void e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));if(this._payloadLength>125||8===this._opcode&&1===this._payloadLength)return void e(this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH"))}if(this._fin||this._fragmented||(this._fragmented=this._opcode),this._masked=(128&a[1])==128,this._isServer){if(!this._masked)return void e(this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK"))}else if(this._masked)return void e(this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK"));126===this._payloadLength?this._state=1:127===this._payloadLength?this._state=2:this.haveLength(e)}getPayloadLength16(e){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(e)}getPayloadLength64(e){if(this._bufferedBytes<8){this._loop=!1;return}let a=this.consume(8),p=a.readUInt32BE(0);if(p>2097151)return void e(this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH"));this._payloadLength=0x100000000*p+a.readUInt32BE(4),this.haveLength(e)}haveLength(e){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0))return void e(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));this._masked?this._state=3:this._state=4}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=4}getData(e){let a=s;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}a=this.consume(this._payloadLength),this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!=0&&m(a,this._mask)}if(this._opcode>7)return void this.controlMessage(a,e);if(this._compressed){this._state=5,this.decompress(a,e);return}a.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(a)),this.dataMessage(e)}decompress(e,a){this._extensions[t.extensionName].decompress(e,this._fin,(e,p)=>{if(e)return a(e);if(p.length){if(this._messageLength+=p.length,this._messageLength>this._maxPayload&&this._maxPayload>0)return void a(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));this._fragments.push(p)}this.dataMessage(a),0===this._state&&this.startLoop(a)})}dataMessage(e){if(!this._fin){this._state=0;return}let a=this._messageLength,p=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],2===this._opcode){let d;d="nodebuffer"===this._binaryType?o(p,a):"arraybuffer"===this._binaryType?n(o(p,a)):"blob"===this._binaryType?new Blob(p):p,this._allowSynchronousEvents?(this.emit("message",d,!0),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",d,!0),this._state=0,this.startLoop(e)}))}else{let d=o(p,a);if(!this._skipUTF8Validation&&!u(d))return void e(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));5===this._state||this._allowSynchronousEvents?(this.emit("message",d,!1),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",d,!1),this._state=0,this.startLoop(e)}))}}controlMessage(e,a){if(8===this._opcode){if(0===e.length)this._loop=!1,this.emit("conclude",1005,s),this.end();else{let p=e.readUInt16BE(0);if(!h(p))return void a(this.createError(RangeError,`invalid status code ${p}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE"));let d=new c(e.buffer,e.byteOffset+2,e.length-2);if(!this._skipUTF8Validation&&!u(d))return void a(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));this._loop=!1,this.emit("conclude",p,d),this.end()}this._state=0;return}this._allowSynchronousEvents?(this.emit(9===this._opcode?"ping":"pong",e),this._state=0):(this._state=6,setImmediate(()=>{this.emit(9===this._opcode?"ping":"pong",e),this._state=0,this.startLoop(a)}))}createError(e,a,p,d,t){this._loop=!1,this._errored=!0;let i=new e(p?`Invalid WebSocket frame: ${a}`:a);return Error.captureStackTrace(i,this.createError),i.code=t,i[r]=d,i}}e.exports=v},38624:e=>{var a={};function p(e,a){!a.unsigned&&--e;let p=a.unsigned?0:-Math.pow(2,e),d=Math.pow(2,e)-1,t=a.moduloBitLength?Math.pow(2,a.moduloBitLength):Math.pow(2,e),i=a.moduloBitLength?Math.pow(2,a.moduloBitLength-1):Math.pow(2,e-1);return function(e,s){s||(s={});let r=+e;if(s.enforceRange){if(!Number.isFinite(r))throw TypeError("Argument is not a finite number");if((r=(r<0?-1:1)*Math.floor(Math.abs(r)))<p||r>d)throw TypeError("Argument is not in byte range");return r}if(!isNaN(r)&&s.clamp){var l;return(r=(l=r)%1==.5&&(1&l)==0?Math.floor(l):Math.round(l))<p&&(r=p),r>d&&(r=d),r}if(!Number.isFinite(r)||0===r)return 0;if(r=(r<0?-1:1)*Math.floor(Math.abs(r))%t,!a.unsigned&&r>=i)return r-t;if(a.unsigned){if(r<0)r+=t;else if(-0===r)return 0}return r}}e.exports=a,a.void=function(){},a.boolean=function(e){return!!e},a.byte=p(8,{unsigned:!1}),a.octet=p(8,{unsigned:!0}),a.short=p(16,{unsigned:!1}),a["unsigned short"]=p(16,{unsigned:!0}),a.long=p(32,{unsigned:!1}),a["unsigned long"]=p(32,{unsigned:!0}),a["long long"]=p(32,{unsigned:!1,moduloBitLength:64}),a["unsigned long long"]=p(32,{unsigned:!0,moduloBitLength:64}),a.double=function(e){let a=+e;if(!Number.isFinite(a))throw TypeError("Argument is not a finite floating-point value");return a},a["unrestricted double"]=function(e){let a=+e;if(isNaN(a))throw TypeError("Argument is NaN");return a},a.float=a.double,a["unrestricted float"]=a["unrestricted double"],a.DOMString=function(e,a){return(a||(a={}),a.treatNullAsEmptyString&&null===e)?"":String(e)},a.ByteString=function(e,a){let p,d=String(e);for(let e=0;void 0!==(p=d.codePointAt(e));++e)if(p>255)throw TypeError("Argument is not a valid bytestring");return d},a.USVString=function(e){let a=String(e),p=a.length,d=[];for(let e=0;e<p;++e){let t=a.charCodeAt(e);if(t<55296||t>57343)d.push(String.fromCodePoint(t));else if(56320<=t&&t<=57343)d.push(String.fromCodePoint(65533));else if(e===p-1)d.push(String.fromCodePoint(65533));else{let p=a.charCodeAt(e+1);if(56320<=p&&p<=57343){let a=1023&t,i=1023&p;d.push(String.fromCodePoint(65536+1024*a+i)),++e}else d.push(String.fromCodePoint(65533))}}return d.join("")},a.Date=function(e,a){if(!(e instanceof Date))throw TypeError("Argument is not a Date object");if(!isNaN(e))return e},a.RegExp=function(e,a){return e instanceof RegExp||(e=new RegExp(e)),e}},44059:function(e,a,p){var d=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(a,"__esModule",{value:!0}),a.PostgrestError=a.PostgrestBuilder=a.PostgrestTransformBuilder=a.PostgrestFilterBuilder=a.PostgrestQueryBuilder=a.PostgrestClient=void 0;let t=d(p(82657));a.PostgrestClient=t.default;let i=d(p(9301));a.PostgrestQueryBuilder=i.default;let s=d(p(56537));a.PostgrestFilterBuilder=s.default;let r=d(p(86693));a.PostgrestTransformBuilder=r.default;let l=d(p(88699));a.PostgrestBuilder=l.default;let o=d(p(20246));a.PostgrestError=o.default,a.default={PostgrestClient:t.default,PostgrestQueryBuilder:i.default,PostgrestFilterBuilder:s.default,PostgrestTransformBuilder:r.default,PostgrestBuilder:l.default,PostgrestError:o.default}},47762:(e,a,p)=>{let{tokenChars:d}=p(10248);function t(e,a,p){void 0===e[a]?e[a]=[p]:e[a].push(p)}e.exports={format:function(e){return Object.keys(e).map(a=>{let p=e[a];return Array.isArray(p)||(p=[p]),p.map(e=>[a].concat(Object.keys(e).map(a=>{let p=e[a];return Array.isArray(p)||(p=[p]),p.map(e=>!0===e?a:`${a}=${e}`).join("; ")})).join("; ")).join(", ")}).join(", ")},parse:function(e){let a,p,i=Object.create(null),s=Object.create(null),r=!1,l=!1,o=!1,n=-1,m=-1,h=-1,u=0;for(;u<e.length;u++)if(m=e.charCodeAt(u),void 0===a)if(-1===h&&1===d[m])-1===n&&(n=u);else if(0!==u&&(32===m||9===m))-1===h&&-1!==n&&(h=u);else if(59===m||44===m){if(-1===n)throw SyntaxError(`Unexpected character at index ${u}`);-1===h&&(h=u);let p=e.slice(n,h);44===m?(t(i,p,s),s=Object.create(null)):a=p,n=h=-1}else throw SyntaxError(`Unexpected character at index ${u}`);else if(void 0===p)if(-1===h&&1===d[m])-1===n&&(n=u);else if(32===m||9===m)-1===h&&-1!==n&&(h=u);else if(59===m||44===m){if(-1===n)throw SyntaxError(`Unexpected character at index ${u}`);-1===h&&(h=u),t(s,e.slice(n,h),!0),44===m&&(t(i,a,s),s=Object.create(null),a=void 0),n=h=-1}else if(61===m&&-1!==n&&-1===h)p=e.slice(n,u),n=h=-1;else throw SyntaxError(`Unexpected character at index ${u}`);else if(l){if(1!==d[m])throw SyntaxError(`Unexpected character at index ${u}`);-1===n?n=u:r||(r=!0),l=!1}else if(o)if(1===d[m])-1===n&&(n=u);else if(34===m&&-1!==n)o=!1,h=u;else if(92===m)l=!0;else throw SyntaxError(`Unexpected character at index ${u}`);else if(34===m&&61===e.charCodeAt(u-1))o=!0;else if(-1===h&&1===d[m])-1===n&&(n=u);else if(-1!==n&&(32===m||9===m))-1===h&&(h=u);else if(59===m||44===m){if(-1===n)throw SyntaxError(`Unexpected character at index ${u}`);-1===h&&(h=u);let d=e.slice(n,h);r&&(d=d.replace(/\\/g,""),r=!1),t(s,p,d),44===m&&(t(i,a,s),s=Object.create(null),a=void 0),p=void 0,n=h=-1}else throw SyntaxError(`Unexpected character at index ${u}`);if(-1===n||o||32===m||9===m)throw SyntaxError("Unexpected end of input");-1===h&&(h=u);let c=e.slice(n,h);return void 0===a?t(i,c,s):(void 0===p?t(s,c,!0):r?t(s,p,c.replace(/\\/g,"")):t(s,p,c),t(i,a,s)),i}}},56537:function(e,a,p){var d=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(a,"__esModule",{value:!0});let t=d(p(86693));class i extends t.default{eq(e,a){return this.url.searchParams.append(e,`eq.${a}`),this}neq(e,a){return this.url.searchParams.append(e,`neq.${a}`),this}gt(e,a){return this.url.searchParams.append(e,`gt.${a}`),this}gte(e,a){return this.url.searchParams.append(e,`gte.${a}`),this}lt(e,a){return this.url.searchParams.append(e,`lt.${a}`),this}lte(e,a){return this.url.searchParams.append(e,`lte.${a}`),this}like(e,a){return this.url.searchParams.append(e,`like.${a}`),this}likeAllOf(e,a){return this.url.searchParams.append(e,`like(all).{${a.join(",")}}`),this}likeAnyOf(e,a){return this.url.searchParams.append(e,`like(any).{${a.join(",")}}`),this}ilike(e,a){return this.url.searchParams.append(e,`ilike.${a}`),this}ilikeAllOf(e,a){return this.url.searchParams.append(e,`ilike(all).{${a.join(",")}}`),this}ilikeAnyOf(e,a){return this.url.searchParams.append(e,`ilike(any).{${a.join(",")}}`),this}is(e,a){return this.url.searchParams.append(e,`is.${a}`),this}in(e,a){let p=Array.from(new Set(a)).map(e=>"string"==typeof e&&RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${p})`),this}contains(e,a){return"string"==typeof a?this.url.searchParams.append(e,`cs.${a}`):Array.isArray(a)?this.url.searchParams.append(e,`cs.{${a.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(a)}`),this}containedBy(e,a){return"string"==typeof a?this.url.searchParams.append(e,`cd.${a}`):Array.isArray(a)?this.url.searchParams.append(e,`cd.{${a.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(a)}`),this}rangeGt(e,a){return this.url.searchParams.append(e,`sr.${a}`),this}rangeGte(e,a){return this.url.searchParams.append(e,`nxl.${a}`),this}rangeLt(e,a){return this.url.searchParams.append(e,`sl.${a}`),this}rangeLte(e,a){return this.url.searchParams.append(e,`nxr.${a}`),this}rangeAdjacent(e,a){return this.url.searchParams.append(e,`adj.${a}`),this}overlaps(e,a){return"string"==typeof a?this.url.searchParams.append(e,`ov.${a}`):this.url.searchParams.append(e,`ov.{${a.join(",")}}`),this}textSearch(e,a,{config:p,type:d}={}){let t="";"plain"===d?t="pl":"phrase"===d?t="ph":"websearch"===d&&(t="w");let i=void 0===p?"":`(${p})`;return this.url.searchParams.append(e,`${t}fts${i}.${a}`),this}match(e){return Object.entries(e).forEach(([e,a])=>{this.url.searchParams.append(e,`eq.${a}`)}),this}not(e,a,p){return this.url.searchParams.append(e,`not.${a}.${p}`),this}or(e,{foreignTable:a,referencedTable:p=a}={}){let d=p?`${p}.or`:"or";return this.url.searchParams.append(d,`(${e})`),this}filter(e,a,p){return this.url.searchParams.append(e,`${a}.${p}`),this}}a.default=i},57113:(e,a,p)=>{var d=p(11997),t=p(77653),i={TRANSITIONAL:0,NONTRANSITIONAL:1};function s(e){return e.split("\0").map(function(e){return e.normalize("NFC")}).join("\0")}function r(e){for(var a=0,p=t.length-1;a<=p;){var d=Math.floor((a+p)/2),i=t[d];if(i[0][0]<=e&&i[0][1]>=e)return i;i[0][0]>e?p=d-1:a=d+1}return null}var l=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g;function o(e){return e.replace(l,"_").length}var n=/[\u0300-\u036F\u0483-\u0489\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u08E4-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B62\u0B63\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0C00-\u0C03\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0D01-\u0D03\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D82\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0EB1\u0EB4-\u0EB9\u0EBB\u0EBC\u0EC8-\u0ECD\u0F18\u0F19\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F\u109A-\u109D\u135D-\u135F\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u180B-\u180D\u18A9\u1920-\u192B\u1930-\u193B\u19B0-\u19C0\u19C8\u19C9\u1A17-\u1A1B\u1A55-\u1A5E\u1A60-\u1A7C\u1A7F\u1AB0-\u1ABE\u1B00-\u1B04\u1B34-\u1B44\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BE6-\u1BF3\u1C24-\u1C37\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF2-\u1CF4\u1CF8\u1CF9\u1DC0-\u1DF5\u1DFC-\u1DFF\u20D0-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\uA66F-\uA672\uA674-\uA67D\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA880\uA881\uA8B4-\uA8C4\uA8E0-\uA8F1\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9E5\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uFB1E\uFE00-\uFE0F\uFE20-\uFE2D]|\uD800[\uDDFD\uDEE0\uDF76-\uDF7A]|\uD802[\uDE01-\uDE03\uDE05\uDE06\uDE0C-\uDE0F\uDE38-\uDE3A\uDE3F\uDEE5\uDEE6]|\uD804[\uDC00-\uDC02\uDC38-\uDC46\uDC7F-\uDC82\uDCB0-\uDCBA\uDD00-\uDD02\uDD27-\uDD34\uDD73\uDD80-\uDD82\uDDB3-\uDDC0\uDE2C-\uDE37\uDEDF-\uDEEA\uDF01-\uDF03\uDF3C\uDF3E-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF57\uDF62\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDCB0-\uDCC3\uDDAF-\uDDB5\uDDB8-\uDDC0\uDE30-\uDE40\uDEAB-\uDEB7]|\uD81A[\uDEF0-\uDEF4\uDF30-\uDF36]|\uD81B[\uDF51-\uDF7E\uDF8F-\uDF92]|\uD82F[\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD83A[\uDCD0-\uDCD6]|\uDB40[\uDD00-\uDDEF]/;function m(e,a,p){var t=function(e,a,p){for(var d=!1,t="",s=o(e),l=0;l<s;++l){var n=e.codePointAt(l),m=r(n);switch(m[1]){case"disallowed":d=!0,t+=String.fromCodePoint(n);break;case"ignored":break;case"mapped":t+=String.fromCodePoint.apply(String,m[2]);break;case"deviation":p===i.TRANSITIONAL?t+=String.fromCodePoint.apply(String,m[2]):t+=String.fromCodePoint(n);break;case"valid":t+=String.fromCodePoint(n);break;case"disallowed_STD3_mapped":a?(d=!0,t+=String.fromCodePoint(n)):t+=String.fromCodePoint.apply(String,m[2]);break;case"disallowed_STD3_valid":a&&(d=!0),t+=String.fromCodePoint(n)}}return{string:t,error:d}}(e,a,p);t.string=s(t.string);for(var l=t.string.split("."),h=0;h<l.length;++h)try{var u=function(e,a){"xn--"===e.substr(0,4)&&(e=d.toUnicode(e),i.NONTRANSITIONAL);var p=!1;(s(e)!==e||"-"===e[3]&&"-"===e[4]||"-"===e[0]||"-"===e[e.length-1]||-1!==e.indexOf(".")||0===e.search(n))&&(p=!0);for(var t=o(e),l=0;l<t;++l){var h=r(e.codePointAt(l));if(m===i.TRANSITIONAL&&"valid"!==h[1]||m===i.NONTRANSITIONAL&&"valid"!==h[1]&&"deviation"!==h[1]){p=!0;break}}return{label:e,error:p}}(l[h]);l[h]=u.label,t.error=t.error||u.error}catch(e){t.error=!0}return{string:l.join("."),error:t.error}}e.exports.toASCII=function(e,a,p,t){var i=m(e,a,p),s=i.string.split(".");if(s=s.map(function(e){try{return d.toASCII(e)}catch(a){return i.error=!0,e}}),t){var r=s.slice(0,s.length-1).join(".").length;(r.length>253||0===r.length)&&(i.error=!0);for(var l=0;l<s.length;++l)if(s.length>63||0===s.length){i.error=!0;break}}return i.error?null:s.join(".")},e.exports.toUnicode=function(e,a){var p=m(e,a,i.NONTRANSITIONAL);return{domain:p.string,error:p.error}},e.exports.PROCESSING_OPTIONS=i},58238:(e,a,p)=>{Object.defineProperty(a,"__esModule",{value:!0}),a.DEFAULT_HEADERS=void 0;let d=p(63915);a.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${d.version}`}},63915:(e,a)=>{Object.defineProperty(a,"__esModule",{value:!0}),a.version=void 0,a.version="0.0.0-automated"},67438:(e,a,p)=>{let d=p(94735),t=p(81630),{Duplex:i}=p(27910),{createHash:s}=p(55511),r=p(47762),l=p(7367),o=p(67597),n=p(97628),{GUID:m,kWebSocket:h}=p(31394),u=/^[+/0-9A-Za-z]{22}==$/;class c extends d{constructor(e,a){if(super(),null==(e={allowSynchronousEvents:!0,autoPong:!0,maxPayload:0x6400000,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:n,...e}).port&&!e.server&&!e.noServer||null!=e.port&&(e.server||e.noServer)||e.server&&e.noServer)throw TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(null!=e.port?(this._server=t.createServer((e,a)=>{let p=t.STATUS_CODES[426];a.writeHead(426,{"Content-Length":p.length,"Content-Type":"text/plain"}),a.end(p)}),this._server.listen(e.port,e.host,e.backlog,a)):e.server&&(this._server=e.server),this._server){let e=this.emit.bind(this,"connection");this._removeListeners=function(e,a){for(let p of Object.keys(a))e.on(p,a[p]);return function(){for(let p of Object.keys(a))e.removeListener(p,a[p])}}(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(a,p,d)=>{this.handleUpgrade(a,p,d,e)}})}!0===e.perMessageDeflate&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=0}address(){if(this.options.noServer)throw Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(2===this._state){e&&this.once("close",()=>{e(Error("The server is not running"))}),process.nextTick(v,this);return}if(e&&this.once("close",e),1!==this._state)if(this._state=1,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients&&this.clients.size?this._shouldEmitClose=!0:process.nextTick(v,this);else{let e=this._server;this._removeListeners(),this._removeListeners=this._server=null,e.close(()=>{v(this)})}}shouldHandle(e){if(this.options.path){let a=e.url.indexOf("?");if((-1!==a?e.url.slice(0,a):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,a,p,d){a.on("error",f);let t=e.headers["sec-websocket-key"],i=e.headers.upgrade,s=+e.headers["sec-websocket-version"];if("GET"!==e.method)return void _(this,e,a,405,"Invalid HTTP method");if(void 0===i||"websocket"!==i.toLowerCase())return void _(this,e,a,400,"Invalid Upgrade header");if(void 0===t||!u.test(t))return void _(this,e,a,400,"Missing or invalid Sec-WebSocket-Key header");if(13!==s&&8!==s)return void _(this,e,a,400,"Missing or invalid Sec-WebSocket-Version header",{"Sec-WebSocket-Version":"13, 8"});if(!this.shouldHandle(e))return void w(a,400);let n=e.headers["sec-websocket-protocol"],m=new Set;if(void 0!==n)try{m=o.parse(n)}catch(p){_(this,e,a,400,"Invalid Sec-WebSocket-Protocol header");return}let h=e.headers["sec-websocket-extensions"],c={};if(this.options.perMessageDeflate&&void 0!==h){let p=new l(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let e=r.parse(h);e[l.extensionName]&&(p.accept(e[l.extensionName]),c[l.extensionName]=p)}catch(p){_(this,e,a,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let i={origin:e.headers[`${8===s?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(2===this.options.verifyClient.length)return void this.options.verifyClient(i,(i,s,r,l)=>{if(!i)return w(a,s||401,r,l);this.completeUpgrade(c,t,m,e,a,p,d)});if(!this.options.verifyClient(i))return w(a,401)}this.completeUpgrade(c,t,m,e,a,p,d)}completeUpgrade(e,a,p,d,t,i,o){if(!t.readable||!t.writable)return t.destroy();if(t[h])throw Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>0)return w(t,503);let n=s("sha1").update(a+m).digest("base64"),u=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${n}`],c=new this.options.WebSocket(null,void 0,this.options);if(p.size){let e=this.options.handleProtocols?this.options.handleProtocols(p,d):p.values().next().value;e&&(u.push(`Sec-WebSocket-Protocol: ${e}`),c._protocol=e)}if(e[l.extensionName]){let a=e[l.extensionName].params,p=r.format({[l.extensionName]:[a]});u.push(`Sec-WebSocket-Extensions: ${p}`),c._extensions=e}this.emit("headers",u,d),t.write(u.concat("\r\n").join("\r\n")),t.removeListener("error",f),c.setSocket(t,i,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(c),c.on("close",()=>{this.clients.delete(c),this._shouldEmitClose&&!this.clients.size&&process.nextTick(v,this)})),o(c,d)}}function v(e){e._state=2,e.emit("close")}function f(){this.destroy()}function w(e,a,p,d){p=p||t.STATUS_CODES[a],d={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(p),...d},e.once("finish",e.destroy),e.end(`HTTP/1.1 ${a} ${t.STATUS_CODES[a]}\r
`+Object.keys(d).map(e=>`${e}: ${d[e]}`).join("\r\n")+"\r\n\r\n"+p)}function _(e,a,p,d,t,i){if(e.listenerCount("wsClientError")){let d=Error(t);Error.captureStackTrace(d,_),e.emit("wsClientError",d,p,a)}else w(p,d,t,i)}e.exports=c},67597:(e,a,p)=>{let{tokenChars:d}=p(10248);e.exports={parse:function(e){let a=new Set,p=-1,t=-1,i=0;for(;i<e.length;i++){let s=e.charCodeAt(i);if(-1===t&&1===d[s])-1===p&&(p=i);else if(0!==i&&(32===s||9===s))-1===t&&-1!==p&&(t=i);else if(44===s){if(-1===p)throw SyntaxError(`Unexpected character at index ${i}`);-1===t&&(t=i);let d=e.slice(p,t);if(a.has(d))throw SyntaxError(`The "${d}" subprotocol is duplicated`);a.add(d),p=t=-1}else throw SyntaxError(`Unexpected character at index ${i}`)}if(-1===p||-1!==t)throw SyntaxError("Unexpected end of input");let s=e.slice(p,i);if(a.has(s))throw SyntaxError(`The "${s}" subprotocol is duplicated`);return a.add(s),a}}},73425:(e,a,p)=>{a.URL=p(79432).interface,a.serializeURL=p(32980).serializeURL,a.serializeURLOrigin=p(32980).serializeURLOrigin,a.basicURLParse=p(32980).basicURLParse,a.setTheUsername=p(32980).setTheUsername,a.setThePassword=p(32980).setThePassword,a.serializeHost=p(32980).serializeHost,a.serializeInteger=p(32980).serializeInteger,a.parseURL=p(32980).parseURL},77653:e=>{e.exports=JSON.parse('[[[0,44],"disallowed_STD3_valid"],[[45,46],"valid"],[[47,47],"disallowed_STD3_valid"],[[48,57],"valid"],[[58,64],"disallowed_STD3_valid"],[[65,65],"mapped",[97]],[[66,66],"mapped",[98]],[[67,67],"mapped",[99]],[[68,68],"mapped",[100]],[[69,69],"mapped",[101]],[[70,70],"mapped",[102]],[[71,71],"mapped",[103]],[[72,72],"mapped",[104]],[[73,73],"mapped",[105]],[[74,74],"mapped",[106]],[[75,75],"mapped",[107]],[[76,76],"mapped",[108]],[[77,77],"mapped",[109]],[[78,78],"mapped",[110]],[[79,79],"mapped",[111]],[[80,80],"mapped",[112]],[[81,81],"mapped",[113]],[[82,82],"mapped",[114]],[[83,83],"mapped",[115]],[[84,84],"mapped",[116]],[[85,85],"mapped",[117]],[[86,86],"mapped",[118]],[[87,87],"mapped",[119]],[[88,88],"mapped",[120]],[[89,89],"mapped",[121]],[[90,90],"mapped",[122]],[[91,96],"disallowed_STD3_valid"],[[97,122],"valid"],[[123,127],"disallowed_STD3_valid"],[[128,159],"disallowed"],[[160,160],"disallowed_STD3_mapped",[32]],[[161,167],"valid",[],"NV8"],[[168,168],"disallowed_STD3_mapped",[32,776]],[[169,169],"valid",[],"NV8"],[[170,170],"mapped",[97]],[[171,172],"valid",[],"NV8"],[[173,173],"ignored"],[[174,174],"valid",[],"NV8"],[[175,175],"disallowed_STD3_mapped",[32,772]],[[176,177],"valid",[],"NV8"],[[178,178],"mapped",[50]],[[179,179],"mapped",[51]],[[180,180],"disallowed_STD3_mapped",[32,769]],[[181,181],"mapped",[956]],[[182,182],"valid",[],"NV8"],[[183,183],"valid"],[[184,184],"disallowed_STD3_mapped",[32,807]],[[185,185],"mapped",[49]],[[186,186],"mapped",[111]],[[187,187],"valid",[],"NV8"],[[188,188],"mapped",[49,8260,52]],[[189,189],"mapped",[49,8260,50]],[[190,190],"mapped",[51,8260,52]],[[191,191],"valid",[],"NV8"],[[192,192],"mapped",[224]],[[193,193],"mapped",[225]],[[194,194],"mapped",[226]],[[195,195],"mapped",[227]],[[196,196],"mapped",[228]],[[197,197],"mapped",[229]],[[198,198],"mapped",[230]],[[199,199],"mapped",[231]],[[200,200],"mapped",[232]],[[201,201],"mapped",[233]],[[202,202],"mapped",[234]],[[203,203],"mapped",[235]],[[204,204],"mapped",[236]],[[205,205],"mapped",[237]],[[206,206],"mapped",[238]],[[207,207],"mapped",[239]],[[208,208],"mapped",[240]],[[209,209],"mapped",[241]],[[210,210],"mapped",[242]],[[211,211],"mapped",[243]],[[212,212],"mapped",[244]],[[213,213],"mapped",[245]],[[214,214],"mapped",[246]],[[215,215],"valid",[],"NV8"],[[216,216],"mapped",[248]],[[217,217],"mapped",[249]],[[218,218],"mapped",[250]],[[219,219],"mapped",[251]],[[220,220],"mapped",[252]],[[221,221],"mapped",[253]],[[222,222],"mapped",[254]],[[223,223],"deviation",[115,115]],[[224,246],"valid"],[[247,247],"valid",[],"NV8"],[[248,255],"valid"],[[256,256],"mapped",[257]],[[257,257],"valid"],[[258,258],"mapped",[259]],[[259,259],"valid"],[[260,260],"mapped",[261]],[[261,261],"valid"],[[262,262],"mapped",[263]],[[263,263],"valid"],[[264,264],"mapped",[265]],[[265,265],"valid"],[[266,266],"mapped",[267]],[[267,267],"valid"],[[268,268],"mapped",[269]],[[269,269],"valid"],[[270,270],"mapped",[271]],[[271,271],"valid"],[[272,272],"mapped",[273]],[[273,273],"valid"],[[274,274],"mapped",[275]],[[275,275],"valid"],[[276,276],"mapped",[277]],[[277,277],"valid"],[[278,278],"mapped",[279]],[[279,279],"valid"],[[280,280],"mapped",[281]],[[281,281],"valid"],[[282,282],"mapped",[283]],[[283,283],"valid"],[[284,284],"mapped",[285]],[[285,285],"valid"],[[286,286],"mapped",[287]],[[287,287],"valid"],[[288,288],"mapped",[289]],[[289,289],"valid"],[[290,290],"mapped",[291]],[[291,291],"valid"],[[292,292],"mapped",[293]],[[293,293],"valid"],[[294,294],"mapped",[295]],[[295,295],"valid"],[[296,296],"mapped",[297]],[[297,297],"valid"],[[298,298],"mapped",[299]],[[299,299],"valid"],[[300,300],"mapped",[301]],[[301,301],"valid"],[[302,302],"mapped",[303]],[[303,303],"valid"],[[304,304],"mapped",[105,775]],[[305,305],"valid"],[[306,307],"mapped",[105,106]],[[308,308],"mapped",[309]],[[309,309],"valid"],[[310,310],"mapped",[311]],[[311,312],"valid"],[[313,313],"mapped",[314]],[[314,314],"valid"],[[315,315],"mapped",[316]],[[316,316],"valid"],[[317,317],"mapped",[318]],[[318,318],"valid"],[[319,320],"mapped",[108,183]],[[321,321],"mapped",[322]],[[322,322],"valid"],[[323,323],"mapped",[324]],[[324,324],"valid"],[[325,325],"mapped",[326]],[[326,326],"valid"],[[327,327],"mapped",[328]],[[328,328],"valid"],[[329,329],"mapped",[700,110]],[[330,330],"mapped",[331]],[[331,331],"valid"],[[332,332],"mapped",[333]],[[333,333],"valid"],[[334,334],"mapped",[335]],[[335,335],"valid"],[[336,336],"mapped",[337]],[[337,337],"valid"],[[338,338],"mapped",[339]],[[339,339],"valid"],[[340,340],"mapped",[341]],[[341,341],"valid"],[[342,342],"mapped",[343]],[[343,343],"valid"],[[344,344],"mapped",[345]],[[345,345],"valid"],[[346,346],"mapped",[347]],[[347,347],"valid"],[[348,348],"mapped",[349]],[[349,349],"valid"],[[350,350],"mapped",[351]],[[351,351],"valid"],[[352,352],"mapped",[353]],[[353,353],"valid"],[[354,354],"mapped",[355]],[[355,355],"valid"],[[356,356],"mapped",[357]],[[357,357],"valid"],[[358,358],"mapped",[359]],[[359,359],"valid"],[[360,360],"mapped",[361]],[[361,361],"valid"],[[362,362],"mapped",[363]],[[363,363],"valid"],[[364,364],"mapped",[365]],[[365,365],"valid"],[[366,366],"mapped",[367]],[[367,367],"valid"],[[368,368],"mapped",[369]],[[369,369],"valid"],[[370,370],"mapped",[371]],[[371,371],"valid"],[[372,372],"mapped",[373]],[[373,373],"valid"],[[374,374],"mapped",[375]],[[375,375],"valid"],[[376,376],"mapped",[255]],[[377,377],"mapped",[378]],[[378,378],"valid"],[[379,379],"mapped",[380]],[[380,380],"valid"],[[381,381],"mapped",[382]],[[382,382],"valid"],[[383,383],"mapped",[115]],[[384,384],"valid"],[[385,385],"mapped",[595]],[[386,386],"mapped",[387]],[[387,387],"valid"],[[388,388],"mapped",[389]],[[389,389],"valid"],[[390,390],"mapped",[596]],[[391,391],"mapped",[392]],[[392,392],"valid"],[[393,393],"mapped",[598]],[[394,394],"mapped",[599]],[[395,395],"mapped",[396]],[[396,397],"valid"],[[398,398],"mapped",[477]],[[399,399],"mapped",[601]],[[400,400],"mapped",[603]],[[401,401],"mapped",[402]],[[402,402],"valid"],[[403,403],"mapped",[608]],[[404,404],"mapped",[611]],[[405,405],"valid"],[[406,406],"mapped",[617]],[[407,407],"mapped",[616]],[[408,408],"mapped",[409]],[[409,411],"valid"],[[412,412],"mapped",[623]],[[413,413],"mapped",[626]],[[414,414],"valid"],[[415,415],"mapped",[629]],[[416,416],"mapped",[417]],[[417,417],"valid"],[[418,418],"mapped",[419]],[[419,419],"valid"],[[420,420],"mapped",[421]],[[421,421],"valid"],[[422,422],"mapped",[640]],[[423,423],"mapped",[424]],[[424,424],"valid"],[[425,425],"mapped",[643]],[[426,427],"valid"],[[428,428],"mapped",[429]],[[429,429],"valid"],[[430,430],"mapped",[648]],[[431,431],"mapped",[432]],[[432,432],"valid"],[[433,433],"mapped",[650]],[[434,434],"mapped",[651]],[[435,435],"mapped",[436]],[[436,436],"valid"],[[437,437],"mapped",[438]],[[438,438],"valid"],[[439,439],"mapped",[658]],[[440,440],"mapped",[441]],[[441,443],"valid"],[[444,444],"mapped",[445]],[[445,451],"valid"],[[452,454],"mapped",[100,382]],[[455,457],"mapped",[108,106]],[[458,460],"mapped",[110,106]],[[461,461],"mapped",[462]],[[462,462],"valid"],[[463,463],"mapped",[464]],[[464,464],"valid"],[[465,465],"mapped",[466]],[[466,466],"valid"],[[467,467],"mapped",[468]],[[468,468],"valid"],[[469,469],"mapped",[470]],[[470,470],"valid"],[[471,471],"mapped",[472]],[[472,472],"valid"],[[473,473],"mapped",[474]],[[474,474],"valid"],[[475,475],"mapped",[476]],[[476,477],"valid"],[[478,478],"mapped",[479]],[[479,479],"valid"],[[480,480],"mapped",[481]],[[481,481],"valid"],[[482,482],"mapped",[483]],[[483,483],"valid"],[[484,484],"mapped",[485]],[[485,485],"valid"],[[486,486],"mapped",[487]],[[487,487],"valid"],[[488,488],"mapped",[489]],[[489,489],"valid"],[[490,490],"mapped",[491]],[[491,491],"valid"],[[492,492],"mapped",[493]],[[493,493],"valid"],[[494,494],"mapped",[495]],[[495,496],"valid"],[[497,499],"mapped",[100,122]],[[500,500],"mapped",[501]],[[501,501],"valid"],[[502,502],"mapped",[405]],[[503,503],"mapped",[447]],[[504,504],"mapped",[505]],[[505,505],"valid"],[[506,506],"mapped",[507]],[[507,507],"valid"],[[508,508],"mapped",[509]],[[509,509],"valid"],[[510,510],"mapped",[511]],[[511,511],"valid"],[[512,512],"mapped",[513]],[[513,513],"valid"],[[514,514],"mapped",[515]],[[515,515],"valid"],[[516,516],"mapped",[517]],[[517,517],"valid"],[[518,518],"mapped",[519]],[[519,519],"valid"],[[520,520],"mapped",[521]],[[521,521],"valid"],[[522,522],"mapped",[523]],[[523,523],"valid"],[[524,524],"mapped",[525]],[[525,525],"valid"],[[526,526],"mapped",[527]],[[527,527],"valid"],[[528,528],"mapped",[529]],[[529,529],"valid"],[[530,530],"mapped",[531]],[[531,531],"valid"],[[532,532],"mapped",[533]],[[533,533],"valid"],[[534,534],"mapped",[535]],[[535,535],"valid"],[[536,536],"mapped",[537]],[[537,537],"valid"],[[538,538],"mapped",[539]],[[539,539],"valid"],[[540,540],"mapped",[541]],[[541,541],"valid"],[[542,542],"mapped",[543]],[[543,543],"valid"],[[544,544],"mapped",[414]],[[545,545],"valid"],[[546,546],"mapped",[547]],[[547,547],"valid"],[[548,548],"mapped",[549]],[[549,549],"valid"],[[550,550],"mapped",[551]],[[551,551],"valid"],[[552,552],"mapped",[553]],[[553,553],"valid"],[[554,554],"mapped",[555]],[[555,555],"valid"],[[556,556],"mapped",[557]],[[557,557],"valid"],[[558,558],"mapped",[559]],[[559,559],"valid"],[[560,560],"mapped",[561]],[[561,561],"valid"],[[562,562],"mapped",[563]],[[563,563],"valid"],[[564,566],"valid"],[[567,569],"valid"],[[570,570],"mapped",[11365]],[[571,571],"mapped",[572]],[[572,572],"valid"],[[573,573],"mapped",[410]],[[574,574],"mapped",[11366]],[[575,576],"valid"],[[577,577],"mapped",[578]],[[578,578],"valid"],[[579,579],"mapped",[384]],[[580,580],"mapped",[649]],[[581,581],"mapped",[652]],[[582,582],"mapped",[583]],[[583,583],"valid"],[[584,584],"mapped",[585]],[[585,585],"valid"],[[586,586],"mapped",[587]],[[587,587],"valid"],[[588,588],"mapped",[589]],[[589,589],"valid"],[[590,590],"mapped",[591]],[[591,591],"valid"],[[592,680],"valid"],[[681,685],"valid"],[[686,687],"valid"],[[688,688],"mapped",[104]],[[689,689],"mapped",[614]],[[690,690],"mapped",[106]],[[691,691],"mapped",[114]],[[692,692],"mapped",[633]],[[693,693],"mapped",[635]],[[694,694],"mapped",[641]],[[695,695],"mapped",[119]],[[696,696],"mapped",[121]],[[697,705],"valid"],[[706,709],"valid",[],"NV8"],[[710,721],"valid"],[[722,727],"valid",[],"NV8"],[[728,728],"disallowed_STD3_mapped",[32,774]],[[729,729],"disallowed_STD3_mapped",[32,775]],[[730,730],"disallowed_STD3_mapped",[32,778]],[[731,731],"disallowed_STD3_mapped",[32,808]],[[732,732],"disallowed_STD3_mapped",[32,771]],[[733,733],"disallowed_STD3_mapped",[32,779]],[[734,734],"valid",[],"NV8"],[[735,735],"valid",[],"NV8"],[[736,736],"mapped",[611]],[[737,737],"mapped",[108]],[[738,738],"mapped",[115]],[[739,739],"mapped",[120]],[[740,740],"mapped",[661]],[[741,745],"valid",[],"NV8"],[[746,747],"valid",[],"NV8"],[[748,748],"valid"],[[749,749],"valid",[],"NV8"],[[750,750],"valid"],[[751,767],"valid",[],"NV8"],[[768,831],"valid"],[[832,832],"mapped",[768]],[[833,833],"mapped",[769]],[[834,834],"valid"],[[835,835],"mapped",[787]],[[836,836],"mapped",[776,769]],[[837,837],"mapped",[953]],[[838,846],"valid"],[[847,847],"ignored"],[[848,855],"valid"],[[856,860],"valid"],[[861,863],"valid"],[[864,865],"valid"],[[866,866],"valid"],[[867,879],"valid"],[[880,880],"mapped",[881]],[[881,881],"valid"],[[882,882],"mapped",[883]],[[883,883],"valid"],[[884,884],"mapped",[697]],[[885,885],"valid"],[[886,886],"mapped",[887]],[[887,887],"valid"],[[888,889],"disallowed"],[[890,890],"disallowed_STD3_mapped",[32,953]],[[891,893],"valid"],[[894,894],"disallowed_STD3_mapped",[59]],[[895,895],"mapped",[1011]],[[896,899],"disallowed"],[[900,900],"disallowed_STD3_mapped",[32,769]],[[901,901],"disallowed_STD3_mapped",[32,776,769]],[[902,902],"mapped",[940]],[[903,903],"mapped",[183]],[[904,904],"mapped",[941]],[[905,905],"mapped",[942]],[[906,906],"mapped",[943]],[[907,907],"disallowed"],[[908,908],"mapped",[972]],[[909,909],"disallowed"],[[910,910],"mapped",[973]],[[911,911],"mapped",[974]],[[912,912],"valid"],[[913,913],"mapped",[945]],[[914,914],"mapped",[946]],[[915,915],"mapped",[947]],[[916,916],"mapped",[948]],[[917,917],"mapped",[949]],[[918,918],"mapped",[950]],[[919,919],"mapped",[951]],[[920,920],"mapped",[952]],[[921,921],"mapped",[953]],[[922,922],"mapped",[954]],[[923,923],"mapped",[955]],[[924,924],"mapped",[956]],[[925,925],"mapped",[957]],[[926,926],"mapped",[958]],[[927,927],"mapped",[959]],[[928,928],"mapped",[960]],[[929,929],"mapped",[961]],[[930,930],"disallowed"],[[931,931],"mapped",[963]],[[932,932],"mapped",[964]],[[933,933],"mapped",[965]],[[934,934],"mapped",[966]],[[935,935],"mapped",[967]],[[936,936],"mapped",[968]],[[937,937],"mapped",[969]],[[938,938],"mapped",[970]],[[939,939],"mapped",[971]],[[940,961],"valid"],[[962,962],"deviation",[963]],[[963,974],"valid"],[[975,975],"mapped",[983]],[[976,976],"mapped",[946]],[[977,977],"mapped",[952]],[[978,978],"mapped",[965]],[[979,979],"mapped",[973]],[[980,980],"mapped",[971]],[[981,981],"mapped",[966]],[[982,982],"mapped",[960]],[[983,983],"valid"],[[984,984],"mapped",[985]],[[985,985],"valid"],[[986,986],"mapped",[987]],[[987,987],"valid"],[[988,988],"mapped",[989]],[[989,989],"valid"],[[990,990],"mapped",[991]],[[991,991],"valid"],[[992,992],"mapped",[993]],[[993,993],"valid"],[[994,994],"mapped",[995]],[[995,995],"valid"],[[996,996],"mapped",[997]],[[997,997],"valid"],[[998,998],"mapped",[999]],[[999,999],"valid"],[[1000,1000],"mapped",[1001]],[[1001,1001],"valid"],[[1002,1002],"mapped",[1003]],[[1003,1003],"valid"],[[1004,1004],"mapped",[1005]],[[1005,1005],"valid"],[[1006,1006],"mapped",[1007]],[[1007,1007],"valid"],[[1008,1008],"mapped",[954]],[[1009,1009],"mapped",[961]],[[1010,1010],"mapped",[963]],[[1011,1011],"valid"],[[1012,1012],"mapped",[952]],[[1013,1013],"mapped",[949]],[[1014,1014],"valid",[],"NV8"],[[1015,1015],"mapped",[1016]],[[1016,1016],"valid"],[[1017,1017],"mapped",[963]],[[1018,1018],"mapped",[1019]],[[1019,1019],"valid"],[[1020,1020],"valid"],[[1021,1021],"mapped",[891]],[[1022,1022],"mapped",[892]],[[1023,1023],"mapped",[893]],[[1024,1024],"mapped",[1104]],[[1025,1025],"mapped",[1105]],[[1026,1026],"mapped",[1106]],[[1027,1027],"mapped",[1107]],[[1028,1028],"mapped",[1108]],[[1029,1029],"mapped",[1109]],[[1030,1030],"mapped",[1110]],[[1031,1031],"mapped",[1111]],[[1032,1032],"mapped",[1112]],[[1033,1033],"mapped",[1113]],[[1034,1034],"mapped",[1114]],[[1035,1035],"mapped",[1115]],[[1036,1036],"mapped",[1116]],[[1037,1037],"mapped",[1117]],[[1038,1038],"mapped",[1118]],[[1039,1039],"mapped",[1119]],[[1040,1040],"mapped",[1072]],[[1041,1041],"mapped",[1073]],[[1042,1042],"mapped",[1074]],[[1043,1043],"mapped",[1075]],[[1044,1044],"mapped",[1076]],[[1045,1045],"mapped",[1077]],[[1046,1046],"mapped",[1078]],[[1047,1047],"mapped",[1079]],[[1048,1048],"mapped",[1080]],[[1049,1049],"mapped",[1081]],[[1050,1050],"mapped",[1082]],[[1051,1051],"mapped",[1083]],[[1052,1052],"mapped",[1084]],[[1053,1053],"mapped",[1085]],[[1054,1054],"mapped",[1086]],[[1055,1055],"mapped",[1087]],[[1056,1056],"mapped",[1088]],[[1057,1057],"mapped",[1089]],[[1058,1058],"mapped",[1090]],[[1059,1059],"mapped",[1091]],[[1060,1060],"mapped",[1092]],[[1061,1061],"mapped",[1093]],[[1062,1062],"mapped",[1094]],[[1063,1063],"mapped",[1095]],[[1064,1064],"mapped",[1096]],[[1065,1065],"mapped",[1097]],[[1066,1066],"mapped",[1098]],[[1067,1067],"mapped",[1099]],[[1068,1068],"mapped",[1100]],[[1069,1069],"mapped",[1101]],[[1070,1070],"mapped",[1102]],[[1071,1071],"mapped",[1103]],[[1072,1103],"valid"],[[1104,1104],"valid"],[[1105,1116],"valid"],[[1117,1117],"valid"],[[1118,1119],"valid"],[[1120,1120],"mapped",[1121]],[[1121,1121],"valid"],[[1122,1122],"mapped",[1123]],[[1123,1123],"valid"],[[1124,1124],"mapped",[1125]],[[1125,1125],"valid"],[[1126,1126],"mapped",[1127]],[[1127,1127],"valid"],[[1128,1128],"mapped",[1129]],[[1129,1129],"valid"],[[1130,1130],"mapped",[1131]],[[1131,1131],"valid"],[[1132,1132],"mapped",[1133]],[[1133,1133],"valid"],[[1134,1134],"mapped",[1135]],[[1135,1135],"valid"],[[1136,1136],"mapped",[1137]],[[1137,1137],"valid"],[[1138,1138],"mapped",[1139]],[[1139,1139],"valid"],[[1140,1140],"mapped",[1141]],[[1141,1141],"valid"],[[1142,1142],"mapped",[1143]],[[1143,1143],"valid"],[[1144,1144],"mapped",[1145]],[[1145,1145],"valid"],[[1146,1146],"mapped",[1147]],[[1147,1147],"valid"],[[1148,1148],"mapped",[1149]],[[1149,1149],"valid"],[[1150,1150],"mapped",[1151]],[[1151,1151],"valid"],[[1152,1152],"mapped",[1153]],[[1153,1153],"valid"],[[1154,1154],"valid",[],"NV8"],[[1155,1158],"valid"],[[1159,1159],"valid"],[[1160,1161],"valid",[],"NV8"],[[1162,1162],"mapped",[1163]],[[1163,1163],"valid"],[[1164,1164],"mapped",[1165]],[[1165,1165],"valid"],[[1166,1166],"mapped",[1167]],[[1167,1167],"valid"],[[1168,1168],"mapped",[1169]],[[1169,1169],"valid"],[[1170,1170],"mapped",[1171]],[[1171,1171],"valid"],[[1172,1172],"mapped",[1173]],[[1173,1173],"valid"],[[1174,1174],"mapped",[1175]],[[1175,1175],"valid"],[[1176,1176],"mapped",[1177]],[[1177,1177],"valid"],[[1178,1178],"mapped",[1179]],[[1179,1179],"valid"],[[1180,1180],"mapped",[1181]],[[1181,1181],"valid"],[[1182,1182],"mapped",[1183]],[[1183,1183],"valid"],[[1184,1184],"mapped",[1185]],[[1185,1185],"valid"],[[1186,1186],"mapped",[1187]],[[1187,1187],"valid"],[[1188,1188],"mapped",[1189]],[[1189,1189],"valid"],[[1190,1190],"mapped",[1191]],[[1191,1191],"valid"],[[1192,1192],"mapped",[1193]],[[1193,1193],"valid"],[[1194,1194],"mapped",[1195]],[[1195,1195],"valid"],[[1196,1196],"mapped",[1197]],[[1197,1197],"valid"],[[1198,1198],"mapped",[1199]],[[1199,1199],"valid"],[[1200,1200],"mapped",[1201]],[[1201,1201],"valid"],[[1202,1202],"mapped",[1203]],[[1203,1203],"valid"],[[1204,1204],"mapped",[1205]],[[1205,1205],"valid"],[[1206,1206],"mapped",[1207]],[[1207,1207],"valid"],[[1208,1208],"mapped",[1209]],[[1209,1209],"valid"],[[1210,1210],"mapped",[1211]],[[1211,1211],"valid"],[[1212,1212],"mapped",[1213]],[[1213,1213],"valid"],[[1214,1214],"mapped",[1215]],[[1215,1215],"valid"],[[1216,1216],"disallowed"],[[1217,1217],"mapped",[1218]],[[1218,1218],"valid"],[[1219,1219],"mapped",[1220]],[[1220,1220],"valid"],[[1221,1221],"mapped",[1222]],[[1222,1222],"valid"],[[1223,1223],"mapped",[1224]],[[1224,1224],"valid"],[[1225,1225],"mapped",[1226]],[[1226,1226],"valid"],[[1227,1227],"mapped",[1228]],[[1228,1228],"valid"],[[1229,1229],"mapped",[1230]],[[1230,1230],"valid"],[[1231,1231],"valid"],[[1232,1232],"mapped",[1233]],[[1233,1233],"valid"],[[1234,1234],"mapped",[1235]],[[1235,1235],"valid"],[[1236,1236],"mapped",[1237]],[[1237,1237],"valid"],[[1238,1238],"mapped",[1239]],[[1239,1239],"valid"],[[1240,1240],"mapped",[1241]],[[1241,1241],"valid"],[[1242,1242],"mapped",[1243]],[[1243,1243],"valid"],[[1244,1244],"mapped",[1245]],[[1245,1245],"valid"],[[1246,1246],"mapped",[1247]],[[1247,1247],"valid"],[[1248,1248],"mapped",[1249]],[[1249,1249],"valid"],[[1250,1250],"mapped",[1251]],[[1251,1251],"valid"],[[1252,1252],"mapped",[1253]],[[1253,1253],"valid"],[[1254,1254],"mapped",[1255]],[[1255,1255],"valid"],[[1256,1256],"mapped",[1257]],[[1257,1257],"valid"],[[1258,1258],"mapped",[1259]],[[1259,1259],"valid"],[[1260,1260],"mapped",[1261]],[[1261,1261],"valid"],[[1262,1262],"mapped",[1263]],[[1263,1263],"valid"],[[1264,1264],"mapped",[1265]],[[1265,1265],"valid"],[[1266,1266],"mapped",[1267]],[[1267,1267],"valid"],[[1268,1268],"mapped",[1269]],[[1269,1269],"valid"],[[1270,1270],"mapped",[1271]],[[1271,1271],"valid"],[[1272,1272],"mapped",[1273]],[[1273,1273],"valid"],[[1274,1274],"mapped",[1275]],[[1275,1275],"valid"],[[1276,1276],"mapped",[1277]],[[1277,1277],"valid"],[[1278,1278],"mapped",[1279]],[[1279,1279],"valid"],[[1280,1280],"mapped",[1281]],[[1281,1281],"valid"],[[1282,1282],"mapped",[1283]],[[1283,1283],"valid"],[[1284,1284],"mapped",[1285]],[[1285,1285],"valid"],[[1286,1286],"mapped",[1287]],[[1287,1287],"valid"],[[1288,1288],"mapped",[1289]],[[1289,1289],"valid"],[[1290,1290],"mapped",[1291]],[[1291,1291],"valid"],[[1292,1292],"mapped",[1293]],[[1293,1293],"valid"],[[1294,1294],"mapped",[1295]],[[1295,1295],"valid"],[[1296,1296],"mapped",[1297]],[[1297,1297],"valid"],[[1298,1298],"mapped",[1299]],[[1299,1299],"valid"],[[1300,1300],"mapped",[1301]],[[1301,1301],"valid"],[[1302,1302],"mapped",[1303]],[[1303,1303],"valid"],[[1304,1304],"mapped",[1305]],[[1305,1305],"valid"],[[1306,1306],"mapped",[1307]],[[1307,1307],"valid"],[[1308,1308],"mapped",[1309]],[[1309,1309],"valid"],[[1310,1310],"mapped",[1311]],[[1311,1311],"valid"],[[1312,1312],"mapped",[1313]],[[1313,1313],"valid"],[[1314,1314],"mapped",[1315]],[[1315,1315],"valid"],[[1316,1316],"mapped",[1317]],[[1317,1317],"valid"],[[1318,1318],"mapped",[1319]],[[1319,1319],"valid"],[[1320,1320],"mapped",[1321]],[[1321,1321],"valid"],[[1322,1322],"mapped",[1323]],[[1323,1323],"valid"],[[1324,1324],"mapped",[1325]],[[1325,1325],"valid"],[[1326,1326],"mapped",[1327]],[[1327,1327],"valid"],[[1328,1328],"disallowed"],[[1329,1329],"mapped",[1377]],[[1330,1330],"mapped",[1378]],[[1331,1331],"mapped",[1379]],[[1332,1332],"mapped",[1380]],[[1333,1333],"mapped",[1381]],[[1334,1334],"mapped",[1382]],[[1335,1335],"mapped",[1383]],[[1336,1336],"mapped",[1384]],[[1337,1337],"mapped",[1385]],[[1338,1338],"mapped",[1386]],[[1339,1339],"mapped",[1387]],[[1340,1340],"mapped",[1388]],[[1341,1341],"mapped",[1389]],[[1342,1342],"mapped",[1390]],[[1343,1343],"mapped",[1391]],[[1344,1344],"mapped",[1392]],[[1345,1345],"mapped",[1393]],[[1346,1346],"mapped",[1394]],[[1347,1347],"mapped",[1395]],[[1348,1348],"mapped",[1396]],[[1349,1349],"mapped",[1397]],[[1350,1350],"mapped",[1398]],[[1351,1351],"mapped",[1399]],[[1352,1352],"mapped",[1400]],[[1353,1353],"mapped",[1401]],[[1354,1354],"mapped",[1402]],[[1355,1355],"mapped",[1403]],[[1356,1356],"mapped",[1404]],[[1357,1357],"mapped",[1405]],[[1358,1358],"mapped",[1406]],[[1359,1359],"mapped",[1407]],[[1360,1360],"mapped",[1408]],[[1361,1361],"mapped",[1409]],[[1362,1362],"mapped",[1410]],[[1363,1363],"mapped",[1411]],[[1364,1364],"mapped",[1412]],[[1365,1365],"mapped",[1413]],[[1366,1366],"mapped",[1414]],[[1367,1368],"disallowed"],[[1369,1369],"valid"],[[1370,1375],"valid",[],"NV8"],[[1376,1376],"disallowed"],[[1377,1414],"valid"],[[1415,1415],"mapped",[1381,1410]],[[1416,1416],"disallowed"],[[1417,1417],"valid",[],"NV8"],[[1418,1418],"valid",[],"NV8"],[[1419,1420],"disallowed"],[[1421,1422],"valid",[],"NV8"],[[1423,1423],"valid",[],"NV8"],[[1424,1424],"disallowed"],[[1425,1441],"valid"],[[1442,1442],"valid"],[[1443,1455],"valid"],[[1456,1465],"valid"],[[1466,1466],"valid"],[[1467,1469],"valid"],[[1470,1470],"valid",[],"NV8"],[[1471,1471],"valid"],[[1472,1472],"valid",[],"NV8"],[[1473,1474],"valid"],[[1475,1475],"valid",[],"NV8"],[[1476,1476],"valid"],[[1477,1477],"valid"],[[1478,1478],"valid",[],"NV8"],[[1479,1479],"valid"],[[1480,1487],"disallowed"],[[1488,1514],"valid"],[[1515,1519],"disallowed"],[[1520,1524],"valid"],[[1525,1535],"disallowed"],[[1536,1539],"disallowed"],[[1540,1540],"disallowed"],[[1541,1541],"disallowed"],[[1542,1546],"valid",[],"NV8"],[[1547,1547],"valid",[],"NV8"],[[1548,1548],"valid",[],"NV8"],[[1549,1551],"valid",[],"NV8"],[[1552,1557],"valid"],[[1558,1562],"valid"],[[1563,1563],"valid",[],"NV8"],[[1564,1564],"disallowed"],[[1565,1565],"disallowed"],[[1566,1566],"valid",[],"NV8"],[[1567,1567],"valid",[],"NV8"],[[1568,1568],"valid"],[[1569,1594],"valid"],[[1595,1599],"valid"],[[1600,1600],"valid",[],"NV8"],[[1601,1618],"valid"],[[1619,1621],"valid"],[[1622,1624],"valid"],[[1625,1630],"valid"],[[1631,1631],"valid"],[[1632,1641],"valid"],[[1642,1645],"valid",[],"NV8"],[[1646,1647],"valid"],[[1648,1652],"valid"],[[1653,1653],"mapped",[1575,1652]],[[1654,1654],"mapped",[1608,1652]],[[1655,1655],"mapped",[1735,1652]],[[1656,1656],"mapped",[1610,1652]],[[1657,1719],"valid"],[[1720,1721],"valid"],[[1722,1726],"valid"],[[1727,1727],"valid"],[[1728,1742],"valid"],[[1743,1743],"valid"],[[1744,1747],"valid"],[[1748,1748],"valid",[],"NV8"],[[1749,1756],"valid"],[[1757,1757],"disallowed"],[[1758,1758],"valid",[],"NV8"],[[1759,1768],"valid"],[[1769,1769],"valid",[],"NV8"],[[1770,1773],"valid"],[[1774,1775],"valid"],[[1776,1785],"valid"],[[1786,1790],"valid"],[[1791,1791],"valid"],[[1792,1805],"valid",[],"NV8"],[[1806,1806],"disallowed"],[[1807,1807],"disallowed"],[[1808,1836],"valid"],[[1837,1839],"valid"],[[1840,1866],"valid"],[[1867,1868],"disallowed"],[[1869,1871],"valid"],[[1872,1901],"valid"],[[1902,1919],"valid"],[[1920,1968],"valid"],[[1969,1969],"valid"],[[1970,1983],"disallowed"],[[1984,2037],"valid"],[[2038,2042],"valid",[],"NV8"],[[2043,2047],"disallowed"],[[2048,2093],"valid"],[[2094,2095],"disallowed"],[[2096,2110],"valid",[],"NV8"],[[2111,2111],"disallowed"],[[2112,2139],"valid"],[[2140,2141],"disallowed"],[[2142,2142],"valid",[],"NV8"],[[2143,2207],"disallowed"],[[2208,2208],"valid"],[[2209,2209],"valid"],[[2210,2220],"valid"],[[2221,2226],"valid"],[[2227,2228],"valid"],[[2229,2274],"disallowed"],[[2275,2275],"valid"],[[2276,2302],"valid"],[[2303,2303],"valid"],[[2304,2304],"valid"],[[2305,2307],"valid"],[[2308,2308],"valid"],[[2309,2361],"valid"],[[2362,2363],"valid"],[[2364,2381],"valid"],[[2382,2382],"valid"],[[2383,2383],"valid"],[[2384,2388],"valid"],[[2389,2389],"valid"],[[2390,2391],"valid"],[[2392,2392],"mapped",[2325,2364]],[[2393,2393],"mapped",[2326,2364]],[[2394,2394],"mapped",[2327,2364]],[[2395,2395],"mapped",[2332,2364]],[[2396,2396],"mapped",[2337,2364]],[[2397,2397],"mapped",[2338,2364]],[[2398,2398],"mapped",[2347,2364]],[[2399,2399],"mapped",[2351,2364]],[[2400,2403],"valid"],[[2404,2405],"valid",[],"NV8"],[[2406,2415],"valid"],[[2416,2416],"valid",[],"NV8"],[[2417,2418],"valid"],[[2419,2423],"valid"],[[2424,2424],"valid"],[[2425,2426],"valid"],[[2427,2428],"valid"],[[2429,2429],"valid"],[[2430,2431],"valid"],[[2432,2432],"valid"],[[2433,2435],"valid"],[[2436,2436],"disallowed"],[[2437,2444],"valid"],[[2445,2446],"disallowed"],[[2447,2448],"valid"],[[2449,2450],"disallowed"],[[2451,2472],"valid"],[[2473,2473],"disallowed"],[[2474,2480],"valid"],[[2481,2481],"disallowed"],[[2482,2482],"valid"],[[2483,2485],"disallowed"],[[2486,2489],"valid"],[[2490,2491],"disallowed"],[[2492,2492],"valid"],[[2493,2493],"valid"],[[2494,2500],"valid"],[[2501,2502],"disallowed"],[[2503,2504],"valid"],[[2505,2506],"disallowed"],[[2507,2509],"valid"],[[2510,2510],"valid"],[[2511,2518],"disallowed"],[[2519,2519],"valid"],[[2520,2523],"disallowed"],[[2524,2524],"mapped",[2465,2492]],[[2525,2525],"mapped",[2466,2492]],[[2526,2526],"disallowed"],[[2527,2527],"mapped",[2479,2492]],[[2528,2531],"valid"],[[2532,2533],"disallowed"],[[2534,2545],"valid"],[[2546,2554],"valid",[],"NV8"],[[2555,2555],"valid",[],"NV8"],[[2556,2560],"disallowed"],[[2561,2561],"valid"],[[2562,2562],"valid"],[[2563,2563],"valid"],[[2564,2564],"disallowed"],[[2565,2570],"valid"],[[2571,2574],"disallowed"],[[2575,2576],"valid"],[[2577,2578],"disallowed"],[[2579,2600],"valid"],[[2601,2601],"disallowed"],[[2602,2608],"valid"],[[2609,2609],"disallowed"],[[2610,2610],"valid"],[[2611,2611],"mapped",[2610,2620]],[[2612,2612],"disallowed"],[[2613,2613],"valid"],[[2614,2614],"mapped",[2616,2620]],[[2615,2615],"disallowed"],[[2616,2617],"valid"],[[2618,2619],"disallowed"],[[2620,2620],"valid"],[[2621,2621],"disallowed"],[[2622,2626],"valid"],[[2627,2630],"disallowed"],[[2631,2632],"valid"],[[2633,2634],"disallowed"],[[2635,2637],"valid"],[[2638,2640],"disallowed"],[[2641,2641],"valid"],[[2642,2648],"disallowed"],[[2649,2649],"mapped",[2582,2620]],[[2650,2650],"mapped",[2583,2620]],[[2651,2651],"mapped",[2588,2620]],[[2652,2652],"valid"],[[2653,2653],"disallowed"],[[2654,2654],"mapped",[2603,2620]],[[2655,2661],"disallowed"],[[2662,2676],"valid"],[[2677,2677],"valid"],[[2678,2688],"disallowed"],[[2689,2691],"valid"],[[2692,2692],"disallowed"],[[2693,2699],"valid"],[[2700,2700],"valid"],[[2701,2701],"valid"],[[2702,2702],"disallowed"],[[2703,2705],"valid"],[[2706,2706],"disallowed"],[[2707,2728],"valid"],[[2729,2729],"disallowed"],[[2730,2736],"valid"],[[2737,2737],"disallowed"],[[2738,2739],"valid"],[[2740,2740],"disallowed"],[[2741,2745],"valid"],[[2746,2747],"disallowed"],[[2748,2757],"valid"],[[2758,2758],"disallowed"],[[2759,2761],"valid"],[[2762,2762],"disallowed"],[[2763,2765],"valid"],[[2766,2767],"disallowed"],[[2768,2768],"valid"],[[2769,2783],"disallowed"],[[2784,2784],"valid"],[[2785,2787],"valid"],[[2788,2789],"disallowed"],[[2790,2799],"valid"],[[2800,2800],"valid",[],"NV8"],[[2801,2801],"valid",[],"NV8"],[[2802,2808],"disallowed"],[[2809,2809],"valid"],[[2810,2816],"disallowed"],[[2817,2819],"valid"],[[2820,2820],"disallowed"],[[2821,2828],"valid"],[[2829,2830],"disallowed"],[[2831,2832],"valid"],[[2833,2834],"disallowed"],[[2835,2856],"valid"],[[2857,2857],"disallowed"],[[2858,2864],"valid"],[[2865,2865],"disallowed"],[[2866,2867],"valid"],[[2868,2868],"disallowed"],[[2869,2869],"valid"],[[2870,2873],"valid"],[[2874,2875],"disallowed"],[[2876,2883],"valid"],[[2884,2884],"valid"],[[2885,2886],"disallowed"],[[2887,2888],"valid"],[[2889,2890],"disallowed"],[[2891,2893],"valid"],[[2894,2901],"disallowed"],[[2902,2903],"valid"],[[2904,2907],"disallowed"],[[2908,2908],"mapped",[2849,2876]],[[2909,2909],"mapped",[2850,2876]],[[2910,2910],"disallowed"],[[2911,2913],"valid"],[[2914,2915],"valid"],[[2916,2917],"disallowed"],[[2918,2927],"valid"],[[2928,2928],"valid",[],"NV8"],[[2929,2929],"valid"],[[2930,2935],"valid",[],"NV8"],[[2936,2945],"disallowed"],[[2946,2947],"valid"],[[2948,2948],"disallowed"],[[2949,2954],"valid"],[[2955,2957],"disallowed"],[[2958,2960],"valid"],[[2961,2961],"disallowed"],[[2962,2965],"valid"],[[2966,2968],"disallowed"],[[2969,2970],"valid"],[[2971,2971],"disallowed"],[[2972,2972],"valid"],[[2973,2973],"disallowed"],[[2974,2975],"valid"],[[2976,2978],"disallowed"],[[2979,2980],"valid"],[[2981,2983],"disallowed"],[[2984,2986],"valid"],[[2987,2989],"disallowed"],[[2990,2997],"valid"],[[2998,2998],"valid"],[[2999,3001],"valid"],[[3002,3005],"disallowed"],[[3006,3010],"valid"],[[3011,3013],"disallowed"],[[3014,3016],"valid"],[[3017,3017],"disallowed"],[[3018,3021],"valid"],[[3022,3023],"disallowed"],[[3024,3024],"valid"],[[3025,3030],"disallowed"],[[3031,3031],"valid"],[[3032,3045],"disallowed"],[[3046,3046],"valid"],[[3047,3055],"valid"],[[3056,3058],"valid",[],"NV8"],[[3059,3066],"valid",[],"NV8"],[[3067,3071],"disallowed"],[[3072,3072],"valid"],[[3073,3075],"valid"],[[3076,3076],"disallowed"],[[3077,3084],"valid"],[[3085,3085],"disallowed"],[[3086,3088],"valid"],[[3089,3089],"disallowed"],[[3090,3112],"valid"],[[3113,3113],"disallowed"],[[3114,3123],"valid"],[[3124,3124],"valid"],[[3125,3129],"valid"],[[3130,3132],"disallowed"],[[3133,3133],"valid"],[[3134,3140],"valid"],[[3141,3141],"disallowed"],[[3142,3144],"valid"],[[3145,3145],"disallowed"],[[3146,3149],"valid"],[[3150,3156],"disallowed"],[[3157,3158],"valid"],[[3159,3159],"disallowed"],[[3160,3161],"valid"],[[3162,3162],"valid"],[[3163,3167],"disallowed"],[[3168,3169],"valid"],[[3170,3171],"valid"],[[3172,3173],"disallowed"],[[3174,3183],"valid"],[[3184,3191],"disallowed"],[[3192,3199],"valid",[],"NV8"],[[3200,3200],"disallowed"],[[3201,3201],"valid"],[[3202,3203],"valid"],[[3204,3204],"disallowed"],[[3205,3212],"valid"],[[3213,3213],"disallowed"],[[3214,3216],"valid"],[[3217,3217],"disallowed"],[[3218,3240],"valid"],[[3241,3241],"disallowed"],[[3242,3251],"valid"],[[3252,3252],"disallowed"],[[3253,3257],"valid"],[[3258,3259],"disallowed"],[[3260,3261],"valid"],[[3262,3268],"valid"],[[3269,3269],"disallowed"],[[3270,3272],"valid"],[[3273,3273],"disallowed"],[[3274,3277],"valid"],[[3278,3284],"disallowed"],[[3285,3286],"valid"],[[3287,3293],"disallowed"],[[3294,3294],"valid"],[[3295,3295],"disallowed"],[[3296,3297],"valid"],[[3298,3299],"valid"],[[3300,3301],"disallowed"],[[3302,3311],"valid"],[[3312,3312],"disallowed"],[[3313,3314],"valid"],[[3315,3328],"disallowed"],[[3329,3329],"valid"],[[3330,3331],"valid"],[[3332,3332],"disallowed"],[[3333,3340],"valid"],[[3341,3341],"disallowed"],[[3342,3344],"valid"],[[3345,3345],"disallowed"],[[3346,3368],"valid"],[[3369,3369],"valid"],[[3370,3385],"valid"],[[3386,3386],"valid"],[[3387,3388],"disallowed"],[[3389,3389],"valid"],[[3390,3395],"valid"],[[3396,3396],"valid"],[[3397,3397],"disallowed"],[[3398,3400],"valid"],[[3401,3401],"disallowed"],[[3402,3405],"valid"],[[3406,3406],"valid"],[[3407,3414],"disallowed"],[[3415,3415],"valid"],[[3416,3422],"disallowed"],[[3423,3423],"valid"],[[3424,3425],"valid"],[[3426,3427],"valid"],[[3428,3429],"disallowed"],[[3430,3439],"valid"],[[3440,3445],"valid",[],"NV8"],[[3446,3448],"disallowed"],[[3449,3449],"valid",[],"NV8"],[[3450,3455],"valid"],[[3456,3457],"disallowed"],[[3458,3459],"valid"],[[3460,3460],"disallowed"],[[3461,3478],"valid"],[[3479,3481],"disallowed"],[[3482,3505],"valid"],[[3506,3506],"disallowed"],[[3507,3515],"valid"],[[3516,3516],"disallowed"],[[3517,3517],"valid"],[[3518,3519],"disallowed"],[[3520,3526],"valid"],[[3527,3529],"disallowed"],[[3530,3530],"valid"],[[3531,3534],"disallowed"],[[3535,3540],"valid"],[[3541,3541],"disallowed"],[[3542,3542],"valid"],[[3543,3543],"disallowed"],[[3544,3551],"valid"],[[3552,3557],"disallowed"],[[3558,3567],"valid"],[[3568,3569],"disallowed"],[[3570,3571],"valid"],[[3572,3572],"valid",[],"NV8"],[[3573,3584],"disallowed"],[[3585,3634],"valid"],[[3635,3635],"mapped",[3661,3634]],[[3636,3642],"valid"],[[3643,3646],"disallowed"],[[3647,3647],"valid",[],"NV8"],[[3648,3662],"valid"],[[3663,3663],"valid",[],"NV8"],[[3664,3673],"valid"],[[3674,3675],"valid",[],"NV8"],[[3676,3712],"disallowed"],[[3713,3714],"valid"],[[3715,3715],"disallowed"],[[3716,3716],"valid"],[[3717,3718],"disallowed"],[[3719,3720],"valid"],[[3721,3721],"disallowed"],[[3722,3722],"valid"],[[3723,3724],"disallowed"],[[3725,3725],"valid"],[[3726,3731],"disallowed"],[[3732,3735],"valid"],[[3736,3736],"disallowed"],[[3737,3743],"valid"],[[3744,3744],"disallowed"],[[3745,3747],"valid"],[[3748,3748],"disallowed"],[[3749,3749],"valid"],[[3750,3750],"disallowed"],[[3751,3751],"valid"],[[3752,3753],"disallowed"],[[3754,3755],"valid"],[[3756,3756],"disallowed"],[[3757,3762],"valid"],[[3763,3763],"mapped",[3789,3762]],[[3764,3769],"valid"],[[3770,3770],"disallowed"],[[3771,3773],"valid"],[[3774,3775],"disallowed"],[[3776,3780],"valid"],[[3781,3781],"disallowed"],[[3782,3782],"valid"],[[3783,3783],"disallowed"],[[3784,3789],"valid"],[[3790,3791],"disallowed"],[[3792,3801],"valid"],[[3802,3803],"disallowed"],[[3804,3804],"mapped",[3755,3737]],[[3805,3805],"mapped",[3755,3745]],[[3806,3807],"valid"],[[3808,3839],"disallowed"],[[3840,3840],"valid"],[[3841,3850],"valid",[],"NV8"],[[3851,3851],"valid"],[[3852,3852],"mapped",[3851]],[[3853,3863],"valid",[],"NV8"],[[3864,3865],"valid"],[[3866,3871],"valid",[],"NV8"],[[3872,3881],"valid"],[[3882,3892],"valid",[],"NV8"],[[3893,3893],"valid"],[[3894,3894],"valid",[],"NV8"],[[3895,3895],"valid"],[[3896,3896],"valid",[],"NV8"],[[3897,3897],"valid"],[[3898,3901],"valid",[],"NV8"],[[3902,3906],"valid"],[[3907,3907],"mapped",[3906,4023]],[[3908,3911],"valid"],[[3912,3912],"disallowed"],[[3913,3916],"valid"],[[3917,3917],"mapped",[3916,4023]],[[3918,3921],"valid"],[[3922,3922],"mapped",[3921,4023]],[[3923,3926],"valid"],[[3927,3927],"mapped",[3926,4023]],[[3928,3931],"valid"],[[3932,3932],"mapped",[3931,4023]],[[3933,3944],"valid"],[[3945,3945],"mapped",[3904,4021]],[[3946,3946],"valid"],[[3947,3948],"valid"],[[3949,3952],"disallowed"],[[3953,3954],"valid"],[[3955,3955],"mapped",[3953,3954]],[[3956,3956],"valid"],[[3957,3957],"mapped",[3953,3956]],[[3958,3958],"mapped",[4018,3968]],[[3959,3959],"mapped",[4018,3953,3968]],[[3960,3960],"mapped",[4019,3968]],[[3961,3961],"mapped",[4019,3953,3968]],[[3962,3968],"valid"],[[3969,3969],"mapped",[3953,3968]],[[3970,3972],"valid"],[[3973,3973],"valid",[],"NV8"],[[3974,3979],"valid"],[[3980,3983],"valid"],[[3984,3986],"valid"],[[3987,3987],"mapped",[3986,4023]],[[3988,3989],"valid"],[[3990,3990],"valid"],[[3991,3991],"valid"],[[3992,3992],"disallowed"],[[3993,3996],"valid"],[[3997,3997],"mapped",[3996,4023]],[[3998,4001],"valid"],[[4002,4002],"mapped",[4001,4023]],[[4003,4006],"valid"],[[4007,4007],"mapped",[4006,4023]],[[4008,4011],"valid"],[[4012,4012],"mapped",[4011,4023]],[[4013,4013],"valid"],[[4014,4016],"valid"],[[4017,4023],"valid"],[[4024,4024],"valid"],[[4025,4025],"mapped",[3984,4021]],[[4026,4028],"valid"],[[4029,4029],"disallowed"],[[4030,4037],"valid",[],"NV8"],[[4038,4038],"valid"],[[4039,4044],"valid",[],"NV8"],[[4045,4045],"disallowed"],[[4046,4046],"valid",[],"NV8"],[[4047,4047],"valid",[],"NV8"],[[4048,4049],"valid",[],"NV8"],[[4050,4052],"valid",[],"NV8"],[[4053,4056],"valid",[],"NV8"],[[4057,4058],"valid",[],"NV8"],[[4059,4095],"disallowed"],[[4096,4129],"valid"],[[4130,4130],"valid"],[[4131,4135],"valid"],[[4136,4136],"valid"],[[4137,4138],"valid"],[[4139,4139],"valid"],[[4140,4146],"valid"],[[4147,4149],"valid"],[[4150,4153],"valid"],[[4154,4159],"valid"],[[4160,4169],"valid"],[[4170,4175],"valid",[],"NV8"],[[4176,4185],"valid"],[[4186,4249],"valid"],[[4250,4253],"valid"],[[4254,4255],"valid",[],"NV8"],[[4256,4293],"disallowed"],[[4294,4294],"disallowed"],[[4295,4295],"mapped",[11559]],[[4296,4300],"disallowed"],[[4301,4301],"mapped",[11565]],[[4302,4303],"disallowed"],[[4304,4342],"valid"],[[4343,4344],"valid"],[[4345,4346],"valid"],[[4347,4347],"valid",[],"NV8"],[[4348,4348],"mapped",[4316]],[[4349,4351],"valid"],[[4352,4441],"valid",[],"NV8"],[[4442,4446],"valid",[],"NV8"],[[4447,4448],"disallowed"],[[4449,4514],"valid",[],"NV8"],[[4515,4519],"valid",[],"NV8"],[[4520,4601],"valid",[],"NV8"],[[4602,4607],"valid",[],"NV8"],[[4608,4614],"valid"],[[4615,4615],"valid"],[[4616,4678],"valid"],[[4679,4679],"valid"],[[4680,4680],"valid"],[[4681,4681],"disallowed"],[[4682,4685],"valid"],[[4686,4687],"disallowed"],[[4688,4694],"valid"],[[4695,4695],"disallowed"],[[4696,4696],"valid"],[[4697,4697],"disallowed"],[[4698,4701],"valid"],[[4702,4703],"disallowed"],[[4704,4742],"valid"],[[4743,4743],"valid"],[[4744,4744],"valid"],[[4745,4745],"disallowed"],[[4746,4749],"valid"],[[4750,4751],"disallowed"],[[4752,4782],"valid"],[[4783,4783],"valid"],[[4784,4784],"valid"],[[4785,4785],"disallowed"],[[4786,4789],"valid"],[[4790,4791],"disallowed"],[[4792,4798],"valid"],[[4799,4799],"disallowed"],[[4800,4800],"valid"],[[4801,4801],"disallowed"],[[4802,4805],"valid"],[[4806,4807],"disallowed"],[[4808,4814],"valid"],[[4815,4815],"valid"],[[4816,4822],"valid"],[[4823,4823],"disallowed"],[[4824,4846],"valid"],[[4847,4847],"valid"],[[4848,4878],"valid"],[[4879,4879],"valid"],[[4880,4880],"valid"],[[4881,4881],"disallowed"],[[4882,4885],"valid"],[[4886,4887],"disallowed"],[[4888,4894],"valid"],[[4895,4895],"valid"],[[4896,4934],"valid"],[[4935,4935],"valid"],[[4936,4954],"valid"],[[4955,4956],"disallowed"],[[4957,4958],"valid"],[[4959,4959],"valid"],[[4960,4960],"valid",[],"NV8"],[[4961,4988],"valid",[],"NV8"],[[4989,4991],"disallowed"],[[4992,5007],"valid"],[[5008,5017],"valid",[],"NV8"],[[5018,5023],"disallowed"],[[5024,5108],"valid"],[[5109,5109],"valid"],[[5110,5111],"disallowed"],[[5112,5112],"mapped",[5104]],[[5113,5113],"mapped",[5105]],[[5114,5114],"mapped",[5106]],[[5115,5115],"mapped",[5107]],[[5116,5116],"mapped",[5108]],[[5117,5117],"mapped",[5109]],[[5118,5119],"disallowed"],[[5120,5120],"valid",[],"NV8"],[[5121,5740],"valid"],[[5741,5742],"valid",[],"NV8"],[[5743,5750],"valid"],[[5751,5759],"valid"],[[5760,5760],"disallowed"],[[5761,5786],"valid"],[[5787,5788],"valid",[],"NV8"],[[5789,5791],"disallowed"],[[5792,5866],"valid"],[[5867,5872],"valid",[],"NV8"],[[5873,5880],"valid"],[[5881,5887],"disallowed"],[[5888,5900],"valid"],[[5901,5901],"disallowed"],[[5902,5908],"valid"],[[5909,5919],"disallowed"],[[5920,5940],"valid"],[[5941,5942],"valid",[],"NV8"],[[5943,5951],"disallowed"],[[5952,5971],"valid"],[[5972,5983],"disallowed"],[[5984,5996],"valid"],[[5997,5997],"disallowed"],[[5998,6000],"valid"],[[6001,6001],"disallowed"],[[6002,6003],"valid"],[[6004,6015],"disallowed"],[[6016,6067],"valid"],[[6068,6069],"disallowed"],[[6070,6099],"valid"],[[6100,6102],"valid",[],"NV8"],[[6103,6103],"valid"],[[6104,6107],"valid",[],"NV8"],[[6108,6108],"valid"],[[6109,6109],"valid"],[[6110,6111],"disallowed"],[[6112,6121],"valid"],[[6122,6127],"disallowed"],[[6128,6137],"valid",[],"NV8"],[[6138,6143],"disallowed"],[[6144,6149],"valid",[],"NV8"],[[6150,6150],"disallowed"],[[6151,6154],"valid",[],"NV8"],[[6155,6157],"ignored"],[[6158,6158],"disallowed"],[[6159,6159],"disallowed"],[[6160,6169],"valid"],[[6170,6175],"disallowed"],[[6176,6263],"valid"],[[6264,6271],"disallowed"],[[6272,6313],"valid"],[[6314,6314],"valid"],[[6315,6319],"disallowed"],[[6320,6389],"valid"],[[6390,6399],"disallowed"],[[6400,6428],"valid"],[[6429,6430],"valid"],[[6431,6431],"disallowed"],[[6432,6443],"valid"],[[6444,6447],"disallowed"],[[6448,6459],"valid"],[[6460,6463],"disallowed"],[[6464,6464],"valid",[],"NV8"],[[6465,6467],"disallowed"],[[6468,6469],"valid",[],"NV8"],[[6470,6509],"valid"],[[6510,6511],"disallowed"],[[6512,6516],"valid"],[[6517,6527],"disallowed"],[[6528,6569],"valid"],[[6570,6571],"valid"],[[6572,6575],"disallowed"],[[6576,6601],"valid"],[[6602,6607],"disallowed"],[[6608,6617],"valid"],[[6618,6618],"valid",[],"XV8"],[[6619,6621],"disallowed"],[[6622,6623],"valid",[],"NV8"],[[6624,6655],"valid",[],"NV8"],[[6656,6683],"valid"],[[6684,6685],"disallowed"],[[6686,6687],"valid",[],"NV8"],[[6688,6750],"valid"],[[6751,6751],"disallowed"],[[6752,6780],"valid"],[[6781,6782],"disallowed"],[[6783,6793],"valid"],[[6794,6799],"disallowed"],[[6800,6809],"valid"],[[6810,6815],"disallowed"],[[6816,6822],"valid",[],"NV8"],[[6823,6823],"valid"],[[6824,6829],"valid",[],"NV8"],[[6830,6831],"disallowed"],[[6832,6845],"valid"],[[6846,6846],"valid",[],"NV8"],[[6847,6911],"disallowed"],[[6912,6987],"valid"],[[6988,6991],"disallowed"],[[6992,7001],"valid"],[[7002,7018],"valid",[],"NV8"],[[7019,7027],"valid"],[[7028,7036],"valid",[],"NV8"],[[7037,7039],"disallowed"],[[7040,7082],"valid"],[[7083,7085],"valid"],[[7086,7097],"valid"],[[7098,7103],"valid"],[[7104,7155],"valid"],[[7156,7163],"disallowed"],[[7164,7167],"valid",[],"NV8"],[[7168,7223],"valid"],[[7224,7226],"disallowed"],[[7227,7231],"valid",[],"NV8"],[[7232,7241],"valid"],[[7242,7244],"disallowed"],[[7245,7293],"valid"],[[7294,7295],"valid",[],"NV8"],[[7296,7359],"disallowed"],[[7360,7367],"valid",[],"NV8"],[[7368,7375],"disallowed"],[[7376,7378],"valid"],[[7379,7379],"valid",[],"NV8"],[[7380,7410],"valid"],[[7411,7414],"valid"],[[7415,7415],"disallowed"],[[7416,7417],"valid"],[[7418,7423],"disallowed"],[[7424,7467],"valid"],[[7468,7468],"mapped",[97]],[[7469,7469],"mapped",[230]],[[7470,7470],"mapped",[98]],[[7471,7471],"valid"],[[7472,7472],"mapped",[100]],[[7473,7473],"mapped",[101]],[[7474,7474],"mapped",[477]],[[7475,7475],"mapped",[103]],[[7476,7476],"mapped",[104]],[[7477,7477],"mapped",[105]],[[7478,7478],"mapped",[106]],[[7479,7479],"mapped",[107]],[[7480,7480],"mapped",[108]],[[7481,7481],"mapped",[109]],[[7482,7482],"mapped",[110]],[[7483,7483],"valid"],[[7484,7484],"mapped",[111]],[[7485,7485],"mapped",[547]],[[7486,7486],"mapped",[112]],[[7487,7487],"mapped",[114]],[[7488,7488],"mapped",[116]],[[7489,7489],"mapped",[117]],[[7490,7490],"mapped",[119]],[[7491,7491],"mapped",[97]],[[7492,7492],"mapped",[592]],[[7493,7493],"mapped",[593]],[[7494,7494],"mapped",[7426]],[[7495,7495],"mapped",[98]],[[7496,7496],"mapped",[100]],[[7497,7497],"mapped",[101]],[[7498,7498],"mapped",[601]],[[7499,7499],"mapped",[603]],[[7500,7500],"mapped",[604]],[[7501,7501],"mapped",[103]],[[7502,7502],"valid"],[[7503,7503],"mapped",[107]],[[7504,7504],"mapped",[109]],[[7505,7505],"mapped",[331]],[[7506,7506],"mapped",[111]],[[7507,7507],"mapped",[596]],[[7508,7508],"mapped",[7446]],[[7509,7509],"mapped",[7447]],[[7510,7510],"mapped",[112]],[[7511,7511],"mapped",[116]],[[7512,7512],"mapped",[117]],[[7513,7513],"mapped",[7453]],[[7514,7514],"mapped",[623]],[[7515,7515],"mapped",[118]],[[7516,7516],"mapped",[7461]],[[7517,7517],"mapped",[946]],[[7518,7518],"mapped",[947]],[[7519,7519],"mapped",[948]],[[7520,7520],"mapped",[966]],[[7521,7521],"mapped",[967]],[[7522,7522],"mapped",[105]],[[7523,7523],"mapped",[114]],[[7524,7524],"mapped",[117]],[[7525,7525],"mapped",[118]],[[7526,7526],"mapped",[946]],[[7527,7527],"mapped",[947]],[[7528,7528],"mapped",[961]],[[7529,7529],"mapped",[966]],[[7530,7530],"mapped",[967]],[[7531,7531],"valid"],[[7532,7543],"valid"],[[7544,7544],"mapped",[1085]],[[7545,7578],"valid"],[[7579,7579],"mapped",[594]],[[7580,7580],"mapped",[99]],[[7581,7581],"mapped",[597]],[[7582,7582],"mapped",[240]],[[7583,7583],"mapped",[604]],[[7584,7584],"mapped",[102]],[[7585,7585],"mapped",[607]],[[7586,7586],"mapped",[609]],[[7587,7587],"mapped",[613]],[[7588,7588],"mapped",[616]],[[7589,7589],"mapped",[617]],[[7590,7590],"mapped",[618]],[[7591,7591],"mapped",[7547]],[[7592,7592],"mapped",[669]],[[7593,7593],"mapped",[621]],[[7594,7594],"mapped",[7557]],[[7595,7595],"mapped",[671]],[[7596,7596],"mapped",[625]],[[7597,7597],"mapped",[624]],[[7598,7598],"mapped",[626]],[[7599,7599],"mapped",[627]],[[7600,7600],"mapped",[628]],[[7601,7601],"mapped",[629]],[[7602,7602],"mapped",[632]],[[7603,7603],"mapped",[642]],[[7604,7604],"mapped",[643]],[[7605,7605],"mapped",[427]],[[7606,7606],"mapped",[649]],[[7607,7607],"mapped",[650]],[[7608,7608],"mapped",[7452]],[[7609,7609],"mapped",[651]],[[7610,7610],"mapped",[652]],[[7611,7611],"mapped",[122]],[[7612,7612],"mapped",[656]],[[7613,7613],"mapped",[657]],[[7614,7614],"mapped",[658]],[[7615,7615],"mapped",[952]],[[7616,7619],"valid"],[[7620,7626],"valid"],[[7627,7654],"valid"],[[7655,7669],"valid"],[[7670,7675],"disallowed"],[[7676,7676],"valid"],[[7677,7677],"valid"],[[7678,7679],"valid"],[[7680,7680],"mapped",[7681]],[[7681,7681],"valid"],[[7682,7682],"mapped",[7683]],[[7683,7683],"valid"],[[7684,7684],"mapped",[7685]],[[7685,7685],"valid"],[[7686,7686],"mapped",[7687]],[[7687,7687],"valid"],[[7688,7688],"mapped",[7689]],[[7689,7689],"valid"],[[7690,7690],"mapped",[7691]],[[7691,7691],"valid"],[[7692,7692],"mapped",[7693]],[[7693,7693],"valid"],[[7694,7694],"mapped",[7695]],[[7695,7695],"valid"],[[7696,7696],"mapped",[7697]],[[7697,7697],"valid"],[[7698,7698],"mapped",[7699]],[[7699,7699],"valid"],[[7700,7700],"mapped",[7701]],[[7701,7701],"valid"],[[7702,7702],"mapped",[7703]],[[7703,7703],"valid"],[[7704,7704],"mapped",[7705]],[[7705,7705],"valid"],[[7706,7706],"mapped",[7707]],[[7707,7707],"valid"],[[7708,7708],"mapped",[7709]],[[7709,7709],"valid"],[[7710,7710],"mapped",[7711]],[[7711,7711],"valid"],[[7712,7712],"mapped",[7713]],[[7713,7713],"valid"],[[7714,7714],"mapped",[7715]],[[7715,7715],"valid"],[[7716,7716],"mapped",[7717]],[[7717,7717],"valid"],[[7718,7718],"mapped",[7719]],[[7719,7719],"valid"],[[7720,7720],"mapped",[7721]],[[7721,7721],"valid"],[[7722,7722],"mapped",[7723]],[[7723,7723],"valid"],[[7724,7724],"mapped",[7725]],[[7725,7725],"valid"],[[7726,7726],"mapped",[7727]],[[7727,7727],"valid"],[[7728,7728],"mapped",[7729]],[[7729,7729],"valid"],[[7730,7730],"mapped",[7731]],[[7731,7731],"valid"],[[7732,7732],"mapped",[7733]],[[7733,7733],"valid"],[[7734,7734],"mapped",[7735]],[[7735,7735],"valid"],[[7736,7736],"mapped",[7737]],[[7737,7737],"valid"],[[7738,7738],"mapped",[7739]],[[7739,7739],"valid"],[[7740,7740],"mapped",[7741]],[[7741,7741],"valid"],[[7742,7742],"mapped",[7743]],[[7743,7743],"valid"],[[7744,7744],"mapped",[7745]],[[7745,7745],"valid"],[[7746,7746],"mapped",[7747]],[[7747,7747],"valid"],[[7748,7748],"mapped",[7749]],[[7749,7749],"valid"],[[7750,7750],"mapped",[7751]],[[7751,7751],"valid"],[[7752,7752],"mapped",[7753]],[[7753,7753],"valid"],[[7754,7754],"mapped",[7755]],[[7755,7755],"valid"],[[7756,7756],"mapped",[7757]],[[7757,7757],"valid"],[[7758,7758],"mapped",[7759]],[[7759,7759],"valid"],[[7760,7760],"mapped",[7761]],[[7761,7761],"valid"],[[7762,7762],"mapped",[7763]],[[7763,7763],"valid"],[[7764,7764],"mapped",[7765]],[[7765,7765],"valid"],[[7766,7766],"mapped",[7767]],[[7767,7767],"valid"],[[7768,7768],"mapped",[7769]],[[7769,7769],"valid"],[[7770,7770],"mapped",[7771]],[[7771,7771],"valid"],[[7772,7772],"mapped",[7773]],[[7773,7773],"valid"],[[7774,7774],"mapped",[7775]],[[7775,7775],"valid"],[[7776,7776],"mapped",[7777]],[[7777,7777],"valid"],[[7778,7778],"mapped",[7779]],[[7779,7779],"valid"],[[7780,7780],"mapped",[7781]],[[7781,7781],"valid"],[[7782,7782],"mapped",[7783]],[[7783,7783],"valid"],[[7784,7784],"mapped",[7785]],[[7785,7785],"valid"],[[7786,7786],"mapped",[7787]],[[7787,7787],"valid"],[[7788,7788],"mapped",[7789]],[[7789,7789],"valid"],[[7790,7790],"mapped",[7791]],[[7791,7791],"valid"],[[7792,7792],"mapped",[7793]],[[7793,7793],"valid"],[[7794,7794],"mapped",[7795]],[[7795,7795],"valid"],[[7796,7796],"mapped",[7797]],[[7797,7797],"valid"],[[7798,7798],"mapped",[7799]],[[7799,7799],"valid"],[[7800,7800],"mapped",[7801]],[[7801,7801],"valid"],[[7802,7802],"mapped",[7803]],[[7803,7803],"valid"],[[7804,7804],"mapped",[7805]],[[7805,7805],"valid"],[[7806,7806],"mapped",[7807]],[[7807,7807],"valid"],[[7808,7808],"mapped",[7809]],[[7809,7809],"valid"],[[7810,7810],"mapped",[7811]],[[7811,7811],"valid"],[[7812,7812],"mapped",[7813]],[[7813,7813],"valid"],[[7814,7814],"mapped",[7815]],[[7815,7815],"valid"],[[7816,7816],"mapped",[7817]],[[7817,7817],"valid"],[[7818,7818],"mapped",[7819]],[[7819,7819],"valid"],[[7820,7820],"mapped",[7821]],[[7821,7821],"valid"],[[7822,7822],"mapped",[7823]],[[7823,7823],"valid"],[[7824,7824],"mapped",[7825]],[[7825,7825],"valid"],[[7826,7826],"mapped",[7827]],[[7827,7827],"valid"],[[7828,7828],"mapped",[7829]],[[7829,7833],"valid"],[[7834,7834],"mapped",[97,702]],[[7835,7835],"mapped",[7777]],[[7836,7837],"valid"],[[7838,7838],"mapped",[115,115]],[[7839,7839],"valid"],[[7840,7840],"mapped",[7841]],[[7841,7841],"valid"],[[7842,7842],"mapped",[7843]],[[7843,7843],"valid"],[[7844,7844],"mapped",[7845]],[[7845,7845],"valid"],[[7846,7846],"mapped",[7847]],[[7847,7847],"valid"],[[7848,7848],"mapped",[7849]],[[7849,7849],"valid"],[[7850,7850],"mapped",[7851]],[[7851,7851],"valid"],[[7852,7852],"mapped",[7853]],[[7853,7853],"valid"],[[7854,7854],"mapped",[7855]],[[7855,7855],"valid"],[[7856,7856],"mapped",[7857]],[[7857,7857],"valid"],[[7858,7858],"mapped",[7859]],[[7859,7859],"valid"],[[7860,7860],"mapped",[7861]],[[7861,7861],"valid"],[[7862,7862],"mapped",[7863]],[[7863,7863],"valid"],[[7864,7864],"mapped",[7865]],[[7865,7865],"valid"],[[7866,7866],"mapped",[7867]],[[7867,7867],"valid"],[[7868,7868],"mapped",[7869]],[[7869,7869],"valid"],[[7870,7870],"mapped",[7871]],[[7871,7871],"valid"],[[7872,7872],"mapped",[7873]],[[7873,7873],"valid"],[[7874,7874],"mapped",[7875]],[[7875,7875],"valid"],[[7876,7876],"mapped",[7877]],[[7877,7877],"valid"],[[7878,7878],"mapped",[7879]],[[7879,7879],"valid"],[[7880,7880],"mapped",[7881]],[[7881,7881],"valid"],[[7882,7882],"mapped",[7883]],[[7883,7883],"valid"],[[7884,7884],"mapped",[7885]],[[7885,7885],"valid"],[[7886,7886],"mapped",[7887]],[[7887,7887],"valid"],[[7888,7888],"mapped",[7889]],[[7889,7889],"valid"],[[7890,7890],"mapped",[7891]],[[7891,7891],"valid"],[[7892,7892],"mapped",[7893]],[[7893,7893],"valid"],[[7894,7894],"mapped",[7895]],[[7895,7895],"valid"],[[7896,7896],"mapped",[7897]],[[7897,7897],"valid"],[[7898,7898],"mapped",[7899]],[[7899,7899],"valid"],[[7900,7900],"mapped",[7901]],[[7901,7901],"valid"],[[7902,7902],"mapped",[7903]],[[7903,7903],"valid"],[[7904,7904],"mapped",[7905]],[[7905,7905],"valid"],[[7906,7906],"mapped",[7907]],[[7907,7907],"valid"],[[7908,7908],"mapped",[7909]],[[7909,7909],"valid"],[[7910,7910],"mapped",[7911]],[[7911,7911],"valid"],[[7912,7912],"mapped",[7913]],[[7913,7913],"valid"],[[7914,7914],"mapped",[7915]],[[7915,7915],"valid"],[[7916,7916],"mapped",[7917]],[[7917,7917],"valid"],[[7918,7918],"mapped",[7919]],[[7919,7919],"valid"],[[7920,7920],"mapped",[7921]],[[7921,7921],"valid"],[[7922,7922],"mapped",[7923]],[[7923,7923],"valid"],[[7924,7924],"mapped",[7925]],[[7925,7925],"valid"],[[7926,7926],"mapped",[7927]],[[7927,7927],"valid"],[[7928,7928],"mapped",[7929]],[[7929,7929],"valid"],[[7930,7930],"mapped",[7931]],[[7931,7931],"valid"],[[7932,7932],"mapped",[7933]],[[7933,7933],"valid"],[[7934,7934],"mapped",[7935]],[[7935,7935],"valid"],[[7936,7943],"valid"],[[7944,7944],"mapped",[7936]],[[7945,7945],"mapped",[7937]],[[7946,7946],"mapped",[7938]],[[7947,7947],"mapped",[7939]],[[7948,7948],"mapped",[7940]],[[7949,7949],"mapped",[7941]],[[7950,7950],"mapped",[7942]],[[7951,7951],"mapped",[7943]],[[7952,7957],"valid"],[[7958,7959],"disallowed"],[[7960,7960],"mapped",[7952]],[[7961,7961],"mapped",[7953]],[[7962,7962],"mapped",[7954]],[[7963,7963],"mapped",[7955]],[[7964,7964],"mapped",[7956]],[[7965,7965],"mapped",[7957]],[[7966,7967],"disallowed"],[[7968,7975],"valid"],[[7976,7976],"mapped",[7968]],[[7977,7977],"mapped",[7969]],[[7978,7978],"mapped",[7970]],[[7979,7979],"mapped",[7971]],[[7980,7980],"mapped",[7972]],[[7981,7981],"mapped",[7973]],[[7982,7982],"mapped",[7974]],[[7983,7983],"mapped",[7975]],[[7984,7991],"valid"],[[7992,7992],"mapped",[7984]],[[7993,7993],"mapped",[7985]],[[7994,7994],"mapped",[7986]],[[7995,7995],"mapped",[7987]],[[7996,7996],"mapped",[7988]],[[7997,7997],"mapped",[7989]],[[7998,7998],"mapped",[7990]],[[7999,7999],"mapped",[7991]],[[8000,8005],"valid"],[[8006,8007],"disallowed"],[[8008,8008],"mapped",[8000]],[[8009,8009],"mapped",[8001]],[[8010,8010],"mapped",[8002]],[[8011,8011],"mapped",[8003]],[[8012,8012],"mapped",[8004]],[[8013,8013],"mapped",[8005]],[[8014,8015],"disallowed"],[[8016,8023],"valid"],[[8024,8024],"disallowed"],[[8025,8025],"mapped",[8017]],[[8026,8026],"disallowed"],[[8027,8027],"mapped",[8019]],[[8028,8028],"disallowed"],[[8029,8029],"mapped",[8021]],[[8030,8030],"disallowed"],[[8031,8031],"mapped",[8023]],[[8032,8039],"valid"],[[8040,8040],"mapped",[8032]],[[8041,8041],"mapped",[8033]],[[8042,8042],"mapped",[8034]],[[8043,8043],"mapped",[8035]],[[8044,8044],"mapped",[8036]],[[8045,8045],"mapped",[8037]],[[8046,8046],"mapped",[8038]],[[8047,8047],"mapped",[8039]],[[8048,8048],"valid"],[[8049,8049],"mapped",[940]],[[8050,8050],"valid"],[[8051,8051],"mapped",[941]],[[8052,8052],"valid"],[[8053,8053],"mapped",[942]],[[8054,8054],"valid"],[[8055,8055],"mapped",[943]],[[8056,8056],"valid"],[[8057,8057],"mapped",[972]],[[8058,8058],"valid"],[[8059,8059],"mapped",[973]],[[8060,8060],"valid"],[[8061,8061],"mapped",[974]],[[8062,8063],"disallowed"],[[8064,8064],"mapped",[7936,953]],[[8065,8065],"mapped",[7937,953]],[[8066,8066],"mapped",[7938,953]],[[8067,8067],"mapped",[7939,953]],[[8068,8068],"mapped",[7940,953]],[[8069,8069],"mapped",[7941,953]],[[8070,8070],"mapped",[7942,953]],[[8071,8071],"mapped",[7943,953]],[[8072,8072],"mapped",[7936,953]],[[8073,8073],"mapped",[7937,953]],[[8074,8074],"mapped",[7938,953]],[[8075,8075],"mapped",[7939,953]],[[8076,8076],"mapped",[7940,953]],[[8077,8077],"mapped",[7941,953]],[[8078,8078],"mapped",[7942,953]],[[8079,8079],"mapped",[7943,953]],[[8080,8080],"mapped",[7968,953]],[[8081,8081],"mapped",[7969,953]],[[8082,8082],"mapped",[7970,953]],[[8083,8083],"mapped",[7971,953]],[[8084,8084],"mapped",[7972,953]],[[8085,8085],"mapped",[7973,953]],[[8086,8086],"mapped",[7974,953]],[[8087,8087],"mapped",[7975,953]],[[8088,8088],"mapped",[7968,953]],[[8089,8089],"mapped",[7969,953]],[[8090,8090],"mapped",[7970,953]],[[8091,8091],"mapped",[7971,953]],[[8092,8092],"mapped",[7972,953]],[[8093,8093],"mapped",[7973,953]],[[8094,8094],"mapped",[7974,953]],[[8095,8095],"mapped",[7975,953]],[[8096,8096],"mapped",[8032,953]],[[8097,8097],"mapped",[8033,953]],[[8098,8098],"mapped",[8034,953]],[[8099,8099],"mapped",[8035,953]],[[8100,8100],"mapped",[8036,953]],[[8101,8101],"mapped",[8037,953]],[[8102,8102],"mapped",[8038,953]],[[8103,8103],"mapped",[8039,953]],[[8104,8104],"mapped",[8032,953]],[[8105,8105],"mapped",[8033,953]],[[8106,8106],"mapped",[8034,953]],[[8107,8107],"mapped",[8035,953]],[[8108,8108],"mapped",[8036,953]],[[8109,8109],"mapped",[8037,953]],[[8110,8110],"mapped",[8038,953]],[[8111,8111],"mapped",[8039,953]],[[8112,8113],"valid"],[[8114,8114],"mapped",[8048,953]],[[8115,8115],"mapped",[945,953]],[[8116,8116],"mapped",[940,953]],[[8117,8117],"disallowed"],[[8118,8118],"valid"],[[8119,8119],"mapped",[8118,953]],[[8120,8120],"mapped",[8112]],[[8121,8121],"mapped",[8113]],[[8122,8122],"mapped",[8048]],[[8123,8123],"mapped",[940]],[[8124,8124],"mapped",[945,953]],[[8125,8125],"disallowed_STD3_mapped",[32,787]],[[8126,8126],"mapped",[953]],[[8127,8127],"disallowed_STD3_mapped",[32,787]],[[8128,8128],"disallowed_STD3_mapped",[32,834]],[[8129,8129],"disallowed_STD3_mapped",[32,776,834]],[[8130,8130],"mapped",[8052,953]],[[8131,8131],"mapped",[951,953]],[[8132,8132],"mapped",[942,953]],[[8133,8133],"disallowed"],[[8134,8134],"valid"],[[8135,8135],"mapped",[8134,953]],[[8136,8136],"mapped",[8050]],[[8137,8137],"mapped",[941]],[[8138,8138],"mapped",[8052]],[[8139,8139],"mapped",[942]],[[8140,8140],"mapped",[951,953]],[[8141,8141],"disallowed_STD3_mapped",[32,787,768]],[[8142,8142],"disallowed_STD3_mapped",[32,787,769]],[[8143,8143],"disallowed_STD3_mapped",[32,787,834]],[[8144,8146],"valid"],[[8147,8147],"mapped",[912]],[[8148,8149],"disallowed"],[[8150,8151],"valid"],[[8152,8152],"mapped",[8144]],[[8153,8153],"mapped",[8145]],[[8154,8154],"mapped",[8054]],[[8155,8155],"mapped",[943]],[[8156,8156],"disallowed"],[[8157,8157],"disallowed_STD3_mapped",[32,788,768]],[[8158,8158],"disallowed_STD3_mapped",[32,788,769]],[[8159,8159],"disallowed_STD3_mapped",[32,788,834]],[[8160,8162],"valid"],[[8163,8163],"mapped",[944]],[[8164,8167],"valid"],[[8168,8168],"mapped",[8160]],[[8169,8169],"mapped",[8161]],[[8170,8170],"mapped",[8058]],[[8171,8171],"mapped",[973]],[[8172,8172],"mapped",[8165]],[[8173,8173],"disallowed_STD3_mapped",[32,776,768]],[[8174,8174],"disallowed_STD3_mapped",[32,776,769]],[[8175,8175],"disallowed_STD3_mapped",[96]],[[8176,8177],"disallowed"],[[8178,8178],"mapped",[8060,953]],[[8179,8179],"mapped",[969,953]],[[8180,8180],"mapped",[974,953]],[[8181,8181],"disallowed"],[[8182,8182],"valid"],[[8183,8183],"mapped",[8182,953]],[[8184,8184],"mapped",[8056]],[[8185,8185],"mapped",[972]],[[8186,8186],"mapped",[8060]],[[8187,8187],"mapped",[974]],[[8188,8188],"mapped",[969,953]],[[8189,8189],"disallowed_STD3_mapped",[32,769]],[[8190,8190],"disallowed_STD3_mapped",[32,788]],[[8191,8191],"disallowed"],[[8192,8202],"disallowed_STD3_mapped",[32]],[[8203,8203],"ignored"],[[8204,8205],"deviation",[]],[[8206,8207],"disallowed"],[[8208,8208],"valid",[],"NV8"],[[8209,8209],"mapped",[8208]],[[8210,8214],"valid",[],"NV8"],[[8215,8215],"disallowed_STD3_mapped",[32,819]],[[8216,8227],"valid",[],"NV8"],[[8228,8230],"disallowed"],[[8231,8231],"valid",[],"NV8"],[[8232,8238],"disallowed"],[[8239,8239],"disallowed_STD3_mapped",[32]],[[8240,8242],"valid",[],"NV8"],[[8243,8243],"mapped",[8242,8242]],[[8244,8244],"mapped",[8242,8242,8242]],[[8245,8245],"valid",[],"NV8"],[[8246,8246],"mapped",[8245,8245]],[[8247,8247],"mapped",[8245,8245,8245]],[[8248,8251],"valid",[],"NV8"],[[8252,8252],"disallowed_STD3_mapped",[33,33]],[[8253,8253],"valid",[],"NV8"],[[8254,8254],"disallowed_STD3_mapped",[32,773]],[[8255,8262],"valid",[],"NV8"],[[8263,8263],"disallowed_STD3_mapped",[63,63]],[[8264,8264],"disallowed_STD3_mapped",[63,33]],[[8265,8265],"disallowed_STD3_mapped",[33,63]],[[8266,8269],"valid",[],"NV8"],[[8270,8274],"valid",[],"NV8"],[[8275,8276],"valid",[],"NV8"],[[8277,8278],"valid",[],"NV8"],[[8279,8279],"mapped",[8242,8242,8242,8242]],[[8280,8286],"valid",[],"NV8"],[[8287,8287],"disallowed_STD3_mapped",[32]],[[8288,8288],"ignored"],[[8289,8291],"disallowed"],[[8292,8292],"ignored"],[[8293,8293],"disallowed"],[[8294,8297],"disallowed"],[[8298,8303],"disallowed"],[[8304,8304],"mapped",[48]],[[8305,8305],"mapped",[105]],[[8306,8307],"disallowed"],[[8308,8308],"mapped",[52]],[[8309,8309],"mapped",[53]],[[8310,8310],"mapped",[54]],[[8311,8311],"mapped",[55]],[[8312,8312],"mapped",[56]],[[8313,8313],"mapped",[57]],[[8314,8314],"disallowed_STD3_mapped",[43]],[[8315,8315],"mapped",[8722]],[[8316,8316],"disallowed_STD3_mapped",[61]],[[8317,8317],"disallowed_STD3_mapped",[40]],[[8318,8318],"disallowed_STD3_mapped",[41]],[[8319,8319],"mapped",[110]],[[8320,8320],"mapped",[48]],[[8321,8321],"mapped",[49]],[[8322,8322],"mapped",[50]],[[8323,8323],"mapped",[51]],[[8324,8324],"mapped",[52]],[[8325,8325],"mapped",[53]],[[8326,8326],"mapped",[54]],[[8327,8327],"mapped",[55]],[[8328,8328],"mapped",[56]],[[8329,8329],"mapped",[57]],[[8330,8330],"disallowed_STD3_mapped",[43]],[[8331,8331],"mapped",[8722]],[[8332,8332],"disallowed_STD3_mapped",[61]],[[8333,8333],"disallowed_STD3_mapped",[40]],[[8334,8334],"disallowed_STD3_mapped",[41]],[[8335,8335],"disallowed"],[[8336,8336],"mapped",[97]],[[8337,8337],"mapped",[101]],[[8338,8338],"mapped",[111]],[[8339,8339],"mapped",[120]],[[8340,8340],"mapped",[601]],[[8341,8341],"mapped",[104]],[[8342,8342],"mapped",[107]],[[8343,8343],"mapped",[108]],[[8344,8344],"mapped",[109]],[[8345,8345],"mapped",[110]],[[8346,8346],"mapped",[112]],[[8347,8347],"mapped",[115]],[[8348,8348],"mapped",[116]],[[8349,8351],"disallowed"],[[8352,8359],"valid",[],"NV8"],[[8360,8360],"mapped",[114,115]],[[8361,8362],"valid",[],"NV8"],[[8363,8363],"valid",[],"NV8"],[[8364,8364],"valid",[],"NV8"],[[8365,8367],"valid",[],"NV8"],[[8368,8369],"valid",[],"NV8"],[[8370,8373],"valid",[],"NV8"],[[8374,8376],"valid",[],"NV8"],[[8377,8377],"valid",[],"NV8"],[[8378,8378],"valid",[],"NV8"],[[8379,8381],"valid",[],"NV8"],[[8382,8382],"valid",[],"NV8"],[[8383,8399],"disallowed"],[[8400,8417],"valid",[],"NV8"],[[8418,8419],"valid",[],"NV8"],[[8420,8426],"valid",[],"NV8"],[[8427,8427],"valid",[],"NV8"],[[8428,8431],"valid",[],"NV8"],[[8432,8432],"valid",[],"NV8"],[[8433,8447],"disallowed"],[[8448,8448],"disallowed_STD3_mapped",[97,47,99]],[[8449,8449],"disallowed_STD3_mapped",[97,47,115]],[[8450,8450],"mapped",[99]],[[8451,8451],"mapped",[176,99]],[[8452,8452],"valid",[],"NV8"],[[8453,8453],"disallowed_STD3_mapped",[99,47,111]],[[8454,8454],"disallowed_STD3_mapped",[99,47,117]],[[8455,8455],"mapped",[603]],[[8456,8456],"valid",[],"NV8"],[[8457,8457],"mapped",[176,102]],[[8458,8458],"mapped",[103]],[[8459,8462],"mapped",[104]],[[8463,8463],"mapped",[295]],[[8464,8465],"mapped",[105]],[[8466,8467],"mapped",[108]],[[8468,8468],"valid",[],"NV8"],[[8469,8469],"mapped",[110]],[[8470,8470],"mapped",[110,111]],[[8471,8472],"valid",[],"NV8"],[[8473,8473],"mapped",[112]],[[8474,8474],"mapped",[113]],[[8475,8477],"mapped",[114]],[[8478,8479],"valid",[],"NV8"],[[8480,8480],"mapped",[115,109]],[[8481,8481],"mapped",[116,101,108]],[[8482,8482],"mapped",[116,109]],[[8483,8483],"valid",[],"NV8"],[[8484,8484],"mapped",[122]],[[8485,8485],"valid",[],"NV8"],[[8486,8486],"mapped",[969]],[[8487,8487],"valid",[],"NV8"],[[8488,8488],"mapped",[122]],[[8489,8489],"valid",[],"NV8"],[[8490,8490],"mapped",[107]],[[8491,8491],"mapped",[229]],[[8492,8492],"mapped",[98]],[[8493,8493],"mapped",[99]],[[8494,8494],"valid",[],"NV8"],[[8495,8496],"mapped",[101]],[[8497,8497],"mapped",[102]],[[8498,8498],"disallowed"],[[8499,8499],"mapped",[109]],[[8500,8500],"mapped",[111]],[[8501,8501],"mapped",[1488]],[[8502,8502],"mapped",[1489]],[[8503,8503],"mapped",[1490]],[[8504,8504],"mapped",[1491]],[[8505,8505],"mapped",[105]],[[8506,8506],"valid",[],"NV8"],[[8507,8507],"mapped",[102,97,120]],[[8508,8508],"mapped",[960]],[[8509,8510],"mapped",[947]],[[8511,8511],"mapped",[960]],[[8512,8512],"mapped",[8721]],[[8513,8516],"valid",[],"NV8"],[[8517,8518],"mapped",[100]],[[8519,8519],"mapped",[101]],[[8520,8520],"mapped",[105]],[[8521,8521],"mapped",[106]],[[8522,8523],"valid",[],"NV8"],[[8524,8524],"valid",[],"NV8"],[[8525,8525],"valid",[],"NV8"],[[8526,8526],"valid"],[[8527,8527],"valid",[],"NV8"],[[8528,8528],"mapped",[49,8260,55]],[[8529,8529],"mapped",[49,8260,57]],[[8530,8530],"mapped",[49,8260,49,48]],[[8531,8531],"mapped",[49,8260,51]],[[8532,8532],"mapped",[50,8260,51]],[[8533,8533],"mapped",[49,8260,53]],[[8534,8534],"mapped",[50,8260,53]],[[8535,8535],"mapped",[51,8260,53]],[[8536,8536],"mapped",[52,8260,53]],[[8537,8537],"mapped",[49,8260,54]],[[8538,8538],"mapped",[53,8260,54]],[[8539,8539],"mapped",[49,8260,56]],[[8540,8540],"mapped",[51,8260,56]],[[8541,8541],"mapped",[53,8260,56]],[[8542,8542],"mapped",[55,8260,56]],[[8543,8543],"mapped",[49,8260]],[[8544,8544],"mapped",[105]],[[8545,8545],"mapped",[105,105]],[[8546,8546],"mapped",[105,105,105]],[[8547,8547],"mapped",[105,118]],[[8548,8548],"mapped",[118]],[[8549,8549],"mapped",[118,105]],[[8550,8550],"mapped",[118,105,105]],[[8551,8551],"mapped",[118,105,105,105]],[[8552,8552],"mapped",[105,120]],[[8553,8553],"mapped",[120]],[[8554,8554],"mapped",[120,105]],[[8555,8555],"mapped",[120,105,105]],[[8556,8556],"mapped",[108]],[[8557,8557],"mapped",[99]],[[8558,8558],"mapped",[100]],[[8559,8559],"mapped",[109]],[[8560,8560],"mapped",[105]],[[8561,8561],"mapped",[105,105]],[[8562,8562],"mapped",[105,105,105]],[[8563,8563],"mapped",[105,118]],[[8564,8564],"mapped",[118]],[[8565,8565],"mapped",[118,105]],[[8566,8566],"mapped",[118,105,105]],[[8567,8567],"mapped",[118,105,105,105]],[[8568,8568],"mapped",[105,120]],[[8569,8569],"mapped",[120]],[[8570,8570],"mapped",[120,105]],[[8571,8571],"mapped",[120,105,105]],[[8572,8572],"mapped",[108]],[[8573,8573],"mapped",[99]],[[8574,8574],"mapped",[100]],[[8575,8575],"mapped",[109]],[[8576,8578],"valid",[],"NV8"],[[8579,8579],"disallowed"],[[8580,8580],"valid"],[[8581,8584],"valid",[],"NV8"],[[8585,8585],"mapped",[48,8260,51]],[[8586,8587],"valid",[],"NV8"],[[8588,8591],"disallowed"],[[8592,8682],"valid",[],"NV8"],[[8683,8691],"valid",[],"NV8"],[[8692,8703],"valid",[],"NV8"],[[8704,8747],"valid",[],"NV8"],[[8748,8748],"mapped",[8747,8747]],[[8749,8749],"mapped",[8747,8747,8747]],[[8750,8750],"valid",[],"NV8"],[[8751,8751],"mapped",[8750,8750]],[[8752,8752],"mapped",[8750,8750,8750]],[[8753,8799],"valid",[],"NV8"],[[8800,8800],"disallowed_STD3_valid"],[[8801,8813],"valid",[],"NV8"],[[8814,8815],"disallowed_STD3_valid"],[[8816,8945],"valid",[],"NV8"],[[8946,8959],"valid",[],"NV8"],[[8960,8960],"valid",[],"NV8"],[[8961,8961],"valid",[],"NV8"],[[8962,9000],"valid",[],"NV8"],[[9001,9001],"mapped",[12296]],[[9002,9002],"mapped",[12297]],[[9003,9082],"valid",[],"NV8"],[[9083,9083],"valid",[],"NV8"],[[9084,9084],"valid",[],"NV8"],[[9085,9114],"valid",[],"NV8"],[[9115,9166],"valid",[],"NV8"],[[9167,9168],"valid",[],"NV8"],[[9169,9179],"valid",[],"NV8"],[[9180,9191],"valid",[],"NV8"],[[9192,9192],"valid",[],"NV8"],[[9193,9203],"valid",[],"NV8"],[[9204,9210],"valid",[],"NV8"],[[9211,9215],"disallowed"],[[9216,9252],"valid",[],"NV8"],[[9253,9254],"valid",[],"NV8"],[[9255,9279],"disallowed"],[[9280,9290],"valid",[],"NV8"],[[9291,9311],"disallowed"],[[9312,9312],"mapped",[49]],[[9313,9313],"mapped",[50]],[[9314,9314],"mapped",[51]],[[9315,9315],"mapped",[52]],[[9316,9316],"mapped",[53]],[[9317,9317],"mapped",[54]],[[9318,9318],"mapped",[55]],[[9319,9319],"mapped",[56]],[[9320,9320],"mapped",[57]],[[9321,9321],"mapped",[49,48]],[[9322,9322],"mapped",[49,49]],[[9323,9323],"mapped",[49,50]],[[9324,9324],"mapped",[49,51]],[[9325,9325],"mapped",[49,52]],[[9326,9326],"mapped",[49,53]],[[9327,9327],"mapped",[49,54]],[[9328,9328],"mapped",[49,55]],[[9329,9329],"mapped",[49,56]],[[9330,9330],"mapped",[49,57]],[[9331,9331],"mapped",[50,48]],[[9332,9332],"disallowed_STD3_mapped",[40,49,41]],[[9333,9333],"disallowed_STD3_mapped",[40,50,41]],[[9334,9334],"disallowed_STD3_mapped",[40,51,41]],[[9335,9335],"disallowed_STD3_mapped",[40,52,41]],[[9336,9336],"disallowed_STD3_mapped",[40,53,41]],[[9337,9337],"disallowed_STD3_mapped",[40,54,41]],[[9338,9338],"disallowed_STD3_mapped",[40,55,41]],[[9339,9339],"disallowed_STD3_mapped",[40,56,41]],[[9340,9340],"disallowed_STD3_mapped",[40,57,41]],[[9341,9341],"disallowed_STD3_mapped",[40,49,48,41]],[[9342,9342],"disallowed_STD3_mapped",[40,49,49,41]],[[9343,9343],"disallowed_STD3_mapped",[40,49,50,41]],[[9344,9344],"disallowed_STD3_mapped",[40,49,51,41]],[[9345,9345],"disallowed_STD3_mapped",[40,49,52,41]],[[9346,9346],"disallowed_STD3_mapped",[40,49,53,41]],[[9347,9347],"disallowed_STD3_mapped",[40,49,54,41]],[[9348,9348],"disallowed_STD3_mapped",[40,49,55,41]],[[9349,9349],"disallowed_STD3_mapped",[40,49,56,41]],[[9350,9350],"disallowed_STD3_mapped",[40,49,57,41]],[[9351,9351],"disallowed_STD3_mapped",[40,50,48,41]],[[9352,9371],"disallowed"],[[9372,9372],"disallowed_STD3_mapped",[40,97,41]],[[9373,9373],"disallowed_STD3_mapped",[40,98,41]],[[9374,9374],"disallowed_STD3_mapped",[40,99,41]],[[9375,9375],"disallowed_STD3_mapped",[40,100,41]],[[9376,9376],"disallowed_STD3_mapped",[40,101,41]],[[9377,9377],"disallowed_STD3_mapped",[40,102,41]],[[9378,9378],"disallowed_STD3_mapped",[40,103,41]],[[9379,9379],"disallowed_STD3_mapped",[40,104,41]],[[9380,9380],"disallowed_STD3_mapped",[40,105,41]],[[9381,9381],"disallowed_STD3_mapped",[40,106,41]],[[9382,9382],"disallowed_STD3_mapped",[40,107,41]],[[9383,9383],"disallowed_STD3_mapped",[40,108,41]],[[9384,9384],"disallowed_STD3_mapped",[40,109,41]],[[9385,9385],"disallowed_STD3_mapped",[40,110,41]],[[9386,9386],"disallowed_STD3_mapped",[40,111,41]],[[9387,9387],"disallowed_STD3_mapped",[40,112,41]],[[9388,9388],"disallowed_STD3_mapped",[40,113,41]],[[9389,9389],"disallowed_STD3_mapped",[40,114,41]],[[9390,9390],"disallowed_STD3_mapped",[40,115,41]],[[9391,9391],"disallowed_STD3_mapped",[40,116,41]],[[9392,9392],"disallowed_STD3_mapped",[40,117,41]],[[9393,9393],"disallowed_STD3_mapped",[40,118,41]],[[9394,9394],"disallowed_STD3_mapped",[40,119,41]],[[9395,9395],"disallowed_STD3_mapped",[40,120,41]],[[9396,9396],"disallowed_STD3_mapped",[40,121,41]],[[9397,9397],"disallowed_STD3_mapped",[40,122,41]],[[9398,9398],"mapped",[97]],[[9399,9399],"mapped",[98]],[[9400,9400],"mapped",[99]],[[9401,9401],"mapped",[100]],[[9402,9402],"mapped",[101]],[[9403,9403],"mapped",[102]],[[9404,9404],"mapped",[103]],[[9405,9405],"mapped",[104]],[[9406,9406],"mapped",[105]],[[9407,9407],"mapped",[106]],[[9408,9408],"mapped",[107]],[[9409,9409],"mapped",[108]],[[9410,9410],"mapped",[109]],[[9411,9411],"mapped",[110]],[[9412,9412],"mapped",[111]],[[9413,9413],"mapped",[112]],[[9414,9414],"mapped",[113]],[[9415,9415],"mapped",[114]],[[9416,9416],"mapped",[115]],[[9417,9417],"mapped",[116]],[[9418,9418],"mapped",[117]],[[9419,9419],"mapped",[118]],[[9420,9420],"mapped",[119]],[[9421,9421],"mapped",[120]],[[9422,9422],"mapped",[121]],[[9423,9423],"mapped",[122]],[[9424,9424],"mapped",[97]],[[9425,9425],"mapped",[98]],[[9426,9426],"mapped",[99]],[[9427,9427],"mapped",[100]],[[9428,9428],"mapped",[101]],[[9429,9429],"mapped",[102]],[[9430,9430],"mapped",[103]],[[9431,9431],"mapped",[104]],[[9432,9432],"mapped",[105]],[[9433,9433],"mapped",[106]],[[9434,9434],"mapped",[107]],[[9435,9435],"mapped",[108]],[[9436,9436],"mapped",[109]],[[9437,9437],"mapped",[110]],[[9438,9438],"mapped",[111]],[[9439,9439],"mapped",[112]],[[9440,9440],"mapped",[113]],[[9441,9441],"mapped",[114]],[[9442,9442],"mapped",[115]],[[9443,9443],"mapped",[116]],[[9444,9444],"mapped",[117]],[[9445,9445],"mapped",[118]],[[9446,9446],"mapped",[119]],[[9447,9447],"mapped",[120]],[[9448,9448],"mapped",[121]],[[9449,9449],"mapped",[122]],[[9450,9450],"mapped",[48]],[[9451,9470],"valid",[],"NV8"],[[9471,9471],"valid",[],"NV8"],[[9472,9621],"valid",[],"NV8"],[[9622,9631],"valid",[],"NV8"],[[9632,9711],"valid",[],"NV8"],[[9712,9719],"valid",[],"NV8"],[[9720,9727],"valid",[],"NV8"],[[9728,9747],"valid",[],"NV8"],[[9748,9749],"valid",[],"NV8"],[[9750,9751],"valid",[],"NV8"],[[9752,9752],"valid",[],"NV8"],[[9753,9753],"valid",[],"NV8"],[[9754,9839],"valid",[],"NV8"],[[9840,9841],"valid",[],"NV8"],[[9842,9853],"valid",[],"NV8"],[[9854,9855],"valid",[],"NV8"],[[9856,9865],"valid",[],"NV8"],[[9866,9873],"valid",[],"NV8"],[[9874,9884],"valid",[],"NV8"],[[9885,9885],"valid",[],"NV8"],[[9886,9887],"valid",[],"NV8"],[[9888,9889],"valid",[],"NV8"],[[9890,9905],"valid",[],"NV8"],[[9906,9906],"valid",[],"NV8"],[[9907,9916],"valid",[],"NV8"],[[9917,9919],"valid",[],"NV8"],[[9920,9923],"valid",[],"NV8"],[[9924,9933],"valid",[],"NV8"],[[9934,9934],"valid",[],"NV8"],[[9935,9953],"valid",[],"NV8"],[[9954,9954],"valid",[],"NV8"],[[9955,9955],"valid",[],"NV8"],[[9956,9959],"valid",[],"NV8"],[[9960,9983],"valid",[],"NV8"],[[9984,9984],"valid",[],"NV8"],[[9985,9988],"valid",[],"NV8"],[[9989,9989],"valid",[],"NV8"],[[9990,9993],"valid",[],"NV8"],[[9994,9995],"valid",[],"NV8"],[[9996,10023],"valid",[],"NV8"],[[10024,10024],"valid",[],"NV8"],[[10025,10059],"valid",[],"NV8"],[[10060,10060],"valid",[],"NV8"],[[10061,10061],"valid",[],"NV8"],[[10062,10062],"valid",[],"NV8"],[[10063,10066],"valid",[],"NV8"],[[10067,10069],"valid",[],"NV8"],[[10070,10070],"valid",[],"NV8"],[[10071,10071],"valid",[],"NV8"],[[10072,10078],"valid",[],"NV8"],[[10079,10080],"valid",[],"NV8"],[[10081,10087],"valid",[],"NV8"],[[10088,10101],"valid",[],"NV8"],[[10102,10132],"valid",[],"NV8"],[[10133,10135],"valid",[],"NV8"],[[10136,10159],"valid",[],"NV8"],[[10160,10160],"valid",[],"NV8"],[[10161,10174],"valid",[],"NV8"],[[10175,10175],"valid",[],"NV8"],[[10176,10182],"valid",[],"NV8"],[[10183,10186],"valid",[],"NV8"],[[10187,10187],"valid",[],"NV8"],[[10188,10188],"valid",[],"NV8"],[[10189,10189],"valid",[],"NV8"],[[10190,10191],"valid",[],"NV8"],[[10192,10219],"valid",[],"NV8"],[[10220,10223],"valid",[],"NV8"],[[10224,10239],"valid",[],"NV8"],[[10240,10495],"valid",[],"NV8"],[[10496,10763],"valid",[],"NV8"],[[10764,10764],"mapped",[8747,8747,8747,8747]],[[10765,10867],"valid",[],"NV8"],[[10868,10868],"disallowed_STD3_mapped",[58,58,61]],[[10869,10869],"disallowed_STD3_mapped",[61,61]],[[10870,10870],"disallowed_STD3_mapped",[61,61,61]],[[10871,10971],"valid",[],"NV8"],[[10972,10972],"mapped",[10973,824]],[[10973,11007],"valid",[],"NV8"],[[11008,11021],"valid",[],"NV8"],[[11022,11027],"valid",[],"NV8"],[[11028,11034],"valid",[],"NV8"],[[11035,11039],"valid",[],"NV8"],[[11040,11043],"valid",[],"NV8"],[[11044,11084],"valid",[],"NV8"],[[11085,11087],"valid",[],"NV8"],[[11088,11092],"valid",[],"NV8"],[[11093,11097],"valid",[],"NV8"],[[11098,11123],"valid",[],"NV8"],[[11124,11125],"disallowed"],[[11126,11157],"valid",[],"NV8"],[[11158,11159],"disallowed"],[[11160,11193],"valid",[],"NV8"],[[11194,11196],"disallowed"],[[11197,11208],"valid",[],"NV8"],[[11209,11209],"disallowed"],[[11210,11217],"valid",[],"NV8"],[[11218,11243],"disallowed"],[[11244,11247],"valid",[],"NV8"],[[11248,11263],"disallowed"],[[11264,11264],"mapped",[11312]],[[11265,11265],"mapped",[11313]],[[11266,11266],"mapped",[11314]],[[11267,11267],"mapped",[11315]],[[11268,11268],"mapped",[11316]],[[11269,11269],"mapped",[11317]],[[11270,11270],"mapped",[11318]],[[11271,11271],"mapped",[11319]],[[11272,11272],"mapped",[11320]],[[11273,11273],"mapped",[11321]],[[11274,11274],"mapped",[11322]],[[11275,11275],"mapped",[11323]],[[11276,11276],"mapped",[11324]],[[11277,11277],"mapped",[11325]],[[11278,11278],"mapped",[11326]],[[11279,11279],"mapped",[11327]],[[11280,11280],"mapped",[11328]],[[11281,11281],"mapped",[11329]],[[11282,11282],"mapped",[11330]],[[11283,11283],"mapped",[11331]],[[11284,11284],"mapped",[11332]],[[11285,11285],"mapped",[11333]],[[11286,11286],"mapped",[11334]],[[11287,11287],"mapped",[11335]],[[11288,11288],"mapped",[11336]],[[11289,11289],"mapped",[11337]],[[11290,11290],"mapped",[11338]],[[11291,11291],"mapped",[11339]],[[11292,11292],"mapped",[11340]],[[11293,11293],"mapped",[11341]],[[11294,11294],"mapped",[11342]],[[11295,11295],"mapped",[11343]],[[11296,11296],"mapped",[11344]],[[11297,11297],"mapped",[11345]],[[11298,11298],"mapped",[11346]],[[11299,11299],"mapped",[11347]],[[11300,11300],"mapped",[11348]],[[11301,11301],"mapped",[11349]],[[11302,11302],"mapped",[11350]],[[11303,11303],"mapped",[11351]],[[11304,11304],"mapped",[11352]],[[11305,11305],"mapped",[11353]],[[11306,11306],"mapped",[11354]],[[11307,11307],"mapped",[11355]],[[11308,11308],"mapped",[11356]],[[11309,11309],"mapped",[11357]],[[11310,11310],"mapped",[11358]],[[11311,11311],"disallowed"],[[11312,11358],"valid"],[[11359,11359],"disallowed"],[[11360,11360],"mapped",[11361]],[[11361,11361],"valid"],[[11362,11362],"mapped",[619]],[[11363,11363],"mapped",[7549]],[[11364,11364],"mapped",[637]],[[11365,11366],"valid"],[[11367,11367],"mapped",[11368]],[[11368,11368],"valid"],[[11369,11369],"mapped",[11370]],[[11370,11370],"valid"],[[11371,11371],"mapped",[11372]],[[11372,11372],"valid"],[[11373,11373],"mapped",[593]],[[11374,11374],"mapped",[625]],[[11375,11375],"mapped",[592]],[[11376,11376],"mapped",[594]],[[11377,11377],"valid"],[[11378,11378],"mapped",[11379]],[[11379,11379],"valid"],[[11380,11380],"valid"],[[11381,11381],"mapped",[11382]],[[11382,11383],"valid"],[[11384,11387],"valid"],[[11388,11388],"mapped",[106]],[[11389,11389],"mapped",[118]],[[11390,11390],"mapped",[575]],[[11391,11391],"mapped",[576]],[[11392,11392],"mapped",[11393]],[[11393,11393],"valid"],[[11394,11394],"mapped",[11395]],[[11395,11395],"valid"],[[11396,11396],"mapped",[11397]],[[11397,11397],"valid"],[[11398,11398],"mapped",[11399]],[[11399,11399],"valid"],[[11400,11400],"mapped",[11401]],[[11401,11401],"valid"],[[11402,11402],"mapped",[11403]],[[11403,11403],"valid"],[[11404,11404],"mapped",[11405]],[[11405,11405],"valid"],[[11406,11406],"mapped",[11407]],[[11407,11407],"valid"],[[11408,11408],"mapped",[11409]],[[11409,11409],"valid"],[[11410,11410],"mapped",[11411]],[[11411,11411],"valid"],[[11412,11412],"mapped",[11413]],[[11413,11413],"valid"],[[11414,11414],"mapped",[11415]],[[11415,11415],"valid"],[[11416,11416],"mapped",[11417]],[[11417,11417],"valid"],[[11418,11418],"mapped",[11419]],[[11419,11419],"valid"],[[11420,11420],"mapped",[11421]],[[11421,11421],"valid"],[[11422,11422],"mapped",[11423]],[[11423,11423],"valid"],[[11424,11424],"mapped",[11425]],[[11425,11425],"valid"],[[11426,11426],"mapped",[11427]],[[11427,11427],"valid"],[[11428,11428],"mapped",[11429]],[[11429,11429],"valid"],[[11430,11430],"mapped",[11431]],[[11431,11431],"valid"],[[11432,11432],"mapped",[11433]],[[11433,11433],"valid"],[[11434,11434],"mapped",[11435]],[[11435,11435],"valid"],[[11436,11436],"mapped",[11437]],[[11437,11437],"valid"],[[11438,11438],"mapped",[11439]],[[11439,11439],"valid"],[[11440,11440],"mapped",[11441]],[[11441,11441],"valid"],[[11442,11442],"mapped",[11443]],[[11443,11443],"valid"],[[11444,11444],"mapped",[11445]],[[11445,11445],"valid"],[[11446,11446],"mapped",[11447]],[[11447,11447],"valid"],[[11448,11448],"mapped",[11449]],[[11449,11449],"valid"],[[11450,11450],"mapped",[11451]],[[11451,11451],"valid"],[[11452,11452],"mapped",[11453]],[[11453,11453],"valid"],[[11454,11454],"mapped",[11455]],[[11455,11455],"valid"],[[11456,11456],"mapped",[11457]],[[11457,11457],"valid"],[[11458,11458],"mapped",[11459]],[[11459,11459],"valid"],[[11460,11460],"mapped",[11461]],[[11461,11461],"valid"],[[11462,11462],"mapped",[11463]],[[11463,11463],"valid"],[[11464,11464],"mapped",[11465]],[[11465,11465],"valid"],[[11466,11466],"mapped",[11467]],[[11467,11467],"valid"],[[11468,11468],"mapped",[11469]],[[11469,11469],"valid"],[[11470,11470],"mapped",[11471]],[[11471,11471],"valid"],[[11472,11472],"mapped",[11473]],[[11473,11473],"valid"],[[11474,11474],"mapped",[11475]],[[11475,11475],"valid"],[[11476,11476],"mapped",[11477]],[[11477,11477],"valid"],[[11478,11478],"mapped",[11479]],[[11479,11479],"valid"],[[11480,11480],"mapped",[11481]],[[11481,11481],"valid"],[[11482,11482],"mapped",[11483]],[[11483,11483],"valid"],[[11484,11484],"mapped",[11485]],[[11485,11485],"valid"],[[11486,11486],"mapped",[11487]],[[11487,11487],"valid"],[[11488,11488],"mapped",[11489]],[[11489,11489],"valid"],[[11490,11490],"mapped",[11491]],[[11491,11492],"valid"],[[11493,11498],"valid",[],"NV8"],[[11499,11499],"mapped",[11500]],[[11500,11500],"valid"],[[11501,11501],"mapped",[11502]],[[11502,11505],"valid"],[[11506,11506],"mapped",[11507]],[[11507,11507],"valid"],[[11508,11512],"disallowed"],[[11513,11519],"valid",[],"NV8"],[[11520,11557],"valid"],[[11558,11558],"disallowed"],[[11559,11559],"valid"],[[11560,11564],"disallowed"],[[11565,11565],"valid"],[[11566,11567],"disallowed"],[[11568,11621],"valid"],[[11622,11623],"valid"],[[11624,11630],"disallowed"],[[11631,11631],"mapped",[11617]],[[11632,11632],"valid",[],"NV8"],[[11633,11646],"disallowed"],[[11647,11647],"valid"],[[11648,11670],"valid"],[[11671,11679],"disallowed"],[[11680,11686],"valid"],[[11687,11687],"disallowed"],[[11688,11694],"valid"],[[11695,11695],"disallowed"],[[11696,11702],"valid"],[[11703,11703],"disallowed"],[[11704,11710],"valid"],[[11711,11711],"disallowed"],[[11712,11718],"valid"],[[11719,11719],"disallowed"],[[11720,11726],"valid"],[[11727,11727],"disallowed"],[[11728,11734],"valid"],[[11735,11735],"disallowed"],[[11736,11742],"valid"],[[11743,11743],"disallowed"],[[11744,11775],"valid"],[[11776,11799],"valid",[],"NV8"],[[11800,11803],"valid",[],"NV8"],[[11804,11805],"valid",[],"NV8"],[[11806,11822],"valid",[],"NV8"],[[11823,11823],"valid"],[[11824,11824],"valid",[],"NV8"],[[11825,11825],"valid",[],"NV8"],[[11826,11835],"valid",[],"NV8"],[[11836,11842],"valid",[],"NV8"],[[11843,11903],"disallowed"],[[11904,11929],"valid",[],"NV8"],[[11930,11930],"disallowed"],[[11931,11934],"valid",[],"NV8"],[[11935,11935],"mapped",[27597]],[[11936,12018],"valid",[],"NV8"],[[12019,12019],"mapped",[40863]],[[12020,12031],"disallowed"],[[12032,12032],"mapped",[19968]],[[12033,12033],"mapped",[20008]],[[12034,12034],"mapped",[20022]],[[12035,12035],"mapped",[20031]],[[12036,12036],"mapped",[20057]],[[12037,12037],"mapped",[20101]],[[12038,12038],"mapped",[20108]],[[12039,12039],"mapped",[20128]],[[12040,12040],"mapped",[20154]],[[12041,12041],"mapped",[20799]],[[12042,12042],"mapped",[20837]],[[12043,12043],"mapped",[20843]],[[12044,12044],"mapped",[20866]],[[12045,12045],"mapped",[20886]],[[12046,12046],"mapped",[20907]],[[12047,12047],"mapped",[20960]],[[12048,12048],"mapped",[20981]],[[12049,12049],"mapped",[20992]],[[12050,12050],"mapped",[21147]],[[12051,12051],"mapped",[21241]],[[12052,12052],"mapped",[21269]],[[12053,12053],"mapped",[21274]],[[12054,12054],"mapped",[21304]],[[12055,12055],"mapped",[21313]],[[12056,12056],"mapped",[21340]],[[12057,12057],"mapped",[21353]],[[12058,12058],"mapped",[21378]],[[12059,12059],"mapped",[21430]],[[12060,12060],"mapped",[21448]],[[12061,12061],"mapped",[21475]],[[12062,12062],"mapped",[22231]],[[12063,12063],"mapped",[22303]],[[12064,12064],"mapped",[22763]],[[12065,12065],"mapped",[22786]],[[12066,12066],"mapped",[22794]],[[12067,12067],"mapped",[22805]],[[12068,12068],"mapped",[22823]],[[12069,12069],"mapped",[22899]],[[12070,12070],"mapped",[23376]],[[12071,12071],"mapped",[23424]],[[12072,12072],"mapped",[23544]],[[12073,12073],"mapped",[23567]],[[12074,12074],"mapped",[23586]],[[12075,12075],"mapped",[23608]],[[12076,12076],"mapped",[23662]],[[12077,12077],"mapped",[23665]],[[12078,12078],"mapped",[24027]],[[12079,12079],"mapped",[24037]],[[12080,12080],"mapped",[24049]],[[12081,12081],"mapped",[24062]],[[12082,12082],"mapped",[24178]],[[12083,12083],"mapped",[24186]],[[12084,12084],"mapped",[24191]],[[12085,12085],"mapped",[24308]],[[12086,12086],"mapped",[24318]],[[12087,12087],"mapped",[24331]],[[12088,12088],"mapped",[24339]],[[12089,12089],"mapped",[24400]],[[12090,12090],"mapped",[24417]],[[12091,12091],"mapped",[24435]],[[12092,12092],"mapped",[24515]],[[12093,12093],"mapped",[25096]],[[12094,12094],"mapped",[25142]],[[12095,12095],"mapped",[25163]],[[12096,12096],"mapped",[25903]],[[12097,12097],"mapped",[25908]],[[12098,12098],"mapped",[25991]],[[12099,12099],"mapped",[26007]],[[12100,12100],"mapped",[26020]],[[12101,12101],"mapped",[26041]],[[12102,12102],"mapped",[26080]],[[12103,12103],"mapped",[26085]],[[12104,12104],"mapped",[26352]],[[12105,12105],"mapped",[26376]],[[12106,12106],"mapped",[26408]],[[12107,12107],"mapped",[27424]],[[12108,12108],"mapped",[27490]],[[12109,12109],"mapped",[27513]],[[12110,12110],"mapped",[27571]],[[12111,12111],"mapped",[27595]],[[12112,12112],"mapped",[27604]],[[12113,12113],"mapped",[27611]],[[12114,12114],"mapped",[27663]],[[12115,12115],"mapped",[27668]],[[12116,12116],"mapped",[27700]],[[12117,12117],"mapped",[28779]],[[12118,12118],"mapped",[29226]],[[12119,12119],"mapped",[29238]],[[12120,12120],"mapped",[29243]],[[12121,12121],"mapped",[29247]],[[12122,12122],"mapped",[29255]],[[12123,12123],"mapped",[29273]],[[12124,12124],"mapped",[29275]],[[12125,12125],"mapped",[29356]],[[12126,12126],"mapped",[29572]],[[12127,12127],"mapped",[29577]],[[12128,12128],"mapped",[29916]],[[12129,12129],"mapped",[29926]],[[12130,12130],"mapped",[29976]],[[12131,12131],"mapped",[29983]],[[12132,12132],"mapped",[29992]],[[12133,12133],"mapped",[30000]],[[12134,12134],"mapped",[30091]],[[12135,12135],"mapped",[30098]],[[12136,12136],"mapped",[30326]],[[12137,12137],"mapped",[30333]],[[12138,12138],"mapped",[30382]],[[12139,12139],"mapped",[30399]],[[12140,12140],"mapped",[30446]],[[12141,12141],"mapped",[30683]],[[12142,12142],"mapped",[30690]],[[12143,12143],"mapped",[30707]],[[12144,12144],"mapped",[31034]],[[12145,12145],"mapped",[31160]],[[12146,12146],"mapped",[31166]],[[12147,12147],"mapped",[31348]],[[12148,12148],"mapped",[31435]],[[12149,12149],"mapped",[31481]],[[12150,12150],"mapped",[31859]],[[12151,12151],"mapped",[31992]],[[12152,12152],"mapped",[32566]],[[12153,12153],"mapped",[32593]],[[12154,12154],"mapped",[32650]],[[12155,12155],"mapped",[32701]],[[12156,12156],"mapped",[32769]],[[12157,12157],"mapped",[32780]],[[12158,12158],"mapped",[32786]],[[12159,12159],"mapped",[32819]],[[12160,12160],"mapped",[32895]],[[12161,12161],"mapped",[32905]],[[12162,12162],"mapped",[33251]],[[12163,12163],"mapped",[33258]],[[12164,12164],"mapped",[33267]],[[12165,12165],"mapped",[33276]],[[12166,12166],"mapped",[33292]],[[12167,12167],"mapped",[33307]],[[12168,12168],"mapped",[33311]],[[12169,12169],"mapped",[33390]],[[12170,12170],"mapped",[33394]],[[12171,12171],"mapped",[33400]],[[12172,12172],"mapped",[34381]],[[12173,12173],"mapped",[34411]],[[12174,12174],"mapped",[34880]],[[12175,12175],"mapped",[34892]],[[12176,12176],"mapped",[34915]],[[12177,12177],"mapped",[35198]],[[12178,12178],"mapped",[35211]],[[12179,12179],"mapped",[35282]],[[12180,12180],"mapped",[35328]],[[12181,12181],"mapped",[35895]],[[12182,12182],"mapped",[35910]],[[12183,12183],"mapped",[35925]],[[12184,12184],"mapped",[35960]],[[12185,12185],"mapped",[35997]],[[12186,12186],"mapped",[36196]],[[12187,12187],"mapped",[36208]],[[12188,12188],"mapped",[36275]],[[12189,12189],"mapped",[36523]],[[12190,12190],"mapped",[36554]],[[12191,12191],"mapped",[36763]],[[12192,12192],"mapped",[36784]],[[12193,12193],"mapped",[36789]],[[12194,12194],"mapped",[37009]],[[12195,12195],"mapped",[37193]],[[12196,12196],"mapped",[37318]],[[12197,12197],"mapped",[37324]],[[12198,12198],"mapped",[37329]],[[12199,12199],"mapped",[38263]],[[12200,12200],"mapped",[38272]],[[12201,12201],"mapped",[38428]],[[12202,12202],"mapped",[38582]],[[12203,12203],"mapped",[38585]],[[12204,12204],"mapped",[38632]],[[12205,12205],"mapped",[38737]],[[12206,12206],"mapped",[38750]],[[12207,12207],"mapped",[38754]],[[12208,12208],"mapped",[38761]],[[12209,12209],"mapped",[38859]],[[12210,12210],"mapped",[38893]],[[12211,12211],"mapped",[38899]],[[12212,12212],"mapped",[38913]],[[12213,12213],"mapped",[39080]],[[12214,12214],"mapped",[39131]],[[12215,12215],"mapped",[39135]],[[12216,12216],"mapped",[39318]],[[12217,12217],"mapped",[39321]],[[12218,12218],"mapped",[39340]],[[12219,12219],"mapped",[39592]],[[12220,12220],"mapped",[39640]],[[12221,12221],"mapped",[39647]],[[12222,12222],"mapped",[39717]],[[12223,12223],"mapped",[39727]],[[12224,12224],"mapped",[39730]],[[12225,12225],"mapped",[39740]],[[12226,12226],"mapped",[39770]],[[12227,12227],"mapped",[40165]],[[12228,12228],"mapped",[40565]],[[12229,12229],"mapped",[40575]],[[12230,12230],"mapped",[40613]],[[12231,12231],"mapped",[40635]],[[12232,12232],"mapped",[40643]],[[12233,12233],"mapped",[40653]],[[12234,12234],"mapped",[40657]],[[12235,12235],"mapped",[40697]],[[12236,12236],"mapped",[40701]],[[12237,12237],"mapped",[40718]],[[12238,12238],"mapped",[40723]],[[12239,12239],"mapped",[40736]],[[12240,12240],"mapped",[40763]],[[12241,12241],"mapped",[40778]],[[12242,12242],"mapped",[40786]],[[12243,12243],"mapped",[40845]],[[12244,12244],"mapped",[40860]],[[12245,12245],"mapped",[40864]],[[12246,12271],"disallowed"],[[12272,12283],"disallowed"],[[12284,12287],"disallowed"],[[12288,12288],"disallowed_STD3_mapped",[32]],[[12289,12289],"valid",[],"NV8"],[[12290,12290],"mapped",[46]],[[12291,12292],"valid",[],"NV8"],[[12293,12295],"valid"],[[12296,12329],"valid",[],"NV8"],[[12330,12333],"valid"],[[12334,12341],"valid",[],"NV8"],[[12342,12342],"mapped",[12306]],[[12343,12343],"valid",[],"NV8"],[[12344,12344],"mapped",[21313]],[[12345,12345],"mapped",[21316]],[[12346,12346],"mapped",[21317]],[[12347,12347],"valid",[],"NV8"],[[12348,12348],"valid"],[[12349,12349],"valid",[],"NV8"],[[12350,12350],"valid",[],"NV8"],[[12351,12351],"valid",[],"NV8"],[[12352,12352],"disallowed"],[[12353,12436],"valid"],[[12437,12438],"valid"],[[12439,12440],"disallowed"],[[12441,12442],"valid"],[[12443,12443],"disallowed_STD3_mapped",[32,12441]],[[12444,12444],"disallowed_STD3_mapped",[32,12442]],[[12445,12446],"valid"],[[12447,12447],"mapped",[12424,12426]],[[12448,12448],"valid",[],"NV8"],[[12449,12542],"valid"],[[12543,12543],"mapped",[12467,12488]],[[12544,12548],"disallowed"],[[12549,12588],"valid"],[[12589,12589],"valid"],[[12590,12592],"disallowed"],[[12593,12593],"mapped",[4352]],[[12594,12594],"mapped",[4353]],[[12595,12595],"mapped",[4522]],[[12596,12596],"mapped",[4354]],[[12597,12597],"mapped",[4524]],[[12598,12598],"mapped",[4525]],[[12599,12599],"mapped",[4355]],[[12600,12600],"mapped",[4356]],[[12601,12601],"mapped",[4357]],[[12602,12602],"mapped",[4528]],[[12603,12603],"mapped",[4529]],[[12604,12604],"mapped",[4530]],[[12605,12605],"mapped",[4531]],[[12606,12606],"mapped",[4532]],[[12607,12607],"mapped",[4533]],[[12608,12608],"mapped",[4378]],[[12609,12609],"mapped",[4358]],[[12610,12610],"mapped",[4359]],[[12611,12611],"mapped",[4360]],[[12612,12612],"mapped",[4385]],[[12613,12613],"mapped",[4361]],[[12614,12614],"mapped",[4362]],[[12615,12615],"mapped",[4363]],[[12616,12616],"mapped",[4364]],[[12617,12617],"mapped",[4365]],[[12618,12618],"mapped",[4366]],[[12619,12619],"mapped",[4367]],[[12620,12620],"mapped",[4368]],[[12621,12621],"mapped",[4369]],[[12622,12622],"mapped",[4370]],[[12623,12623],"mapped",[4449]],[[12624,12624],"mapped",[4450]],[[12625,12625],"mapped",[4451]],[[12626,12626],"mapped",[4452]],[[12627,12627],"mapped",[4453]],[[12628,12628],"mapped",[4454]],[[12629,12629],"mapped",[4455]],[[12630,12630],"mapped",[4456]],[[12631,12631],"mapped",[4457]],[[12632,12632],"mapped",[4458]],[[12633,12633],"mapped",[4459]],[[12634,12634],"mapped",[4460]],[[12635,12635],"mapped",[4461]],[[12636,12636],"mapped",[4462]],[[12637,12637],"mapped",[4463]],[[12638,12638],"mapped",[4464]],[[12639,12639],"mapped",[4465]],[[12640,12640],"mapped",[4466]],[[12641,12641],"mapped",[4467]],[[12642,12642],"mapped",[4468]],[[12643,12643],"mapped",[4469]],[[12644,12644],"disallowed"],[[12645,12645],"mapped",[4372]],[[12646,12646],"mapped",[4373]],[[12647,12647],"mapped",[4551]],[[12648,12648],"mapped",[4552]],[[12649,12649],"mapped",[4556]],[[12650,12650],"mapped",[4558]],[[12651,12651],"mapped",[4563]],[[12652,12652],"mapped",[4567]],[[12653,12653],"mapped",[4569]],[[12654,12654],"mapped",[4380]],[[12655,12655],"mapped",[4573]],[[12656,12656],"mapped",[4575]],[[12657,12657],"mapped",[4381]],[[12658,12658],"mapped",[4382]],[[12659,12659],"mapped",[4384]],[[12660,12660],"mapped",[4386]],[[12661,12661],"mapped",[4387]],[[12662,12662],"mapped",[4391]],[[12663,12663],"mapped",[4393]],[[12664,12664],"mapped",[4395]],[[12665,12665],"mapped",[4396]],[[12666,12666],"mapped",[4397]],[[12667,12667],"mapped",[4398]],[[12668,12668],"mapped",[4399]],[[12669,12669],"mapped",[4402]],[[12670,12670],"mapped",[4406]],[[12671,12671],"mapped",[4416]],[[12672,12672],"mapped",[4423]],[[12673,12673],"mapped",[4428]],[[12674,12674],"mapped",[4593]],[[12675,12675],"mapped",[4594]],[[12676,12676],"mapped",[4439]],[[12677,12677],"mapped",[4440]],[[12678,12678],"mapped",[4441]],[[12679,12679],"mapped",[4484]],[[12680,12680],"mapped",[4485]],[[12681,12681],"mapped",[4488]],[[12682,12682],"mapped",[4497]],[[12683,12683],"mapped",[4498]],[[12684,12684],"mapped",[4500]],[[12685,12685],"mapped",[4510]],[[12686,12686],"mapped",[4513]],[[12687,12687],"disallowed"],[[12688,12689],"valid",[],"NV8"],[[12690,12690],"mapped",[19968]],[[12691,12691],"mapped",[20108]],[[12692,12692],"mapped",[19977]],[[12693,12693],"mapped",[22235]],[[12694,12694],"mapped",[19978]],[[12695,12695],"mapped",[20013]],[[12696,12696],"mapped",[19979]],[[12697,12697],"mapped",[30002]],[[12698,12698],"mapped",[20057]],[[12699,12699],"mapped",[19993]],[[12700,12700],"mapped",[19969]],[[12701,12701],"mapped",[22825]],[[12702,12702],"mapped",[22320]],[[12703,12703],"mapped",[20154]],[[12704,12727],"valid"],[[12728,12730],"valid"],[[12731,12735],"disallowed"],[[12736,12751],"valid",[],"NV8"],[[12752,12771],"valid",[],"NV8"],[[12772,12783],"disallowed"],[[12784,12799],"valid"],[[12800,12800],"disallowed_STD3_mapped",[40,4352,41]],[[12801,12801],"disallowed_STD3_mapped",[40,4354,41]],[[12802,12802],"disallowed_STD3_mapped",[40,4355,41]],[[12803,12803],"disallowed_STD3_mapped",[40,4357,41]],[[12804,12804],"disallowed_STD3_mapped",[40,4358,41]],[[12805,12805],"disallowed_STD3_mapped",[40,4359,41]],[[12806,12806],"disallowed_STD3_mapped",[40,4361,41]],[[12807,12807],"disallowed_STD3_mapped",[40,4363,41]],[[12808,12808],"disallowed_STD3_mapped",[40,4364,41]],[[12809,12809],"disallowed_STD3_mapped",[40,4366,41]],[[12810,12810],"disallowed_STD3_mapped",[40,4367,41]],[[12811,12811],"disallowed_STD3_mapped",[40,4368,41]],[[12812,12812],"disallowed_STD3_mapped",[40,4369,41]],[[12813,12813],"disallowed_STD3_mapped",[40,4370,41]],[[12814,12814],"disallowed_STD3_mapped",[40,44032,41]],[[12815,12815],"disallowed_STD3_mapped",[40,45208,41]],[[12816,12816],"disallowed_STD3_mapped",[40,45796,41]],[[12817,12817],"disallowed_STD3_mapped",[40,46972,41]],[[12818,12818],"disallowed_STD3_mapped",[40,47560,41]],[[12819,12819],"disallowed_STD3_mapped",[40,48148,41]],[[12820,12820],"disallowed_STD3_mapped",[40,49324,41]],[[12821,12821],"disallowed_STD3_mapped",[40,50500,41]],[[12822,12822],"disallowed_STD3_mapped",[40,51088,41]],[[12823,12823],"disallowed_STD3_mapped",[40,52264,41]],[[12824,12824],"disallowed_STD3_mapped",[40,52852,41]],[[12825,12825],"disallowed_STD3_mapped",[40,53440,41]],[[12826,12826],"disallowed_STD3_mapped",[40,54028,41]],[[12827,12827],"disallowed_STD3_mapped",[40,54616,41]],[[12828,12828],"disallowed_STD3_mapped",[40,51452,41]],[[12829,12829],"disallowed_STD3_mapped",[40,50724,51204,41]],[[12830,12830],"disallowed_STD3_mapped",[40,50724,54980,41]],[[12831,12831],"disallowed"],[[12832,12832],"disallowed_STD3_mapped",[40,19968,41]],[[12833,12833],"disallowed_STD3_mapped",[40,20108,41]],[[12834,12834],"disallowed_STD3_mapped",[40,19977,41]],[[12835,12835],"disallowed_STD3_mapped",[40,22235,41]],[[12836,12836],"disallowed_STD3_mapped",[40,20116,41]],[[12837,12837],"disallowed_STD3_mapped",[40,20845,41]],[[12838,12838],"disallowed_STD3_mapped",[40,19971,41]],[[12839,12839],"disallowed_STD3_mapped",[40,20843,41]],[[12840,12840],"disallowed_STD3_mapped",[40,20061,41]],[[12841,12841],"disallowed_STD3_mapped",[40,21313,41]],[[12842,12842],"disallowed_STD3_mapped",[40,26376,41]],[[12843,12843],"disallowed_STD3_mapped",[40,28779,41]],[[12844,12844],"disallowed_STD3_mapped",[40,27700,41]],[[12845,12845],"disallowed_STD3_mapped",[40,26408,41]],[[12846,12846],"disallowed_STD3_mapped",[40,37329,41]],[[12847,12847],"disallowed_STD3_mapped",[40,22303,41]],[[12848,12848],"disallowed_STD3_mapped",[40,26085,41]],[[12849,12849],"disallowed_STD3_mapped",[40,26666,41]],[[12850,12850],"disallowed_STD3_mapped",[40,26377,41]],[[12851,12851],"disallowed_STD3_mapped",[40,31038,41]],[[12852,12852],"disallowed_STD3_mapped",[40,21517,41]],[[12853,12853],"disallowed_STD3_mapped",[40,29305,41]],[[12854,12854],"disallowed_STD3_mapped",[40,36001,41]],[[12855,12855],"disallowed_STD3_mapped",[40,31069,41]],[[12856,12856],"disallowed_STD3_mapped",[40,21172,41]],[[12857,12857],"disallowed_STD3_mapped",[40,20195,41]],[[12858,12858],"disallowed_STD3_mapped",[40,21628,41]],[[12859,12859],"disallowed_STD3_mapped",[40,23398,41]],[[12860,12860],"disallowed_STD3_mapped",[40,30435,41]],[[12861,12861],"disallowed_STD3_mapped",[40,20225,41]],[[12862,12862],"disallowed_STD3_mapped",[40,36039,41]],[[12863,12863],"disallowed_STD3_mapped",[40,21332,41]],[[12864,12864],"disallowed_STD3_mapped",[40,31085,41]],[[12865,12865],"disallowed_STD3_mapped",[40,20241,41]],[[12866,12866],"disallowed_STD3_mapped",[40,33258,41]],[[12867,12867],"disallowed_STD3_mapped",[40,33267,41]],[[12868,12868],"mapped",[21839]],[[12869,12869],"mapped",[24188]],[[12870,12870],"mapped",[25991]],[[12871,12871],"mapped",[31631]],[[12872,12879],"valid",[],"NV8"],[[12880,12880],"mapped",[112,116,101]],[[12881,12881],"mapped",[50,49]],[[12882,12882],"mapped",[50,50]],[[12883,12883],"mapped",[50,51]],[[12884,12884],"mapped",[50,52]],[[12885,12885],"mapped",[50,53]],[[12886,12886],"mapped",[50,54]],[[12887,12887],"mapped",[50,55]],[[12888,12888],"mapped",[50,56]],[[12889,12889],"mapped",[50,57]],[[12890,12890],"mapped",[51,48]],[[12891,12891],"mapped",[51,49]],[[12892,12892],"mapped",[51,50]],[[12893,12893],"mapped",[51,51]],[[12894,12894],"mapped",[51,52]],[[12895,12895],"mapped",[51,53]],[[12896,12896],"mapped",[4352]],[[12897,12897],"mapped",[4354]],[[12898,12898],"mapped",[4355]],[[12899,12899],"mapped",[4357]],[[12900,12900],"mapped",[4358]],[[12901,12901],"mapped",[4359]],[[12902,12902],"mapped",[4361]],[[12903,12903],"mapped",[4363]],[[12904,12904],"mapped",[4364]],[[12905,12905],"mapped",[4366]],[[12906,12906],"mapped",[4367]],[[12907,12907],"mapped",[4368]],[[12908,12908],"mapped",[4369]],[[12909,12909],"mapped",[4370]],[[12910,12910],"mapped",[44032]],[[12911,12911],"mapped",[45208]],[[12912,12912],"mapped",[45796]],[[12913,12913],"mapped",[46972]],[[12914,12914],"mapped",[47560]],[[12915,12915],"mapped",[48148]],[[12916,12916],"mapped",[49324]],[[12917,12917],"mapped",[50500]],[[12918,12918],"mapped",[51088]],[[12919,12919],"mapped",[52264]],[[12920,12920],"mapped",[52852]],[[12921,12921],"mapped",[53440]],[[12922,12922],"mapped",[54028]],[[12923,12923],"mapped",[54616]],[[12924,12924],"mapped",[52280,44256]],[[12925,12925],"mapped",[51452,51032]],[[12926,12926],"mapped",[50864]],[[12927,12927],"valid",[],"NV8"],[[12928,12928],"mapped",[19968]],[[12929,12929],"mapped",[20108]],[[12930,12930],"mapped",[19977]],[[12931,12931],"mapped",[22235]],[[12932,12932],"mapped",[20116]],[[12933,12933],"mapped",[20845]],[[12934,12934],"mapped",[19971]],[[12935,12935],"mapped",[20843]],[[12936,12936],"mapped",[20061]],[[12937,12937],"mapped",[21313]],[[12938,12938],"mapped",[26376]],[[12939,12939],"mapped",[28779]],[[12940,12940],"mapped",[27700]],[[12941,12941],"mapped",[26408]],[[12942,12942],"mapped",[37329]],[[12943,12943],"mapped",[22303]],[[12944,12944],"mapped",[26085]],[[12945,12945],"mapped",[26666]],[[12946,12946],"mapped",[26377]],[[12947,12947],"mapped",[31038]],[[12948,12948],"mapped",[21517]],[[12949,12949],"mapped",[29305]],[[12950,12950],"mapped",[36001]],[[12951,12951],"mapped",[31069]],[[12952,12952],"mapped",[21172]],[[12953,12953],"mapped",[31192]],[[12954,12954],"mapped",[30007]],[[12955,12955],"mapped",[22899]],[[12956,12956],"mapped",[36969]],[[12957,12957],"mapped",[20778]],[[12958,12958],"mapped",[21360]],[[12959,12959],"mapped",[27880]],[[12960,12960],"mapped",[38917]],[[12961,12961],"mapped",[20241]],[[12962,12962],"mapped",[20889]],[[12963,12963],"mapped",[27491]],[[12964,12964],"mapped",[19978]],[[12965,12965],"mapped",[20013]],[[12966,12966],"mapped",[19979]],[[12967,12967],"mapped",[24038]],[[12968,12968],"mapped",[21491]],[[12969,12969],"mapped",[21307]],[[12970,12970],"mapped",[23447]],[[12971,12971],"mapped",[23398]],[[12972,12972],"mapped",[30435]],[[12973,12973],"mapped",[20225]],[[12974,12974],"mapped",[36039]],[[12975,12975],"mapped",[21332]],[[12976,12976],"mapped",[22812]],[[12977,12977],"mapped",[51,54]],[[12978,12978],"mapped",[51,55]],[[12979,12979],"mapped",[51,56]],[[12980,12980],"mapped",[51,57]],[[12981,12981],"mapped",[52,48]],[[12982,12982],"mapped",[52,49]],[[12983,12983],"mapped",[52,50]],[[12984,12984],"mapped",[52,51]],[[12985,12985],"mapped",[52,52]],[[12986,12986],"mapped",[52,53]],[[12987,12987],"mapped",[52,54]],[[12988,12988],"mapped",[52,55]],[[12989,12989],"mapped",[52,56]],[[12990,12990],"mapped",[52,57]],[[12991,12991],"mapped",[53,48]],[[12992,12992],"mapped",[49,26376]],[[12993,12993],"mapped",[50,26376]],[[12994,12994],"mapped",[51,26376]],[[12995,12995],"mapped",[52,26376]],[[12996,12996],"mapped",[53,26376]],[[12997,12997],"mapped",[54,26376]],[[12998,12998],"mapped",[55,26376]],[[12999,12999],"mapped",[56,26376]],[[13000,13000],"mapped",[57,26376]],[[13001,13001],"mapped",[49,48,26376]],[[13002,13002],"mapped",[49,49,26376]],[[13003,13003],"mapped",[49,50,26376]],[[13004,13004],"mapped",[104,103]],[[13005,13005],"mapped",[101,114,103]],[[13006,13006],"mapped",[101,118]],[[13007,13007],"mapped",[108,116,100]],[[13008,13008],"mapped",[12450]],[[13009,13009],"mapped",[12452]],[[13010,13010],"mapped",[12454]],[[13011,13011],"mapped",[12456]],[[13012,13012],"mapped",[12458]],[[13013,13013],"mapped",[12459]],[[13014,13014],"mapped",[12461]],[[13015,13015],"mapped",[12463]],[[13016,13016],"mapped",[12465]],[[13017,13017],"mapped",[12467]],[[13018,13018],"mapped",[12469]],[[13019,13019],"mapped",[12471]],[[13020,13020],"mapped",[12473]],[[13021,13021],"mapped",[12475]],[[13022,13022],"mapped",[12477]],[[13023,13023],"mapped",[12479]],[[13024,13024],"mapped",[12481]],[[13025,13025],"mapped",[12484]],[[13026,13026],"mapped",[12486]],[[13027,13027],"mapped",[12488]],[[13028,13028],"mapped",[12490]],[[13029,13029],"mapped",[12491]],[[13030,13030],"mapped",[12492]],[[13031,13031],"mapped",[12493]],[[13032,13032],"mapped",[12494]],[[13033,13033],"mapped",[12495]],[[13034,13034],"mapped",[12498]],[[13035,13035],"mapped",[12501]],[[13036,13036],"mapped",[12504]],[[13037,13037],"mapped",[12507]],[[13038,13038],"mapped",[12510]],[[13039,13039],"mapped",[12511]],[[13040,13040],"mapped",[12512]],[[13041,13041],"mapped",[12513]],[[13042,13042],"mapped",[12514]],[[13043,13043],"mapped",[12516]],[[13044,13044],"mapped",[12518]],[[13045,13045],"mapped",[12520]],[[13046,13046],"mapped",[12521]],[[13047,13047],"mapped",[12522]],[[13048,13048],"mapped",[12523]],[[13049,13049],"mapped",[12524]],[[13050,13050],"mapped",[12525]],[[13051,13051],"mapped",[12527]],[[13052,13052],"mapped",[12528]],[[13053,13053],"mapped",[12529]],[[13054,13054],"mapped",[12530]],[[13055,13055],"disallowed"],[[13056,13056],"mapped",[12450,12497,12540,12488]],[[13057,13057],"mapped",[12450,12523,12501,12449]],[[13058,13058],"mapped",[12450,12531,12506,12450]],[[13059,13059],"mapped",[12450,12540,12523]],[[13060,13060],"mapped",[12452,12491,12531,12464]],[[13061,13061],"mapped",[12452,12531,12481]],[[13062,13062],"mapped",[12454,12457,12531]],[[13063,13063],"mapped",[12456,12473,12463,12540,12489]],[[13064,13064],"mapped",[12456,12540,12459,12540]],[[13065,13065],"mapped",[12458,12531,12473]],[[13066,13066],"mapped",[12458,12540,12512]],[[13067,13067],"mapped",[12459,12452,12522]],[[13068,13068],"mapped",[12459,12521,12483,12488]],[[13069,13069],"mapped",[12459,12525,12522,12540]],[[13070,13070],"mapped",[12460,12525,12531]],[[13071,13071],"mapped",[12460,12531,12510]],[[13072,13072],"mapped",[12462,12460]],[[13073,13073],"mapped",[12462,12491,12540]],[[13074,13074],"mapped",[12461,12517,12522,12540]],[[13075,13075],"mapped",[12462,12523,12480,12540]],[[13076,13076],"mapped",[12461,12525]],[[13077,13077],"mapped",[12461,12525,12464,12521,12512]],[[13078,13078],"mapped",[12461,12525,12513,12540,12488,12523]],[[13079,13079],"mapped",[12461,12525,12527,12483,12488]],[[13080,13080],"mapped",[12464,12521,12512]],[[13081,13081],"mapped",[12464,12521,12512,12488,12531]],[[13082,13082],"mapped",[12463,12523,12476,12452,12525]],[[13083,13083],"mapped",[12463,12525,12540,12493]],[[13084,13084],"mapped",[12465,12540,12473]],[[13085,13085],"mapped",[12467,12523,12490]],[[13086,13086],"mapped",[12467,12540,12509]],[[13087,13087],"mapped",[12469,12452,12463,12523]],[[13088,13088],"mapped",[12469,12531,12481,12540,12512]],[[13089,13089],"mapped",[12471,12522,12531,12464]],[[13090,13090],"mapped",[12475,12531,12481]],[[13091,13091],"mapped",[12475,12531,12488]],[[13092,13092],"mapped",[12480,12540,12473]],[[13093,13093],"mapped",[12487,12471]],[[13094,13094],"mapped",[12489,12523]],[[13095,13095],"mapped",[12488,12531]],[[13096,13096],"mapped",[12490,12494]],[[13097,13097],"mapped",[12494,12483,12488]],[[13098,13098],"mapped",[12495,12452,12484]],[[13099,13099],"mapped",[12497,12540,12475,12531,12488]],[[13100,13100],"mapped",[12497,12540,12484]],[[13101,13101],"mapped",[12496,12540,12524,12523]],[[13102,13102],"mapped",[12500,12450,12473,12488,12523]],[[13103,13103],"mapped",[12500,12463,12523]],[[13104,13104],"mapped",[12500,12467]],[[13105,13105],"mapped",[12499,12523]],[[13106,13106],"mapped",[12501,12449,12521,12483,12489]],[[13107,13107],"mapped",[12501,12451,12540,12488]],[[13108,13108],"mapped",[12502,12483,12471,12455,12523]],[[13109,13109],"mapped",[12501,12521,12531]],[[13110,13110],"mapped",[12504,12463,12479,12540,12523]],[[13111,13111],"mapped",[12506,12477]],[[13112,13112],"mapped",[12506,12491,12498]],[[13113,13113],"mapped",[12504,12523,12484]],[[13114,13114],"mapped",[12506,12531,12473]],[[13115,13115],"mapped",[12506,12540,12472]],[[13116,13116],"mapped",[12505,12540,12479]],[[13117,13117],"mapped",[12509,12452,12531,12488]],[[13118,13118],"mapped",[12508,12523,12488]],[[13119,13119],"mapped",[12507,12531]],[[13120,13120],"mapped",[12509,12531,12489]],[[13121,13121],"mapped",[12507,12540,12523]],[[13122,13122],"mapped",[12507,12540,12531]],[[13123,13123],"mapped",[12510,12452,12463,12525]],[[13124,13124],"mapped",[12510,12452,12523]],[[13125,13125],"mapped",[12510,12483,12495]],[[13126,13126],"mapped",[12510,12523,12463]],[[13127,13127],"mapped",[12510,12531,12471,12519,12531]],[[13128,13128],"mapped",[12511,12463,12525,12531]],[[13129,13129],"mapped",[12511,12522]],[[13130,13130],"mapped",[12511,12522,12496,12540,12523]],[[13131,13131],"mapped",[12513,12460]],[[13132,13132],"mapped",[12513,12460,12488,12531]],[[13133,13133],"mapped",[12513,12540,12488,12523]],[[13134,13134],"mapped",[12516,12540,12489]],[[13135,13135],"mapped",[12516,12540,12523]],[[13136,13136],"mapped",[12518,12450,12531]],[[13137,13137],"mapped",[12522,12483,12488,12523]],[[13138,13138],"mapped",[12522,12521]],[[13139,13139],"mapped",[12523,12500,12540]],[[13140,13140],"mapped",[12523,12540,12502,12523]],[[13141,13141],"mapped",[12524,12512]],[[13142,13142],"mapped",[12524,12531,12488,12466,12531]],[[13143,13143],"mapped",[12527,12483,12488]],[[13144,13144],"mapped",[48,28857]],[[13145,13145],"mapped",[49,28857]],[[13146,13146],"mapped",[50,28857]],[[13147,13147],"mapped",[51,28857]],[[13148,13148],"mapped",[52,28857]],[[13149,13149],"mapped",[53,28857]],[[13150,13150],"mapped",[54,28857]],[[13151,13151],"mapped",[55,28857]],[[13152,13152],"mapped",[56,28857]],[[13153,13153],"mapped",[57,28857]],[[13154,13154],"mapped",[49,48,28857]],[[13155,13155],"mapped",[49,49,28857]],[[13156,13156],"mapped",[49,50,28857]],[[13157,13157],"mapped",[49,51,28857]],[[13158,13158],"mapped",[49,52,28857]],[[13159,13159],"mapped",[49,53,28857]],[[13160,13160],"mapped",[49,54,28857]],[[13161,13161],"mapped",[49,55,28857]],[[13162,13162],"mapped",[49,56,28857]],[[13163,13163],"mapped",[49,57,28857]],[[13164,13164],"mapped",[50,48,28857]],[[13165,13165],"mapped",[50,49,28857]],[[13166,13166],"mapped",[50,50,28857]],[[13167,13167],"mapped",[50,51,28857]],[[13168,13168],"mapped",[50,52,28857]],[[13169,13169],"mapped",[104,112,97]],[[13170,13170],"mapped",[100,97]],[[13171,13171],"mapped",[97,117]],[[13172,13172],"mapped",[98,97,114]],[[13173,13173],"mapped",[111,118]],[[13174,13174],"mapped",[112,99]],[[13175,13175],"mapped",[100,109]],[[13176,13176],"mapped",[100,109,50]],[[13177,13177],"mapped",[100,109,51]],[[13178,13178],"mapped",[105,117]],[[13179,13179],"mapped",[24179,25104]],[[13180,13180],"mapped",[26157,21644]],[[13181,13181],"mapped",[22823,27491]],[[13182,13182],"mapped",[26126,27835]],[[13183,13183],"mapped",[26666,24335,20250,31038]],[[13184,13184],"mapped",[112,97]],[[13185,13185],"mapped",[110,97]],[[13186,13186],"mapped",[956,97]],[[13187,13187],"mapped",[109,97]],[[13188,13188],"mapped",[107,97]],[[13189,13189],"mapped",[107,98]],[[13190,13190],"mapped",[109,98]],[[13191,13191],"mapped",[103,98]],[[13192,13192],"mapped",[99,97,108]],[[13193,13193],"mapped",[107,99,97,108]],[[13194,13194],"mapped",[112,102]],[[13195,13195],"mapped",[110,102]],[[13196,13196],"mapped",[956,102]],[[13197,13197],"mapped",[956,103]],[[13198,13198],"mapped",[109,103]],[[13199,13199],"mapped",[107,103]],[[13200,13200],"mapped",[104,122]],[[13201,13201],"mapped",[107,104,122]],[[13202,13202],"mapped",[109,104,122]],[[13203,13203],"mapped",[103,104,122]],[[13204,13204],"mapped",[116,104,122]],[[13205,13205],"mapped",[956,108]],[[13206,13206],"mapped",[109,108]],[[13207,13207],"mapped",[100,108]],[[13208,13208],"mapped",[107,108]],[[13209,13209],"mapped",[102,109]],[[13210,13210],"mapped",[110,109]],[[13211,13211],"mapped",[956,109]],[[13212,13212],"mapped",[109,109]],[[13213,13213],"mapped",[99,109]],[[13214,13214],"mapped",[107,109]],[[13215,13215],"mapped",[109,109,50]],[[13216,13216],"mapped",[99,109,50]],[[13217,13217],"mapped",[109,50]],[[13218,13218],"mapped",[107,109,50]],[[13219,13219],"mapped",[109,109,51]],[[13220,13220],"mapped",[99,109,51]],[[13221,13221],"mapped",[109,51]],[[13222,13222],"mapped",[107,109,51]],[[13223,13223],"mapped",[109,8725,115]],[[13224,13224],"mapped",[109,8725,115,50]],[[13225,13225],"mapped",[112,97]],[[13226,13226],"mapped",[107,112,97]],[[13227,13227],"mapped",[109,112,97]],[[13228,13228],"mapped",[103,112,97]],[[13229,13229],"mapped",[114,97,100]],[[13230,13230],"mapped",[114,97,100,8725,115]],[[13231,13231],"mapped",[114,97,100,8725,115,50]],[[13232,13232],"mapped",[112,115]],[[13233,13233],"mapped",[110,115]],[[13234,13234],"mapped",[956,115]],[[13235,13235],"mapped",[109,115]],[[13236,13236],"mapped",[112,118]],[[13237,13237],"mapped",[110,118]],[[13238,13238],"mapped",[956,118]],[[13239,13239],"mapped",[109,118]],[[13240,13240],"mapped",[107,118]],[[13241,13241],"mapped",[109,118]],[[13242,13242],"mapped",[112,119]],[[13243,13243],"mapped",[110,119]],[[13244,13244],"mapped",[956,119]],[[13245,13245],"mapped",[109,119]],[[13246,13246],"mapped",[107,119]],[[13247,13247],"mapped",[109,119]],[[13248,13248],"mapped",[107,969]],[[13249,13249],"mapped",[109,969]],[[13250,13250],"disallowed"],[[13251,13251],"mapped",[98,113]],[[13252,13252],"mapped",[99,99]],[[13253,13253],"mapped",[99,100]],[[13254,13254],"mapped",[99,8725,107,103]],[[13255,13255],"disallowed"],[[13256,13256],"mapped",[100,98]],[[13257,13257],"mapped",[103,121]],[[13258,13258],"mapped",[104,97]],[[13259,13259],"mapped",[104,112]],[[13260,13260],"mapped",[105,110]],[[13261,13261],"mapped",[107,107]],[[13262,13262],"mapped",[107,109]],[[13263,13263],"mapped",[107,116]],[[13264,13264],"mapped",[108,109]],[[13265,13265],"mapped",[108,110]],[[13266,13266],"mapped",[108,111,103]],[[13267,13267],"mapped",[108,120]],[[13268,13268],"mapped",[109,98]],[[13269,13269],"mapped",[109,105,108]],[[13270,13270],"mapped",[109,111,108]],[[13271,13271],"mapped",[112,104]],[[13272,13272],"disallowed"],[[13273,13273],"mapped",[112,112,109]],[[13274,13274],"mapped",[112,114]],[[13275,13275],"mapped",[115,114]],[[13276,13276],"mapped",[115,118]],[[13277,13277],"mapped",[119,98]],[[13278,13278],"mapped",[118,8725,109]],[[13279,13279],"mapped",[97,8725,109]],[[13280,13280],"mapped",[49,26085]],[[13281,13281],"mapped",[50,26085]],[[13282,13282],"mapped",[51,26085]],[[13283,13283],"mapped",[52,26085]],[[13284,13284],"mapped",[53,26085]],[[13285,13285],"mapped",[54,26085]],[[13286,13286],"mapped",[55,26085]],[[13287,13287],"mapped",[56,26085]],[[13288,13288],"mapped",[57,26085]],[[13289,13289],"mapped",[49,48,26085]],[[13290,13290],"mapped",[49,49,26085]],[[13291,13291],"mapped",[49,50,26085]],[[13292,13292],"mapped",[49,51,26085]],[[13293,13293],"mapped",[49,52,26085]],[[13294,13294],"mapped",[49,53,26085]],[[13295,13295],"mapped",[49,54,26085]],[[13296,13296],"mapped",[49,55,26085]],[[13297,13297],"mapped",[49,56,26085]],[[13298,13298],"mapped",[49,57,26085]],[[13299,13299],"mapped",[50,48,26085]],[[13300,13300],"mapped",[50,49,26085]],[[13301,13301],"mapped",[50,50,26085]],[[13302,13302],"mapped",[50,51,26085]],[[13303,13303],"mapped",[50,52,26085]],[[13304,13304],"mapped",[50,53,26085]],[[13305,13305],"mapped",[50,54,26085]],[[13306,13306],"mapped",[50,55,26085]],[[13307,13307],"mapped",[50,56,26085]],[[13308,13308],"mapped",[50,57,26085]],[[13309,13309],"mapped",[51,48,26085]],[[13310,13310],"mapped",[51,49,26085]],[[13311,13311],"mapped",[103,97,108]],[[13312,19893],"valid"],[[19894,19903],"disallowed"],[[19904,19967],"valid",[],"NV8"],[[19968,40869],"valid"],[[40870,40891],"valid"],[[40892,40899],"valid"],[[40900,40907],"valid"],[[40908,40908],"valid"],[[40909,40917],"valid"],[[40918,40959],"disallowed"],[[40960,42124],"valid"],[[42125,42127],"disallowed"],[[42128,42145],"valid",[],"NV8"],[[42146,42147],"valid",[],"NV8"],[[42148,42163],"valid",[],"NV8"],[[42164,42164],"valid",[],"NV8"],[[42165,42176],"valid",[],"NV8"],[[42177,42177],"valid",[],"NV8"],[[42178,42180],"valid",[],"NV8"],[[42181,42181],"valid",[],"NV8"],[[42182,42182],"valid",[],"NV8"],[[42183,42191],"disallowed"],[[42192,42237],"valid"],[[42238,42239],"valid",[],"NV8"],[[42240,42508],"valid"],[[42509,42511],"valid",[],"NV8"],[[42512,42539],"valid"],[[42540,42559],"disallowed"],[[42560,42560],"mapped",[42561]],[[42561,42561],"valid"],[[42562,42562],"mapped",[42563]],[[42563,42563],"valid"],[[42564,42564],"mapped",[42565]],[[42565,42565],"valid"],[[42566,42566],"mapped",[42567]],[[42567,42567],"valid"],[[42568,42568],"mapped",[42569]],[[42569,42569],"valid"],[[42570,42570],"mapped",[42571]],[[42571,42571],"valid"],[[42572,42572],"mapped",[42573]],[[42573,42573],"valid"],[[42574,42574],"mapped",[42575]],[[42575,42575],"valid"],[[42576,42576],"mapped",[42577]],[[42577,42577],"valid"],[[42578,42578],"mapped",[42579]],[[42579,42579],"valid"],[[42580,42580],"mapped",[42581]],[[42581,42581],"valid"],[[42582,42582],"mapped",[42583]],[[42583,42583],"valid"],[[42584,42584],"mapped",[42585]],[[42585,42585],"valid"],[[42586,42586],"mapped",[42587]],[[42587,42587],"valid"],[[42588,42588],"mapped",[42589]],[[42589,42589],"valid"],[[42590,42590],"mapped",[42591]],[[42591,42591],"valid"],[[42592,42592],"mapped",[42593]],[[42593,42593],"valid"],[[42594,42594],"mapped",[42595]],[[42595,42595],"valid"],[[42596,42596],"mapped",[42597]],[[42597,42597],"valid"],[[42598,42598],"mapped",[42599]],[[42599,42599],"valid"],[[42600,42600],"mapped",[42601]],[[42601,42601],"valid"],[[42602,42602],"mapped",[42603]],[[42603,42603],"valid"],[[42604,42604],"mapped",[42605]],[[42605,42607],"valid"],[[42608,42611],"valid",[],"NV8"],[[42612,42619],"valid"],[[42620,42621],"valid"],[[42622,42622],"valid",[],"NV8"],[[42623,42623],"valid"],[[42624,42624],"mapped",[42625]],[[42625,42625],"valid"],[[42626,42626],"mapped",[42627]],[[42627,42627],"valid"],[[42628,42628],"mapped",[42629]],[[42629,42629],"valid"],[[42630,42630],"mapped",[42631]],[[42631,42631],"valid"],[[42632,42632],"mapped",[42633]],[[42633,42633],"valid"],[[42634,42634],"mapped",[42635]],[[42635,42635],"valid"],[[42636,42636],"mapped",[42637]],[[42637,42637],"valid"],[[42638,42638],"mapped",[42639]],[[42639,42639],"valid"],[[42640,42640],"mapped",[42641]],[[42641,42641],"valid"],[[42642,42642],"mapped",[42643]],[[42643,42643],"valid"],[[42644,42644],"mapped",[42645]],[[42645,42645],"valid"],[[42646,42646],"mapped",[42647]],[[42647,42647],"valid"],[[42648,42648],"mapped",[42649]],[[42649,42649],"valid"],[[42650,42650],"mapped",[42651]],[[42651,42651],"valid"],[[42652,42652],"mapped",[1098]],[[42653,42653],"mapped",[1100]],[[42654,42654],"valid"],[[42655,42655],"valid"],[[42656,42725],"valid"],[[42726,42735],"valid",[],"NV8"],[[42736,42737],"valid"],[[42738,42743],"valid",[],"NV8"],[[42744,42751],"disallowed"],[[42752,42774],"valid",[],"NV8"],[[42775,42778],"valid"],[[42779,42783],"valid"],[[42784,42785],"valid",[],"NV8"],[[42786,42786],"mapped",[42787]],[[42787,42787],"valid"],[[42788,42788],"mapped",[42789]],[[42789,42789],"valid"],[[42790,42790],"mapped",[42791]],[[42791,42791],"valid"],[[42792,42792],"mapped",[42793]],[[42793,42793],"valid"],[[42794,42794],"mapped",[42795]],[[42795,42795],"valid"],[[42796,42796],"mapped",[42797]],[[42797,42797],"valid"],[[42798,42798],"mapped",[42799]],[[42799,42801],"valid"],[[42802,42802],"mapped",[42803]],[[42803,42803],"valid"],[[42804,42804],"mapped",[42805]],[[42805,42805],"valid"],[[42806,42806],"mapped",[42807]],[[42807,42807],"valid"],[[42808,42808],"mapped",[42809]],[[42809,42809],"valid"],[[42810,42810],"mapped",[42811]],[[42811,42811],"valid"],[[42812,42812],"mapped",[42813]],[[42813,42813],"valid"],[[42814,42814],"mapped",[42815]],[[42815,42815],"valid"],[[42816,42816],"mapped",[42817]],[[42817,42817],"valid"],[[42818,42818],"mapped",[42819]],[[42819,42819],"valid"],[[42820,42820],"mapped",[42821]],[[42821,42821],"valid"],[[42822,42822],"mapped",[42823]],[[42823,42823],"valid"],[[42824,42824],"mapped",[42825]],[[42825,42825],"valid"],[[42826,42826],"mapped",[42827]],[[42827,42827],"valid"],[[42828,42828],"mapped",[42829]],[[42829,42829],"valid"],[[42830,42830],"mapped",[42831]],[[42831,42831],"valid"],[[42832,42832],"mapped",[42833]],[[42833,42833],"valid"],[[42834,42834],"mapped",[42835]],[[42835,42835],"valid"],[[42836,42836],"mapped",[42837]],[[42837,42837],"valid"],[[42838,42838],"mapped",[42839]],[[42839,42839],"valid"],[[42840,42840],"mapped",[42841]],[[42841,42841],"valid"],[[42842,42842],"mapped",[42843]],[[42843,42843],"valid"],[[42844,42844],"mapped",[42845]],[[42845,42845],"valid"],[[42846,42846],"mapped",[42847]],[[42847,42847],"valid"],[[42848,42848],"mapped",[42849]],[[42849,42849],"valid"],[[42850,42850],"mapped",[42851]],[[42851,42851],"valid"],[[42852,42852],"mapped",[42853]],[[42853,42853],"valid"],[[42854,42854],"mapped",[42855]],[[42855,42855],"valid"],[[42856,42856],"mapped",[42857]],[[42857,42857],"valid"],[[42858,42858],"mapped",[42859]],[[42859,42859],"valid"],[[42860,42860],"mapped",[42861]],[[42861,42861],"valid"],[[42862,42862],"mapped",[42863]],[[42863,42863],"valid"],[[42864,42864],"mapped",[42863]],[[42865,42872],"valid"],[[42873,42873],"mapped",[42874]],[[42874,42874],"valid"],[[42875,42875],"mapped",[42876]],[[42876,42876],"valid"],[[42877,42877],"mapped",[7545]],[[42878,42878],"mapped",[42879]],[[42879,42879],"valid"],[[42880,42880],"mapped",[42881]],[[42881,42881],"valid"],[[42882,42882],"mapped",[42883]],[[42883,42883],"valid"],[[42884,42884],"mapped",[42885]],[[42885,42885],"valid"],[[42886,42886],"mapped",[42887]],[[42887,42888],"valid"],[[42889,42890],"valid",[],"NV8"],[[42891,42891],"mapped",[42892]],[[42892,42892],"valid"],[[42893,42893],"mapped",[613]],[[42894,42894],"valid"],[[42895,42895],"valid"],[[42896,42896],"mapped",[42897]],[[42897,42897],"valid"],[[42898,42898],"mapped",[42899]],[[42899,42899],"valid"],[[42900,42901],"valid"],[[42902,42902],"mapped",[42903]],[[42903,42903],"valid"],[[42904,42904],"mapped",[42905]],[[42905,42905],"valid"],[[42906,42906],"mapped",[42907]],[[42907,42907],"valid"],[[42908,42908],"mapped",[42909]],[[42909,42909],"valid"],[[42910,42910],"mapped",[42911]],[[42911,42911],"valid"],[[42912,42912],"mapped",[42913]],[[42913,42913],"valid"],[[42914,42914],"mapped",[42915]],[[42915,42915],"valid"],[[42916,42916],"mapped",[42917]],[[42917,42917],"valid"],[[42918,42918],"mapped",[42919]],[[42919,42919],"valid"],[[42920,42920],"mapped",[42921]],[[42921,42921],"valid"],[[42922,42922],"mapped",[614]],[[42923,42923],"mapped",[604]],[[42924,42924],"mapped",[609]],[[42925,42925],"mapped",[620]],[[42926,42927],"disallowed"],[[42928,42928],"mapped",[670]],[[42929,42929],"mapped",[647]],[[42930,42930],"mapped",[669]],[[42931,42931],"mapped",[43859]],[[42932,42932],"mapped",[42933]],[[42933,42933],"valid"],[[42934,42934],"mapped",[42935]],[[42935,42935],"valid"],[[42936,42998],"disallowed"],[[42999,42999],"valid"],[[43000,43000],"mapped",[295]],[[43001,43001],"mapped",[339]],[[43002,43002],"valid"],[[43003,43007],"valid"],[[43008,43047],"valid"],[[43048,43051],"valid",[],"NV8"],[[43052,43055],"disallowed"],[[43056,43065],"valid",[],"NV8"],[[43066,43071],"disallowed"],[[43072,43123],"valid"],[[43124,43127],"valid",[],"NV8"],[[43128,43135],"disallowed"],[[43136,43204],"valid"],[[43205,43213],"disallowed"],[[43214,43215],"valid",[],"NV8"],[[43216,43225],"valid"],[[43226,43231],"disallowed"],[[43232,43255],"valid"],[[43256,43258],"valid",[],"NV8"],[[43259,43259],"valid"],[[43260,43260],"valid",[],"NV8"],[[43261,43261],"valid"],[[43262,43263],"disallowed"],[[43264,43309],"valid"],[[43310,43311],"valid",[],"NV8"],[[43312,43347],"valid"],[[43348,43358],"disallowed"],[[43359,43359],"valid",[],"NV8"],[[43360,43388],"valid",[],"NV8"],[[43389,43391],"disallowed"],[[43392,43456],"valid"],[[43457,43469],"valid",[],"NV8"],[[43470,43470],"disallowed"],[[43471,43481],"valid"],[[43482,43485],"disallowed"],[[43486,43487],"valid",[],"NV8"],[[43488,43518],"valid"],[[43519,43519],"disallowed"],[[43520,43574],"valid"],[[43575,43583],"disallowed"],[[43584,43597],"valid"],[[43598,43599],"disallowed"],[[43600,43609],"valid"],[[43610,43611],"disallowed"],[[43612,43615],"valid",[],"NV8"],[[43616,43638],"valid"],[[43639,43641],"valid",[],"NV8"],[[43642,43643],"valid"],[[43644,43647],"valid"],[[43648,43714],"valid"],[[43715,43738],"disallowed"],[[43739,43741],"valid"],[[43742,43743],"valid",[],"NV8"],[[43744,43759],"valid"],[[43760,43761],"valid",[],"NV8"],[[43762,43766],"valid"],[[43767,43776],"disallowed"],[[43777,43782],"valid"],[[43783,43784],"disallowed"],[[43785,43790],"valid"],[[43791,43792],"disallowed"],[[43793,43798],"valid"],[[43799,43807],"disallowed"],[[43808,43814],"valid"],[[43815,43815],"disallowed"],[[43816,43822],"valid"],[[43823,43823],"disallowed"],[[43824,43866],"valid"],[[43867,43867],"valid",[],"NV8"],[[43868,43868],"mapped",[42791]],[[43869,43869],"mapped",[43831]],[[43870,43870],"mapped",[619]],[[43871,43871],"mapped",[43858]],[[43872,43875],"valid"],[[43876,43877],"valid"],[[43878,43887],"disallowed"],[[43888,43888],"mapped",[5024]],[[43889,43889],"mapped",[5025]],[[43890,43890],"mapped",[5026]],[[43891,43891],"mapped",[5027]],[[43892,43892],"mapped",[5028]],[[43893,43893],"mapped",[5029]],[[43894,43894],"mapped",[5030]],[[43895,43895],"mapped",[5031]],[[43896,43896],"mapped",[5032]],[[43897,43897],"mapped",[5033]],[[43898,43898],"mapped",[5034]],[[43899,43899],"mapped",[5035]],[[43900,43900],"mapped",[5036]],[[43901,43901],"mapped",[5037]],[[43902,43902],"mapped",[5038]],[[43903,43903],"mapped",[5039]],[[43904,43904],"mapped",[5040]],[[43905,43905],"mapped",[5041]],[[43906,43906],"mapped",[5042]],[[43907,43907],"mapped",[5043]],[[43908,43908],"mapped",[5044]],[[43909,43909],"mapped",[5045]],[[43910,43910],"mapped",[5046]],[[43911,43911],"mapped",[5047]],[[43912,43912],"mapped",[5048]],[[43913,43913],"mapped",[5049]],[[43914,43914],"mapped",[5050]],[[43915,43915],"mapped",[5051]],[[43916,43916],"mapped",[5052]],[[43917,43917],"mapped",[5053]],[[43918,43918],"mapped",[5054]],[[43919,43919],"mapped",[5055]],[[43920,43920],"mapped",[5056]],[[43921,43921],"mapped",[5057]],[[43922,43922],"mapped",[5058]],[[43923,43923],"mapped",[5059]],[[43924,43924],"mapped",[5060]],[[43925,43925],"mapped",[5061]],[[43926,43926],"mapped",[5062]],[[43927,43927],"mapped",[5063]],[[43928,43928],"mapped",[5064]],[[43929,43929],"mapped",[5065]],[[43930,43930],"mapped",[5066]],[[43931,43931],"mapped",[5067]],[[43932,43932],"mapped",[5068]],[[43933,43933],"mapped",[5069]],[[43934,43934],"mapped",[5070]],[[43935,43935],"mapped",[5071]],[[43936,43936],"mapped",[5072]],[[43937,43937],"mapped",[5073]],[[43938,43938],"mapped",[5074]],[[43939,43939],"mapped",[5075]],[[43940,43940],"mapped",[5076]],[[43941,43941],"mapped",[5077]],[[43942,43942],"mapped",[5078]],[[43943,43943],"mapped",[5079]],[[43944,43944],"mapped",[5080]],[[43945,43945],"mapped",[5081]],[[43946,43946],"mapped",[5082]],[[43947,43947],"mapped",[5083]],[[43948,43948],"mapped",[5084]],[[43949,43949],"mapped",[5085]],[[43950,43950],"mapped",[5086]],[[43951,43951],"mapped",[5087]],[[43952,43952],"mapped",[5088]],[[43953,43953],"mapped",[5089]],[[43954,43954],"mapped",[5090]],[[43955,43955],"mapped",[5091]],[[43956,43956],"mapped",[5092]],[[43957,43957],"mapped",[5093]],[[43958,43958],"mapped",[5094]],[[43959,43959],"mapped",[5095]],[[43960,43960],"mapped",[5096]],[[43961,43961],"mapped",[5097]],[[43962,43962],"mapped",[5098]],[[43963,43963],"mapped",[5099]],[[43964,43964],"mapped",[5100]],[[43965,43965],"mapped",[5101]],[[43966,43966],"mapped",[5102]],[[43967,43967],"mapped",[5103]],[[43968,44010],"valid"],[[44011,44011],"valid",[],"NV8"],[[44012,44013],"valid"],[[44014,44015],"disallowed"],[[44016,44025],"valid"],[[44026,44031],"disallowed"],[[44032,55203],"valid"],[[55204,55215],"disallowed"],[[55216,55238],"valid",[],"NV8"],[[55239,55242],"disallowed"],[[55243,55291],"valid",[],"NV8"],[[55292,55295],"disallowed"],[[55296,57343],"disallowed"],[[57344,63743],"disallowed"],[[63744,63744],"mapped",[35912]],[[63745,63745],"mapped",[26356]],[[63746,63746],"mapped",[36554]],[[63747,63747],"mapped",[36040]],[[63748,63748],"mapped",[28369]],[[63749,63749],"mapped",[20018]],[[63750,63750],"mapped",[21477]],[[63751,63752],"mapped",[40860]],[[63753,63753],"mapped",[22865]],[[63754,63754],"mapped",[37329]],[[63755,63755],"mapped",[21895]],[[63756,63756],"mapped",[22856]],[[63757,63757],"mapped",[25078]],[[63758,63758],"mapped",[30313]],[[63759,63759],"mapped",[32645]],[[63760,63760],"mapped",[34367]],[[63761,63761],"mapped",[34746]],[[63762,63762],"mapped",[35064]],[[63763,63763],"mapped",[37007]],[[63764,63764],"mapped",[27138]],[[63765,63765],"mapped",[27931]],[[63766,63766],"mapped",[28889]],[[63767,63767],"mapped",[29662]],[[63768,63768],"mapped",[33853]],[[63769,63769],"mapped",[37226]],[[63770,63770],"mapped",[39409]],[[63771,63771],"mapped",[20098]],[[63772,63772],"mapped",[21365]],[[63773,63773],"mapped",[27396]],[[63774,63774],"mapped",[29211]],[[63775,63775],"mapped",[34349]],[[63776,63776],"mapped",[40478]],[[63777,63777],"mapped",[23888]],[[63778,63778],"mapped",[28651]],[[63779,63779],"mapped",[34253]],[[63780,63780],"mapped",[35172]],[[63781,63781],"mapped",[25289]],[[63782,63782],"mapped",[33240]],[[63783,63783],"mapped",[34847]],[[63784,63784],"mapped",[24266]],[[63785,63785],"mapped",[26391]],[[63786,63786],"mapped",[28010]],[[63787,63787],"mapped",[29436]],[[63788,63788],"mapped",[37070]],[[63789,63789],"mapped",[20358]],[[63790,63790],"mapped",[20919]],[[63791,63791],"mapped",[21214]],[[63792,63792],"mapped",[25796]],[[63793,63793],"mapped",[27347]],[[63794,63794],"mapped",[29200]],[[63795,63795],"mapped",[30439]],[[63796,63796],"mapped",[32769]],[[63797,63797],"mapped",[34310]],[[63798,63798],"mapped",[34396]],[[63799,63799],"mapped",[36335]],[[63800,63800],"mapped",[38706]],[[63801,63801],"mapped",[39791]],[[63802,63802],"mapped",[40442]],[[63803,63803],"mapped",[30860]],[[63804,63804],"mapped",[31103]],[[63805,63805],"mapped",[32160]],[[63806,63806],"mapped",[33737]],[[63807,63807],"mapped",[37636]],[[63808,63808],"mapped",[40575]],[[63809,63809],"mapped",[35542]],[[63810,63810],"mapped",[22751]],[[63811,63811],"mapped",[24324]],[[63812,63812],"mapped",[31840]],[[63813,63813],"mapped",[32894]],[[63814,63814],"mapped",[29282]],[[63815,63815],"mapped",[30922]],[[63816,63816],"mapped",[36034]],[[63817,63817],"mapped",[38647]],[[63818,63818],"mapped",[22744]],[[63819,63819],"mapped",[23650]],[[63820,63820],"mapped",[27155]],[[63821,63821],"mapped",[28122]],[[63822,63822],"mapped",[28431]],[[63823,63823],"mapped",[32047]],[[63824,63824],"mapped",[32311]],[[63825,63825],"mapped",[38475]],[[63826,63826],"mapped",[21202]],[[63827,63827],"mapped",[32907]],[[63828,63828],"mapped",[20956]],[[63829,63829],"mapped",[20940]],[[63830,63830],"mapped",[31260]],[[63831,63831],"mapped",[32190]],[[63832,63832],"mapped",[33777]],[[63833,63833],"mapped",[38517]],[[63834,63834],"mapped",[35712]],[[63835,63835],"mapped",[25295]],[[63836,63836],"mapped",[27138]],[[63837,63837],"mapped",[35582]],[[63838,63838],"mapped",[20025]],[[63839,63839],"mapped",[23527]],[[63840,63840],"mapped",[24594]],[[63841,63841],"mapped",[29575]],[[63842,63842],"mapped",[30064]],[[63843,63843],"mapped",[21271]],[[63844,63844],"mapped",[30971]],[[63845,63845],"mapped",[20415]],[[63846,63846],"mapped",[24489]],[[63847,63847],"mapped",[19981]],[[63848,63848],"mapped",[27852]],[[63849,63849],"mapped",[25976]],[[63850,63850],"mapped",[32034]],[[63851,63851],"mapped",[21443]],[[63852,63852],"mapped",[22622]],[[63853,63853],"mapped",[30465]],[[63854,63854],"mapped",[33865]],[[63855,63855],"mapped",[35498]],[[63856,63856],"mapped",[27578]],[[63857,63857],"mapped",[36784]],[[63858,63858],"mapped",[27784]],[[63859,63859],"mapped",[25342]],[[63860,63860],"mapped",[33509]],[[63861,63861],"mapped",[25504]],[[63862,63862],"mapped",[30053]],[[63863,63863],"mapped",[20142]],[[63864,63864],"mapped",[20841]],[[63865,63865],"mapped",[20937]],[[63866,63866],"mapped",[26753]],[[63867,63867],"mapped",[31975]],[[63868,63868],"mapped",[33391]],[[63869,63869],"mapped",[35538]],[[63870,63870],"mapped",[37327]],[[63871,63871],"mapped",[21237]],[[63872,63872],"mapped",[21570]],[[63873,63873],"mapped",[22899]],[[63874,63874],"mapped",[24300]],[[63875,63875],"mapped",[26053]],[[63876,63876],"mapped",[28670]],[[63877,63877],"mapped",[31018]],[[63878,63878],"mapped",[38317]],[[63879,63879],"mapped",[39530]],[[63880,63880],"mapped",[40599]],[[63881,63881],"mapped",[40654]],[[63882,63882],"mapped",[21147]],[[63883,63883],"mapped",[26310]],[[63884,63884],"mapped",[27511]],[[63885,63885],"mapped",[36706]],[[63886,63886],"mapped",[24180]],[[63887,63887],"mapped",[24976]],[[63888,63888],"mapped",[25088]],[[63889,63889],"mapped",[25754]],[[63890,63890],"mapped",[28451]],[[63891,63891],"mapped",[29001]],[[63892,63892],"mapped",[29833]],[[63893,63893],"mapped",[31178]],[[63894,63894],"mapped",[32244]],[[63895,63895],"mapped",[32879]],[[63896,63896],"mapped",[36646]],[[63897,63897],"mapped",[34030]],[[63898,63898],"mapped",[36899]],[[63899,63899],"mapped",[37706]],[[63900,63900],"mapped",[21015]],[[63901,63901],"mapped",[21155]],[[63902,63902],"mapped",[21693]],[[63903,63903],"mapped",[28872]],[[63904,63904],"mapped",[35010]],[[63905,63905],"mapped",[35498]],[[63906,63906],"mapped",[24265]],[[63907,63907],"mapped",[24565]],[[63908,63908],"mapped",[25467]],[[63909,63909],"mapped",[27566]],[[63910,63910],"mapped",[31806]],[[63911,63911],"mapped",[29557]],[[63912,63912],"mapped",[20196]],[[63913,63913],"mapped",[22265]],[[63914,63914],"mapped",[23527]],[[63915,63915],"mapped",[23994]],[[63916,63916],"mapped",[24604]],[[63917,63917],"mapped",[29618]],[[63918,63918],"mapped",[29801]],[[63919,63919],"mapped",[32666]],[[63920,63920],"mapped",[32838]],[[63921,63921],"mapped",[37428]],[[63922,63922],"mapped",[38646]],[[63923,63923],"mapped",[38728]],[[63924,63924],"mapped",[38936]],[[63925,63925],"mapped",[20363]],[[63926,63926],"mapped",[31150]],[[63927,63927],"mapped",[37300]],[[63928,63928],"mapped",[38584]],[[63929,63929],"mapped",[24801]],[[63930,63930],"mapped",[20102]],[[63931,63931],"mapped",[20698]],[[63932,63932],"mapped",[23534]],[[63933,63933],"mapped",[23615]],[[63934,63934],"mapped",[26009]],[[63935,63935],"mapped",[27138]],[[63936,63936],"mapped",[29134]],[[63937,63937],"mapped",[30274]],[[63938,63938],"mapped",[34044]],[[63939,63939],"mapped",[36988]],[[63940,63940],"mapped",[40845]],[[63941,63941],"mapped",[26248]],[[63942,63942],"mapped",[38446]],[[63943,63943],"mapped",[21129]],[[63944,63944],"mapped",[26491]],[[63945,63945],"mapped",[26611]],[[63946,63946],"mapped",[27969]],[[63947,63947],"mapped",[28316]],[[63948,63948],"mapped",[29705]],[[63949,63949],"mapped",[30041]],[[63950,63950],"mapped",[30827]],[[63951,63951],"mapped",[32016]],[[63952,63952],"mapped",[39006]],[[63953,63953],"mapped",[20845]],[[63954,63954],"mapped",[25134]],[[63955,63955],"mapped",[38520]],[[63956,63956],"mapped",[20523]],[[63957,63957],"mapped",[23833]],[[63958,63958],"mapped",[28138]],[[63959,63959],"mapped",[36650]],[[63960,63960],"mapped",[24459]],[[63961,63961],"mapped",[24900]],[[63962,63962],"mapped",[26647]],[[63963,63963],"mapped",[29575]],[[63964,63964],"mapped",[38534]],[[63965,63965],"mapped",[21033]],[[63966,63966],"mapped",[21519]],[[63967,63967],"mapped",[23653]],[[63968,63968],"mapped",[26131]],[[63969,63969],"mapped",[26446]],[[63970,63970],"mapped",[26792]],[[63971,63971],"mapped",[27877]],[[63972,63972],"mapped",[29702]],[[63973,63973],"mapped",[30178]],[[63974,63974],"mapped",[32633]],[[63975,63975],"mapped",[35023]],[[63976,63976],"mapped",[35041]],[[63977,63977],"mapped",[37324]],[[63978,63978],"mapped",[38626]],[[63979,63979],"mapped",[21311]],[[63980,63980],"mapped",[28346]],[[63981,63981],"mapped",[21533]],[[63982,63982],"mapped",[29136]],[[63983,63983],"mapped",[29848]],[[63984,63984],"mapped",[34298]],[[63985,63985],"mapped",[38563]],[[63986,63986],"mapped",[40023]],[[63987,63987],"mapped",[40607]],[[63988,63988],"mapped",[26519]],[[63989,63989],"mapped",[28107]],[[63990,63990],"mapped",[33256]],[[63991,63991],"mapped",[31435]],[[63992,63992],"mapped",[31520]],[[63993,63993],"mapped",[31890]],[[63994,63994],"mapped",[29376]],[[63995,63995],"mapped",[28825]],[[63996,63996],"mapped",[35672]],[[63997,63997],"mapped",[20160]],[[63998,63998],"mapped",[33590]],[[63999,63999],"mapped",[21050]],[[64000,64000],"mapped",[20999]],[[64001,64001],"mapped",[24230]],[[64002,64002],"mapped",[25299]],[[64003,64003],"mapped",[31958]],[[64004,64004],"mapped",[23429]],[[64005,64005],"mapped",[27934]],[[64006,64006],"mapped",[26292]],[[64007,64007],"mapped",[36667]],[[64008,64008],"mapped",[34892]],[[64009,64009],"mapped",[38477]],[[64010,64010],"mapped",[35211]],[[64011,64011],"mapped",[24275]],[[64012,64012],"mapped",[20800]],[[64013,64013],"mapped",[21952]],[[64014,64015],"valid"],[[64016,64016],"mapped",[22618]],[[64017,64017],"valid"],[[64018,64018],"mapped",[26228]],[[64019,64020],"valid"],[[64021,64021],"mapped",[20958]],[[64022,64022],"mapped",[29482]],[[64023,64023],"mapped",[30410]],[[64024,64024],"mapped",[31036]],[[64025,64025],"mapped",[31070]],[[64026,64026],"mapped",[31077]],[[64027,64027],"mapped",[31119]],[[64028,64028],"mapped",[38742]],[[64029,64029],"mapped",[31934]],[[64030,64030],"mapped",[32701]],[[64031,64031],"valid"],[[64032,64032],"mapped",[34322]],[[64033,64033],"valid"],[[64034,64034],"mapped",[35576]],[[64035,64036],"valid"],[[64037,64037],"mapped",[36920]],[[64038,64038],"mapped",[37117]],[[64039,64041],"valid"],[[64042,64042],"mapped",[39151]],[[64043,64043],"mapped",[39164]],[[64044,64044],"mapped",[39208]],[[64045,64045],"mapped",[40372]],[[64046,64046],"mapped",[37086]],[[64047,64047],"mapped",[38583]],[[64048,64048],"mapped",[20398]],[[64049,64049],"mapped",[20711]],[[64050,64050],"mapped",[20813]],[[64051,64051],"mapped",[21193]],[[64052,64052],"mapped",[21220]],[[64053,64053],"mapped",[21329]],[[64054,64054],"mapped",[21917]],[[64055,64055],"mapped",[22022]],[[64056,64056],"mapped",[22120]],[[64057,64057],"mapped",[22592]],[[64058,64058],"mapped",[22696]],[[64059,64059],"mapped",[23652]],[[64060,64060],"mapped",[23662]],[[64061,64061],"mapped",[24724]],[[64062,64062],"mapped",[24936]],[[64063,64063],"mapped",[24974]],[[64064,64064],"mapped",[25074]],[[64065,64065],"mapped",[25935]],[[64066,64066],"mapped",[26082]],[[64067,64067],"mapped",[26257]],[[64068,64068],"mapped",[26757]],[[64069,64069],"mapped",[28023]],[[64070,64070],"mapped",[28186]],[[64071,64071],"mapped",[28450]],[[64072,64072],"mapped",[29038]],[[64073,64073],"mapped",[29227]],[[64074,64074],"mapped",[29730]],[[64075,64075],"mapped",[30865]],[[64076,64076],"mapped",[31038]],[[64077,64077],"mapped",[31049]],[[64078,64078],"mapped",[31048]],[[64079,64079],"mapped",[31056]],[[64080,64080],"mapped",[31062]],[[64081,64081],"mapped",[31069]],[[64082,64082],"mapped",[31117]],[[64083,64083],"mapped",[31118]],[[64084,64084],"mapped",[31296]],[[64085,64085],"mapped",[31361]],[[64086,64086],"mapped",[31680]],[[64087,64087],"mapped",[32244]],[[64088,64088],"mapped",[32265]],[[64089,64089],"mapped",[32321]],[[64090,64090],"mapped",[32626]],[[64091,64091],"mapped",[32773]],[[64092,64092],"mapped",[33261]],[[64093,64094],"mapped",[33401]],[[64095,64095],"mapped",[33879]],[[64096,64096],"mapped",[35088]],[[64097,64097],"mapped",[35222]],[[64098,64098],"mapped",[35585]],[[64099,64099],"mapped",[35641]],[[64100,64100],"mapped",[36051]],[[64101,64101],"mapped",[36104]],[[64102,64102],"mapped",[36790]],[[64103,64103],"mapped",[36920]],[[64104,64104],"mapped",[38627]],[[64105,64105],"mapped",[38911]],[[64106,64106],"mapped",[38971]],[[64107,64107],"mapped",[24693]],[[64108,64108],"mapped",[148206]],[[64109,64109],"mapped",[33304]],[[64110,64111],"disallowed"],[[64112,64112],"mapped",[20006]],[[64113,64113],"mapped",[20917]],[[64114,64114],"mapped",[20840]],[[64115,64115],"mapped",[20352]],[[64116,64116],"mapped",[20805]],[[64117,64117],"mapped",[20864]],[[64118,64118],"mapped",[21191]],[[64119,64119],"mapped",[21242]],[[64120,64120],"mapped",[21917]],[[64121,64121],"mapped",[21845]],[[64122,64122],"mapped",[21913]],[[64123,64123],"mapped",[21986]],[[64124,64124],"mapped",[22618]],[[64125,64125],"mapped",[22707]],[[64126,64126],"mapped",[22852]],[[64127,64127],"mapped",[22868]],[[64128,64128],"mapped",[23138]],[[64129,64129],"mapped",[23336]],[[64130,64130],"mapped",[24274]],[[64131,64131],"mapped",[24281]],[[64132,64132],"mapped",[24425]],[[64133,64133],"mapped",[24493]],[[64134,64134],"mapped",[24792]],[[64135,64135],"mapped",[24910]],[[64136,64136],"mapped",[24840]],[[64137,64137],"mapped",[24974]],[[64138,64138],"mapped",[24928]],[[64139,64139],"mapped",[25074]],[[64140,64140],"mapped",[25140]],[[64141,64141],"mapped",[25540]],[[64142,64142],"mapped",[25628]],[[64143,64143],"mapped",[25682]],[[64144,64144],"mapped",[25942]],[[64145,64145],"mapped",[26228]],[[64146,64146],"mapped",[26391]],[[64147,64147],"mapped",[26395]],[[64148,64148],"mapped",[26454]],[[64149,64149],"mapped",[27513]],[[64150,64150],"mapped",[27578]],[[64151,64151],"mapped",[27969]],[[64152,64152],"mapped",[28379]],[[64153,64153],"mapped",[28363]],[[64154,64154],"mapped",[28450]],[[64155,64155],"mapped",[28702]],[[64156,64156],"mapped",[29038]],[[64157,64157],"mapped",[30631]],[[64158,64158],"mapped",[29237]],[[64159,64159],"mapped",[29359]],[[64160,64160],"mapped",[29482]],[[64161,64161],"mapped",[29809]],[[64162,64162],"mapped",[29958]],[[64163,64163],"mapped",[30011]],[[64164,64164],"mapped",[30237]],[[64165,64165],"mapped",[30239]],[[64166,64166],"mapped",[30410]],[[64167,64167],"mapped",[30427]],[[64168,64168],"mapped",[30452]],[[64169,64169],"mapped",[30538]],[[64170,64170],"mapped",[30528]],[[64171,64171],"mapped",[30924]],[[64172,64172],"mapped",[31409]],[[64173,64173],"mapped",[31680]],[[64174,64174],"mapped",[31867]],[[64175,64175],"mapped",[32091]],[[64176,64176],"mapped",[32244]],[[64177,64177],"mapped",[32574]],[[64178,64178],"mapped",[32773]],[[64179,64179],"mapped",[33618]],[[64180,64180],"mapped",[33775]],[[64181,64181],"mapped",[34681]],[[64182,64182],"mapped",[35137]],[[64183,64183],"mapped",[35206]],[[64184,64184],"mapped",[35222]],[[64185,64185],"mapped",[35519]],[[64186,64186],"mapped",[35576]],[[64187,64187],"mapped",[35531]],[[64188,64188],"mapped",[35585]],[[64189,64189],"mapped",[35582]],[[64190,64190],"mapped",[35565]],[[64191,64191],"mapped",[35641]],[[64192,64192],"mapped",[35722]],[[64193,64193],"mapped",[36104]],[[64194,64194],"mapped",[36664]],[[64195,64195],"mapped",[36978]],[[64196,64196],"mapped",[37273]],[[64197,64197],"mapped",[37494]],[[64198,64198],"mapped",[38524]],[[64199,64199],"mapped",[38627]],[[64200,64200],"mapped",[38742]],[[64201,64201],"mapped",[38875]],[[64202,64202],"mapped",[38911]],[[64203,64203],"mapped",[38923]],[[64204,64204],"mapped",[38971]],[[64205,64205],"mapped",[39698]],[[64206,64206],"mapped",[40860]],[[64207,64207],"mapped",[141386]],[[64208,64208],"mapped",[141380]],[[64209,64209],"mapped",[144341]],[[64210,64210],"mapped",[15261]],[[64211,64211],"mapped",[16408]],[[64212,64212],"mapped",[16441]],[[64213,64213],"mapped",[152137]],[[64214,64214],"mapped",[154832]],[[64215,64215],"mapped",[163539]],[[64216,64216],"mapped",[40771]],[[64217,64217],"mapped",[40846]],[[64218,64255],"disallowed"],[[64256,64256],"mapped",[102,102]],[[64257,64257],"mapped",[102,105]],[[64258,64258],"mapped",[102,108]],[[64259,64259],"mapped",[102,102,105]],[[64260,64260],"mapped",[102,102,108]],[[64261,64262],"mapped",[115,116]],[[64263,64274],"disallowed"],[[64275,64275],"mapped",[1396,1398]],[[64276,64276],"mapped",[1396,1381]],[[64277,64277],"mapped",[1396,1387]],[[64278,64278],"mapped",[1406,1398]],[[64279,64279],"mapped",[1396,1389]],[[64280,64284],"disallowed"],[[64285,64285],"mapped",[1497,1460]],[[64286,64286],"valid"],[[64287,64287],"mapped",[1522,1463]],[[64288,64288],"mapped",[1506]],[[64289,64289],"mapped",[1488]],[[64290,64290],"mapped",[1491]],[[64291,64291],"mapped",[1492]],[[64292,64292],"mapped",[1499]],[[64293,64293],"mapped",[1500]],[[64294,64294],"mapped",[1501]],[[64295,64295],"mapped",[1512]],[[64296,64296],"mapped",[1514]],[[64297,64297],"disallowed_STD3_mapped",[43]],[[64298,64298],"mapped",[1513,1473]],[[64299,64299],"mapped",[1513,1474]],[[64300,64300],"mapped",[1513,1468,1473]],[[64301,64301],"mapped",[1513,1468,1474]],[[64302,64302],"mapped",[1488,1463]],[[64303,64303],"mapped",[1488,1464]],[[64304,64304],"mapped",[1488,1468]],[[64305,64305],"mapped",[1489,1468]],[[64306,64306],"mapped",[1490,1468]],[[64307,64307],"mapped",[1491,1468]],[[64308,64308],"mapped",[1492,1468]],[[64309,64309],"mapped",[1493,1468]],[[64310,64310],"mapped",[1494,1468]],[[64311,64311],"disallowed"],[[64312,64312],"mapped",[1496,1468]],[[64313,64313],"mapped",[1497,1468]],[[64314,64314],"mapped",[1498,1468]],[[64315,64315],"mapped",[1499,1468]],[[64316,64316],"mapped",[1500,1468]],[[64317,64317],"disallowed"],[[64318,64318],"mapped",[1502,1468]],[[64319,64319],"disallowed"],[[64320,64320],"mapped",[1504,1468]],[[64321,64321],"mapped",[1505,1468]],[[64322,64322],"disallowed"],[[64323,64323],"mapped",[1507,1468]],[[64324,64324],"mapped",[1508,1468]],[[64325,64325],"disallowed"],[[64326,64326],"mapped",[1510,1468]],[[64327,64327],"mapped",[1511,1468]],[[64328,64328],"mapped",[1512,1468]],[[64329,64329],"mapped",[1513,1468]],[[64330,64330],"mapped",[1514,1468]],[[64331,64331],"mapped",[1493,1465]],[[64332,64332],"mapped",[1489,1471]],[[64333,64333],"mapped",[1499,1471]],[[64334,64334],"mapped",[1508,1471]],[[64335,64335],"mapped",[1488,1500]],[[64336,64337],"mapped",[1649]],[[64338,64341],"mapped",[1659]],[[64342,64345],"mapped",[1662]],[[64346,64349],"mapped",[1664]],[[64350,64353],"mapped",[1658]],[[64354,64357],"mapped",[1663]],[[64358,64361],"mapped",[1657]],[[64362,64365],"mapped",[1700]],[[64366,64369],"mapped",[1702]],[[64370,64373],"mapped",[1668]],[[64374,64377],"mapped",[1667]],[[64378,64381],"mapped",[1670]],[[64382,64385],"mapped",[1671]],[[64386,64387],"mapped",[1677]],[[64388,64389],"mapped",[1676]],[[64390,64391],"mapped",[1678]],[[64392,64393],"mapped",[1672]],[[64394,64395],"mapped",[1688]],[[64396,64397],"mapped",[1681]],[[64398,64401],"mapped",[1705]],[[64402,64405],"mapped",[1711]],[[64406,64409],"mapped",[1715]],[[64410,64413],"mapped",[1713]],[[64414,64415],"mapped",[1722]],[[64416,64419],"mapped",[1723]],[[64420,64421],"mapped",[1728]],[[64422,64425],"mapped",[1729]],[[64426,64429],"mapped",[1726]],[[64430,64431],"mapped",[1746]],[[64432,64433],"mapped",[1747]],[[64434,64449],"valid",[],"NV8"],[[64450,64466],"disallowed"],[[64467,64470],"mapped",[1709]],[[64471,64472],"mapped",[1735]],[[64473,64474],"mapped",[1734]],[[64475,64476],"mapped",[1736]],[[64477,64477],"mapped",[1735,1652]],[[64478,64479],"mapped",[1739]],[[64480,64481],"mapped",[1733]],[[64482,64483],"mapped",[1737]],[[64484,64487],"mapped",[1744]],[[64488,64489],"mapped",[1609]],[[64490,64491],"mapped",[1574,1575]],[[64492,64493],"mapped",[1574,1749]],[[64494,64495],"mapped",[1574,1608]],[[64496,64497],"mapped",[1574,1735]],[[64498,64499],"mapped",[1574,1734]],[[64500,64501],"mapped",[1574,1736]],[[64502,64504],"mapped",[1574,1744]],[[64505,64507],"mapped",[1574,1609]],[[64508,64511],"mapped",[1740]],[[64512,64512],"mapped",[1574,1580]],[[64513,64513],"mapped",[1574,1581]],[[64514,64514],"mapped",[1574,1605]],[[64515,64515],"mapped",[1574,1609]],[[64516,64516],"mapped",[1574,1610]],[[64517,64517],"mapped",[1576,1580]],[[64518,64518],"mapped",[1576,1581]],[[64519,64519],"mapped",[1576,1582]],[[64520,64520],"mapped",[1576,1605]],[[64521,64521],"mapped",[1576,1609]],[[64522,64522],"mapped",[1576,1610]],[[64523,64523],"mapped",[1578,1580]],[[64524,64524],"mapped",[1578,1581]],[[64525,64525],"mapped",[1578,1582]],[[64526,64526],"mapped",[1578,1605]],[[64527,64527],"mapped",[1578,1609]],[[64528,64528],"mapped",[1578,1610]],[[64529,64529],"mapped",[1579,1580]],[[64530,64530],"mapped",[1579,1605]],[[64531,64531],"mapped",[1579,1609]],[[64532,64532],"mapped",[1579,1610]],[[64533,64533],"mapped",[1580,1581]],[[64534,64534],"mapped",[1580,1605]],[[64535,64535],"mapped",[1581,1580]],[[64536,64536],"mapped",[1581,1605]],[[64537,64537],"mapped",[1582,1580]],[[64538,64538],"mapped",[1582,1581]],[[64539,64539],"mapped",[1582,1605]],[[64540,64540],"mapped",[1587,1580]],[[64541,64541],"mapped",[1587,1581]],[[64542,64542],"mapped",[1587,1582]],[[64543,64543],"mapped",[1587,1605]],[[64544,64544],"mapped",[1589,1581]],[[64545,64545],"mapped",[1589,1605]],[[64546,64546],"mapped",[1590,1580]],[[64547,64547],"mapped",[1590,1581]],[[64548,64548],"mapped",[1590,1582]],[[64549,64549],"mapped",[1590,1605]],[[64550,64550],"mapped",[1591,1581]],[[64551,64551],"mapped",[1591,1605]],[[64552,64552],"mapped",[1592,1605]],[[64553,64553],"mapped",[1593,1580]],[[64554,64554],"mapped",[1593,1605]],[[64555,64555],"mapped",[1594,1580]],[[64556,64556],"mapped",[1594,1605]],[[64557,64557],"mapped",[1601,1580]],[[64558,64558],"mapped",[1601,1581]],[[64559,64559],"mapped",[1601,1582]],[[64560,64560],"mapped",[1601,1605]],[[64561,64561],"mapped",[1601,1609]],[[64562,64562],"mapped",[1601,1610]],[[64563,64563],"mapped",[1602,1581]],[[64564,64564],"mapped",[1602,1605]],[[64565,64565],"mapped",[1602,1609]],[[64566,64566],"mapped",[1602,1610]],[[64567,64567],"mapped",[1603,1575]],[[64568,64568],"mapped",[1603,1580]],[[64569,64569],"mapped",[1603,1581]],[[64570,64570],"mapped",[1603,1582]],[[64571,64571],"mapped",[1603,1604]],[[64572,64572],"mapped",[1603,1605]],[[64573,64573],"mapped",[1603,1609]],[[64574,64574],"mapped",[1603,1610]],[[64575,64575],"mapped",[1604,1580]],[[64576,64576],"mapped",[1604,1581]],[[64577,64577],"mapped",[1604,1582]],[[64578,64578],"mapped",[1604,1605]],[[64579,64579],"mapped",[1604,1609]],[[64580,64580],"mapped",[1604,1610]],[[64581,64581],"mapped",[1605,1580]],[[64582,64582],"mapped",[1605,1581]],[[64583,64583],"mapped",[1605,1582]],[[64584,64584],"mapped",[1605,1605]],[[64585,64585],"mapped",[1605,1609]],[[64586,64586],"mapped",[1605,1610]],[[64587,64587],"mapped",[1606,1580]],[[64588,64588],"mapped",[1606,1581]],[[64589,64589],"mapped",[1606,1582]],[[64590,64590],"mapped",[1606,1605]],[[64591,64591],"mapped",[1606,1609]],[[64592,64592],"mapped",[1606,1610]],[[64593,64593],"mapped",[1607,1580]],[[64594,64594],"mapped",[1607,1605]],[[64595,64595],"mapped",[1607,1609]],[[64596,64596],"mapped",[1607,1610]],[[64597,64597],"mapped",[1610,1580]],[[64598,64598],"mapped",[1610,1581]],[[64599,64599],"mapped",[1610,1582]],[[64600,64600],"mapped",[1610,1605]],[[64601,64601],"mapped",[1610,1609]],[[64602,64602],"mapped",[1610,1610]],[[64603,64603],"mapped",[1584,1648]],[[64604,64604],"mapped",[1585,1648]],[[64605,64605],"mapped",[1609,1648]],[[64606,64606],"disallowed_STD3_mapped",[32,1612,1617]],[[64607,64607],"disallowed_STD3_mapped",[32,1613,1617]],[[64608,64608],"disallowed_STD3_mapped",[32,1614,1617]],[[64609,64609],"disallowed_STD3_mapped",[32,1615,1617]],[[64610,64610],"disallowed_STD3_mapped",[32,1616,1617]],[[64611,64611],"disallowed_STD3_mapped",[32,1617,1648]],[[64612,64612],"mapped",[1574,1585]],[[64613,64613],"mapped",[1574,1586]],[[64614,64614],"mapped",[1574,1605]],[[64615,64615],"mapped",[1574,1606]],[[64616,64616],"mapped",[1574,1609]],[[64617,64617],"mapped",[1574,1610]],[[64618,64618],"mapped",[1576,1585]],[[64619,64619],"mapped",[1576,1586]],[[64620,64620],"mapped",[1576,1605]],[[64621,64621],"mapped",[1576,1606]],[[64622,64622],"mapped",[1576,1609]],[[64623,64623],"mapped",[1576,1610]],[[64624,64624],"mapped",[1578,1585]],[[64625,64625],"mapped",[1578,1586]],[[64626,64626],"mapped",[1578,1605]],[[64627,64627],"mapped",[1578,1606]],[[64628,64628],"mapped",[1578,1609]],[[64629,64629],"mapped",[1578,1610]],[[64630,64630],"mapped",[1579,1585]],[[64631,64631],"mapped",[1579,1586]],[[64632,64632],"mapped",[1579,1605]],[[64633,64633],"mapped",[1579,1606]],[[64634,64634],"mapped",[1579,1609]],[[64635,64635],"mapped",[1579,1610]],[[64636,64636],"mapped",[1601,1609]],[[64637,64637],"mapped",[1601,1610]],[[64638,64638],"mapped",[1602,1609]],[[64639,64639],"mapped",[1602,1610]],[[64640,64640],"mapped",[1603,1575]],[[64641,64641],"mapped",[1603,1604]],[[64642,64642],"mapped",[1603,1605]],[[64643,64643],"mapped",[1603,1609]],[[64644,64644],"mapped",[1603,1610]],[[64645,64645],"mapped",[1604,1605]],[[64646,64646],"mapped",[1604,1609]],[[64647,64647],"mapped",[1604,1610]],[[64648,64648],"mapped",[1605,1575]],[[64649,64649],"mapped",[1605,1605]],[[64650,64650],"mapped",[1606,1585]],[[64651,64651],"mapped",[1606,1586]],[[64652,64652],"mapped",[1606,1605]],[[64653,64653],"mapped",[1606,1606]],[[64654,64654],"mapped",[1606,1609]],[[64655,64655],"mapped",[1606,1610]],[[64656,64656],"mapped",[1609,1648]],[[64657,64657],"mapped",[1610,1585]],[[64658,64658],"mapped",[1610,1586]],[[64659,64659],"mapped",[1610,1605]],[[64660,64660],"mapped",[1610,1606]],[[64661,64661],"mapped",[1610,1609]],[[64662,64662],"mapped",[1610,1610]],[[64663,64663],"mapped",[1574,1580]],[[64664,64664],"mapped",[1574,1581]],[[64665,64665],"mapped",[1574,1582]],[[64666,64666],"mapped",[1574,1605]],[[64667,64667],"mapped",[1574,1607]],[[64668,64668],"mapped",[1576,1580]],[[64669,64669],"mapped",[1576,1581]],[[64670,64670],"mapped",[1576,1582]],[[64671,64671],"mapped",[1576,1605]],[[64672,64672],"mapped",[1576,1607]],[[64673,64673],"mapped",[1578,1580]],[[64674,64674],"mapped",[1578,1581]],[[64675,64675],"mapped",[1578,1582]],[[64676,64676],"mapped",[1578,1605]],[[64677,64677],"mapped",[1578,1607]],[[64678,64678],"mapped",[1579,1605]],[[64679,64679],"mapped",[1580,1581]],[[64680,64680],"mapped",[1580,1605]],[[64681,64681],"mapped",[1581,1580]],[[64682,64682],"mapped",[1581,1605]],[[64683,64683],"mapped",[1582,1580]],[[64684,64684],"mapped",[1582,1605]],[[64685,64685],"mapped",[1587,1580]],[[64686,64686],"mapped",[1587,1581]],[[64687,64687],"mapped",[1587,1582]],[[64688,64688],"mapped",[1587,1605]],[[64689,64689],"mapped",[1589,1581]],[[64690,64690],"mapped",[1589,1582]],[[64691,64691],"mapped",[1589,1605]],[[64692,64692],"mapped",[1590,1580]],[[64693,64693],"mapped",[1590,1581]],[[64694,64694],"mapped",[1590,1582]],[[64695,64695],"mapped",[1590,1605]],[[64696,64696],"mapped",[1591,1581]],[[64697,64697],"mapped",[1592,1605]],[[64698,64698],"mapped",[1593,1580]],[[64699,64699],"mapped",[1593,1605]],[[64700,64700],"mapped",[1594,1580]],[[64701,64701],"mapped",[1594,1605]],[[64702,64702],"mapped",[1601,1580]],[[64703,64703],"mapped",[1601,1581]],[[64704,64704],"mapped",[1601,1582]],[[64705,64705],"mapped",[1601,1605]],[[64706,64706],"mapped",[1602,1581]],[[64707,64707],"mapped",[1602,1605]],[[64708,64708],"mapped",[1603,1580]],[[64709,64709],"mapped",[1603,1581]],[[64710,64710],"mapped",[1603,1582]],[[64711,64711],"mapped",[1603,1604]],[[64712,64712],"mapped",[1603,1605]],[[64713,64713],"mapped",[1604,1580]],[[64714,64714],"mapped",[1604,1581]],[[64715,64715],"mapped",[1604,1582]],[[64716,64716],"mapped",[1604,1605]],[[64717,64717],"mapped",[1604,1607]],[[64718,64718],"mapped",[1605,1580]],[[64719,64719],"mapped",[1605,1581]],[[64720,64720],"mapped",[1605,1582]],[[64721,64721],"mapped",[1605,1605]],[[64722,64722],"mapped",[1606,1580]],[[64723,64723],"mapped",[1606,1581]],[[64724,64724],"mapped",[1606,1582]],[[64725,64725],"mapped",[1606,1605]],[[64726,64726],"mapped",[1606,1607]],[[64727,64727],"mapped",[1607,1580]],[[64728,64728],"mapped",[1607,1605]],[[64729,64729],"mapped",[1607,1648]],[[64730,64730],"mapped",[1610,1580]],[[64731,64731],"mapped",[1610,1581]],[[64732,64732],"mapped",[1610,1582]],[[64733,64733],"mapped",[1610,1605]],[[64734,64734],"mapped",[1610,1607]],[[64735,64735],"mapped",[1574,1605]],[[64736,64736],"mapped",[1574,1607]],[[64737,64737],"mapped",[1576,1605]],[[64738,64738],"mapped",[1576,1607]],[[64739,64739],"mapped",[1578,1605]],[[64740,64740],"mapped",[1578,1607]],[[64741,64741],"mapped",[1579,1605]],[[64742,64742],"mapped",[1579,1607]],[[64743,64743],"mapped",[1587,1605]],[[64744,64744],"mapped",[1587,1607]],[[64745,64745],"mapped",[1588,1605]],[[64746,64746],"mapped",[1588,1607]],[[64747,64747],"mapped",[1603,1604]],[[64748,64748],"mapped",[1603,1605]],[[64749,64749],"mapped",[1604,1605]],[[64750,64750],"mapped",[1606,1605]],[[64751,64751],"mapped",[1606,1607]],[[64752,64752],"mapped",[1610,1605]],[[64753,64753],"mapped",[1610,1607]],[[64754,64754],"mapped",[1600,1614,1617]],[[64755,64755],"mapped",[1600,1615,1617]],[[64756,64756],"mapped",[1600,1616,1617]],[[64757,64757],"mapped",[1591,1609]],[[64758,64758],"mapped",[1591,1610]],[[64759,64759],"mapped",[1593,1609]],[[64760,64760],"mapped",[1593,1610]],[[64761,64761],"mapped",[1594,1609]],[[64762,64762],"mapped",[1594,1610]],[[64763,64763],"mapped",[1587,1609]],[[64764,64764],"mapped",[1587,1610]],[[64765,64765],"mapped",[1588,1609]],[[64766,64766],"mapped",[1588,1610]],[[64767,64767],"mapped",[1581,1609]],[[64768,64768],"mapped",[1581,1610]],[[64769,64769],"mapped",[1580,1609]],[[64770,64770],"mapped",[1580,1610]],[[64771,64771],"mapped",[1582,1609]],[[64772,64772],"mapped",[1582,1610]],[[64773,64773],"mapped",[1589,1609]],[[64774,64774],"mapped",[1589,1610]],[[64775,64775],"mapped",[1590,1609]],[[64776,64776],"mapped",[1590,1610]],[[64777,64777],"mapped",[1588,1580]],[[64778,64778],"mapped",[1588,1581]],[[64779,64779],"mapped",[1588,1582]],[[64780,64780],"mapped",[1588,1605]],[[64781,64781],"mapped",[1588,1585]],[[64782,64782],"mapped",[1587,1585]],[[64783,64783],"mapped",[1589,1585]],[[64784,64784],"mapped",[1590,1585]],[[64785,64785],"mapped",[1591,1609]],[[64786,64786],"mapped",[1591,1610]],[[64787,64787],"mapped",[1593,1609]],[[64788,64788],"mapped",[1593,1610]],[[64789,64789],"mapped",[1594,1609]],[[64790,64790],"mapped",[1594,1610]],[[64791,64791],"mapped",[1587,1609]],[[64792,64792],"mapped",[1587,1610]],[[64793,64793],"mapped",[1588,1609]],[[64794,64794],"mapped",[1588,1610]],[[64795,64795],"mapped",[1581,1609]],[[64796,64796],"mapped",[1581,1610]],[[64797,64797],"mapped",[1580,1609]],[[64798,64798],"mapped",[1580,1610]],[[64799,64799],"mapped",[1582,1609]],[[64800,64800],"mapped",[1582,1610]],[[64801,64801],"mapped",[1589,1609]],[[64802,64802],"mapped",[1589,1610]],[[64803,64803],"mapped",[1590,1609]],[[64804,64804],"mapped",[1590,1610]],[[64805,64805],"mapped",[1588,1580]],[[64806,64806],"mapped",[1588,1581]],[[64807,64807],"mapped",[1588,1582]],[[64808,64808],"mapped",[1588,1605]],[[64809,64809],"mapped",[1588,1585]],[[64810,64810],"mapped",[1587,1585]],[[64811,64811],"mapped",[1589,1585]],[[64812,64812],"mapped",[1590,1585]],[[64813,64813],"mapped",[1588,1580]],[[64814,64814],"mapped",[1588,1581]],[[64815,64815],"mapped",[1588,1582]],[[64816,64816],"mapped",[1588,1605]],[[64817,64817],"mapped",[1587,1607]],[[64818,64818],"mapped",[1588,1607]],[[64819,64819],"mapped",[1591,1605]],[[64820,64820],"mapped",[1587,1580]],[[64821,64821],"mapped",[1587,1581]],[[64822,64822],"mapped",[1587,1582]],[[64823,64823],"mapped",[1588,1580]],[[64824,64824],"mapped",[1588,1581]],[[64825,64825],"mapped",[1588,1582]],[[64826,64826],"mapped",[1591,1605]],[[64827,64827],"mapped",[1592,1605]],[[64828,64829],"mapped",[1575,1611]],[[64830,64831],"valid",[],"NV8"],[[64832,64847],"disallowed"],[[64848,64848],"mapped",[1578,1580,1605]],[[64849,64850],"mapped",[1578,1581,1580]],[[64851,64851],"mapped",[1578,1581,1605]],[[64852,64852],"mapped",[1578,1582,1605]],[[64853,64853],"mapped",[1578,1605,1580]],[[64854,64854],"mapped",[1578,1605,1581]],[[64855,64855],"mapped",[1578,1605,1582]],[[64856,64857],"mapped",[1580,1605,1581]],[[64858,64858],"mapped",[1581,1605,1610]],[[64859,64859],"mapped",[1581,1605,1609]],[[64860,64860],"mapped",[1587,1581,1580]],[[64861,64861],"mapped",[1587,1580,1581]],[[64862,64862],"mapped",[1587,1580,1609]],[[64863,64864],"mapped",[1587,1605,1581]],[[64865,64865],"mapped",[1587,1605,1580]],[[64866,64867],"mapped",[1587,1605,1605]],[[64868,64869],"mapped",[1589,1581,1581]],[[64870,64870],"mapped",[1589,1605,1605]],[[64871,64872],"mapped",[1588,1581,1605]],[[64873,64873],"mapped",[1588,1580,1610]],[[64874,64875],"mapped",[1588,1605,1582]],[[64876,64877],"mapped",[1588,1605,1605]],[[64878,64878],"mapped",[1590,1581,1609]],[[64879,64880],"mapped",[1590,1582,1605]],[[64881,64882],"mapped",[1591,1605,1581]],[[64883,64883],"mapped",[1591,1605,1605]],[[64884,64884],"mapped",[1591,1605,1610]],[[64885,64885],"mapped",[1593,1580,1605]],[[64886,64887],"mapped",[1593,1605,1605]],[[64888,64888],"mapped",[1593,1605,1609]],[[64889,64889],"mapped",[1594,1605,1605]],[[64890,64890],"mapped",[1594,1605,1610]],[[64891,64891],"mapped",[1594,1605,1609]],[[64892,64893],"mapped",[1601,1582,1605]],[[64894,64894],"mapped",[1602,1605,1581]],[[64895,64895],"mapped",[1602,1605,1605]],[[64896,64896],"mapped",[1604,1581,1605]],[[64897,64897],"mapped",[1604,1581,1610]],[[64898,64898],"mapped",[1604,1581,1609]],[[64899,64900],"mapped",[1604,1580,1580]],[[64901,64902],"mapped",[1604,1582,1605]],[[64903,64904],"mapped",[1604,1605,1581]],[[64905,64905],"mapped",[1605,1581,1580]],[[64906,64906],"mapped",[1605,1581,1605]],[[64907,64907],"mapped",[1605,1581,1610]],[[64908,64908],"mapped",[1605,1580,1581]],[[64909,64909],"mapped",[1605,1580,1605]],[[64910,64910],"mapped",[1605,1582,1580]],[[64911,64911],"mapped",[1605,1582,1605]],[[64912,64913],"disallowed"],[[64914,64914],"mapped",[1605,1580,1582]],[[64915,64915],"mapped",[1607,1605,1580]],[[64916,64916],"mapped",[1607,1605,1605]],[[64917,64917],"mapped",[1606,1581,1605]],[[64918,64918],"mapped",[1606,1581,1609]],[[64919,64920],"mapped",[1606,1580,1605]],[[64921,64921],"mapped",[1606,1580,1609]],[[64922,64922],"mapped",[1606,1605,1610]],[[64923,64923],"mapped",[1606,1605,1609]],[[64924,64925],"mapped",[1610,1605,1605]],[[64926,64926],"mapped",[1576,1582,1610]],[[64927,64927],"mapped",[1578,1580,1610]],[[64928,64928],"mapped",[1578,1580,1609]],[[64929,64929],"mapped",[1578,1582,1610]],[[64930,64930],"mapped",[1578,1582,1609]],[[64931,64931],"mapped",[1578,1605,1610]],[[64932,64932],"mapped",[1578,1605,1609]],[[64933,64933],"mapped",[1580,1605,1610]],[[64934,64934],"mapped",[1580,1581,1609]],[[64935,64935],"mapped",[1580,1605,1609]],[[64936,64936],"mapped",[1587,1582,1609]],[[64937,64937],"mapped",[1589,1581,1610]],[[64938,64938],"mapped",[1588,1581,1610]],[[64939,64939],"mapped",[1590,1581,1610]],[[64940,64940],"mapped",[1604,1580,1610]],[[64941,64941],"mapped",[1604,1605,1610]],[[64942,64942],"mapped",[1610,1581,1610]],[[64943,64943],"mapped",[1610,1580,1610]],[[64944,64944],"mapped",[1610,1605,1610]],[[64945,64945],"mapped",[1605,1605,1610]],[[64946,64946],"mapped",[1602,1605,1610]],[[64947,64947],"mapped",[1606,1581,1610]],[[64948,64948],"mapped",[1602,1605,1581]],[[64949,64949],"mapped",[1604,1581,1605]],[[64950,64950],"mapped",[1593,1605,1610]],[[64951,64951],"mapped",[1603,1605,1610]],[[64952,64952],"mapped",[1606,1580,1581]],[[64953,64953],"mapped",[1605,1582,1610]],[[64954,64954],"mapped",[1604,1580,1605]],[[64955,64955],"mapped",[1603,1605,1605]],[[64956,64956],"mapped",[1604,1580,1605]],[[64957,64957],"mapped",[1606,1580,1581]],[[64958,64958],"mapped",[1580,1581,1610]],[[64959,64959],"mapped",[1581,1580,1610]],[[64960,64960],"mapped",[1605,1580,1610]],[[64961,64961],"mapped",[1601,1605,1610]],[[64962,64962],"mapped",[1576,1581,1610]],[[64963,64963],"mapped",[1603,1605,1605]],[[64964,64964],"mapped",[1593,1580,1605]],[[64965,64965],"mapped",[1589,1605,1605]],[[64966,64966],"mapped",[1587,1582,1610]],[[64967,64967],"mapped",[1606,1580,1610]],[[64968,64975],"disallowed"],[[64976,65007],"disallowed"],[[65008,65008],"mapped",[1589,1604,1746]],[[65009,65009],"mapped",[1602,1604,1746]],[[65010,65010],"mapped",[1575,1604,1604,1607]],[[65011,65011],"mapped",[1575,1603,1576,1585]],[[65012,65012],"mapped",[1605,1581,1605,1583]],[[65013,65013],"mapped",[1589,1604,1593,1605]],[[65014,65014],"mapped",[1585,1587,1608,1604]],[[65015,65015],"mapped",[1593,1604,1610,1607]],[[65016,65016],"mapped",[1608,1587,1604,1605]],[[65017,65017],"mapped",[1589,1604,1609]],[[65018,65018],"disallowed_STD3_mapped",[1589,1604,1609,32,1575,1604,1604,1607,32,1593,1604,1610,1607,32,1608,1587,1604,1605]],[[65019,65019],"disallowed_STD3_mapped",[1580,1604,32,1580,1604,1575,1604,1607]],[[65020,65020],"mapped",[1585,1740,1575,1604]],[[65021,65021],"valid",[],"NV8"],[[65022,65023],"disallowed"],[[65024,65039],"ignored"],[[65040,65040],"disallowed_STD3_mapped",[44]],[[65041,65041],"mapped",[12289]],[[65042,65042],"disallowed"],[[65043,65043],"disallowed_STD3_mapped",[58]],[[65044,65044],"disallowed_STD3_mapped",[59]],[[65045,65045],"disallowed_STD3_mapped",[33]],[[65046,65046],"disallowed_STD3_mapped",[63]],[[65047,65047],"mapped",[12310]],[[65048,65048],"mapped",[12311]],[[65049,65049],"disallowed"],[[65050,65055],"disallowed"],[[65056,65059],"valid"],[[65060,65062],"valid"],[[65063,65069],"valid"],[[65070,65071],"valid"],[[65072,65072],"disallowed"],[[65073,65073],"mapped",[8212]],[[65074,65074],"mapped",[8211]],[[65075,65076],"disallowed_STD3_mapped",[95]],[[65077,65077],"disallowed_STD3_mapped",[40]],[[65078,65078],"disallowed_STD3_mapped",[41]],[[65079,65079],"disallowed_STD3_mapped",[123]],[[65080,65080],"disallowed_STD3_mapped",[125]],[[65081,65081],"mapped",[12308]],[[65082,65082],"mapped",[12309]],[[65083,65083],"mapped",[12304]],[[65084,65084],"mapped",[12305]],[[65085,65085],"mapped",[12298]],[[65086,65086],"mapped",[12299]],[[65087,65087],"mapped",[12296]],[[65088,65088],"mapped",[12297]],[[65089,65089],"mapped",[12300]],[[65090,65090],"mapped",[12301]],[[65091,65091],"mapped",[12302]],[[65092,65092],"mapped",[12303]],[[65093,65094],"valid",[],"NV8"],[[65095,65095],"disallowed_STD3_mapped",[91]],[[65096,65096],"disallowed_STD3_mapped",[93]],[[65097,65100],"disallowed_STD3_mapped",[32,773]],[[65101,65103],"disallowed_STD3_mapped",[95]],[[65104,65104],"disallowed_STD3_mapped",[44]],[[65105,65105],"mapped",[12289]],[[65106,65106],"disallowed"],[[65107,65107],"disallowed"],[[65108,65108],"disallowed_STD3_mapped",[59]],[[65109,65109],"disallowed_STD3_mapped",[58]],[[65110,65110],"disallowed_STD3_mapped",[63]],[[65111,65111],"disallowed_STD3_mapped",[33]],[[65112,65112],"mapped",[8212]],[[65113,65113],"disallowed_STD3_mapped",[40]],[[65114,65114],"disallowed_STD3_mapped",[41]],[[65115,65115],"disallowed_STD3_mapped",[123]],[[65116,65116],"disallowed_STD3_mapped",[125]],[[65117,65117],"mapped",[12308]],[[65118,65118],"mapped",[12309]],[[65119,65119],"disallowed_STD3_mapped",[35]],[[65120,65120],"disallowed_STD3_mapped",[38]],[[65121,65121],"disallowed_STD3_mapped",[42]],[[65122,65122],"disallowed_STD3_mapped",[43]],[[65123,65123],"mapped",[45]],[[65124,65124],"disallowed_STD3_mapped",[60]],[[65125,65125],"disallowed_STD3_mapped",[62]],[[65126,65126],"disallowed_STD3_mapped",[61]],[[65127,65127],"disallowed"],[[65128,65128],"disallowed_STD3_mapped",[92]],[[65129,65129],"disallowed_STD3_mapped",[36]],[[65130,65130],"disallowed_STD3_mapped",[37]],[[65131,65131],"disallowed_STD3_mapped",[64]],[[65132,65135],"disallowed"],[[65136,65136],"disallowed_STD3_mapped",[32,1611]],[[65137,65137],"mapped",[1600,1611]],[[65138,65138],"disallowed_STD3_mapped",[32,1612]],[[65139,65139],"valid"],[[65140,65140],"disallowed_STD3_mapped",[32,1613]],[[65141,65141],"disallowed"],[[65142,65142],"disallowed_STD3_mapped",[32,1614]],[[65143,65143],"mapped",[1600,1614]],[[65144,65144],"disallowed_STD3_mapped",[32,1615]],[[65145,65145],"mapped",[1600,1615]],[[65146,65146],"disallowed_STD3_mapped",[32,1616]],[[65147,65147],"mapped",[1600,1616]],[[65148,65148],"disallowed_STD3_mapped",[32,1617]],[[65149,65149],"mapped",[1600,1617]],[[65150,65150],"disallowed_STD3_mapped",[32,1618]],[[65151,65151],"mapped",[1600,1618]],[[65152,65152],"mapped",[1569]],[[65153,65154],"mapped",[1570]],[[65155,65156],"mapped",[1571]],[[65157,65158],"mapped",[1572]],[[65159,65160],"mapped",[1573]],[[65161,65164],"mapped",[1574]],[[65165,65166],"mapped",[1575]],[[65167,65170],"mapped",[1576]],[[65171,65172],"mapped",[1577]],[[65173,65176],"mapped",[1578]],[[65177,65180],"mapped",[1579]],[[65181,65184],"mapped",[1580]],[[65185,65188],"mapped",[1581]],[[65189,65192],"mapped",[1582]],[[65193,65194],"mapped",[1583]],[[65195,65196],"mapped",[1584]],[[65197,65198],"mapped",[1585]],[[65199,65200],"mapped",[1586]],[[65201,65204],"mapped",[1587]],[[65205,65208],"mapped",[1588]],[[65209,65212],"mapped",[1589]],[[65213,65216],"mapped",[1590]],[[65217,65220],"mapped",[1591]],[[65221,65224],"mapped",[1592]],[[65225,65228],"mapped",[1593]],[[65229,65232],"mapped",[1594]],[[65233,65236],"mapped",[1601]],[[65237,65240],"mapped",[1602]],[[65241,65244],"mapped",[1603]],[[65245,65248],"mapped",[1604]],[[65249,65252],"mapped",[1605]],[[65253,65256],"mapped",[1606]],[[65257,65260],"mapped",[1607]],[[65261,65262],"mapped",[1608]],[[65263,65264],"mapped",[1609]],[[65265,65268],"mapped",[1610]],[[65269,65270],"mapped",[1604,1570]],[[65271,65272],"mapped",[1604,1571]],[[65273,65274],"mapped",[1604,1573]],[[65275,65276],"mapped",[1604,1575]],[[65277,65278],"disallowed"],[[65279,65279],"ignored"],[[65280,65280],"disallowed"],[[65281,65281],"disallowed_STD3_mapped",[33]],[[65282,65282],"disallowed_STD3_mapped",[34]],[[65283,65283],"disallowed_STD3_mapped",[35]],[[65284,65284],"disallowed_STD3_mapped",[36]],[[65285,65285],"disallowed_STD3_mapped",[37]],[[65286,65286],"disallowed_STD3_mapped",[38]],[[65287,65287],"disallowed_STD3_mapped",[39]],[[65288,65288],"disallowed_STD3_mapped",[40]],[[65289,65289],"disallowed_STD3_mapped",[41]],[[65290,65290],"disallowed_STD3_mapped",[42]],[[65291,65291],"disallowed_STD3_mapped",[43]],[[65292,65292],"disallowed_STD3_mapped",[44]],[[65293,65293],"mapped",[45]],[[65294,65294],"mapped",[46]],[[65295,65295],"disallowed_STD3_mapped",[47]],[[65296,65296],"mapped",[48]],[[65297,65297],"mapped",[49]],[[65298,65298],"mapped",[50]],[[65299,65299],"mapped",[51]],[[65300,65300],"mapped",[52]],[[65301,65301],"mapped",[53]],[[65302,65302],"mapped",[54]],[[65303,65303],"mapped",[55]],[[65304,65304],"mapped",[56]],[[65305,65305],"mapped",[57]],[[65306,65306],"disallowed_STD3_mapped",[58]],[[65307,65307],"disallowed_STD3_mapped",[59]],[[65308,65308],"disallowed_STD3_mapped",[60]],[[65309,65309],"disallowed_STD3_mapped",[61]],[[65310,65310],"disallowed_STD3_mapped",[62]],[[65311,65311],"disallowed_STD3_mapped",[63]],[[65312,65312],"disallowed_STD3_mapped",[64]],[[65313,65313],"mapped",[97]],[[65314,65314],"mapped",[98]],[[65315,65315],"mapped",[99]],[[65316,65316],"mapped",[100]],[[65317,65317],"mapped",[101]],[[65318,65318],"mapped",[102]],[[65319,65319],"mapped",[103]],[[65320,65320],"mapped",[104]],[[65321,65321],"mapped",[105]],[[65322,65322],"mapped",[106]],[[65323,65323],"mapped",[107]],[[65324,65324],"mapped",[108]],[[65325,65325],"mapped",[109]],[[65326,65326],"mapped",[110]],[[65327,65327],"mapped",[111]],[[65328,65328],"mapped",[112]],[[65329,65329],"mapped",[113]],[[65330,65330],"mapped",[114]],[[65331,65331],"mapped",[115]],[[65332,65332],"mapped",[116]],[[65333,65333],"mapped",[117]],[[65334,65334],"mapped",[118]],[[65335,65335],"mapped",[119]],[[65336,65336],"mapped",[120]],[[65337,65337],"mapped",[121]],[[65338,65338],"mapped",[122]],[[65339,65339],"disallowed_STD3_mapped",[91]],[[65340,65340],"disallowed_STD3_mapped",[92]],[[65341,65341],"disallowed_STD3_mapped",[93]],[[65342,65342],"disallowed_STD3_mapped",[94]],[[65343,65343],"disallowed_STD3_mapped",[95]],[[65344,65344],"disallowed_STD3_mapped",[96]],[[65345,65345],"mapped",[97]],[[65346,65346],"mapped",[98]],[[65347,65347],"mapped",[99]],[[65348,65348],"mapped",[100]],[[65349,65349],"mapped",[101]],[[65350,65350],"mapped",[102]],[[65351,65351],"mapped",[103]],[[65352,65352],"mapped",[104]],[[65353,65353],"mapped",[105]],[[65354,65354],"mapped",[106]],[[65355,65355],"mapped",[107]],[[65356,65356],"mapped",[108]],[[65357,65357],"mapped",[109]],[[65358,65358],"mapped",[110]],[[65359,65359],"mapped",[111]],[[65360,65360],"mapped",[112]],[[65361,65361],"mapped",[113]],[[65362,65362],"mapped",[114]],[[65363,65363],"mapped",[115]],[[65364,65364],"mapped",[116]],[[65365,65365],"mapped",[117]],[[65366,65366],"mapped",[118]],[[65367,65367],"mapped",[119]],[[65368,65368],"mapped",[120]],[[65369,65369],"mapped",[121]],[[65370,65370],"mapped",[122]],[[65371,65371],"disallowed_STD3_mapped",[123]],[[65372,65372],"disallowed_STD3_mapped",[124]],[[65373,65373],"disallowed_STD3_mapped",[125]],[[65374,65374],"disallowed_STD3_mapped",[126]],[[65375,65375],"mapped",[10629]],[[65376,65376],"mapped",[10630]],[[65377,65377],"mapped",[46]],[[65378,65378],"mapped",[12300]],[[65379,65379],"mapped",[12301]],[[65380,65380],"mapped",[12289]],[[65381,65381],"mapped",[12539]],[[65382,65382],"mapped",[12530]],[[65383,65383],"mapped",[12449]],[[65384,65384],"mapped",[12451]],[[65385,65385],"mapped",[12453]],[[65386,65386],"mapped",[12455]],[[65387,65387],"mapped",[12457]],[[65388,65388],"mapped",[12515]],[[65389,65389],"mapped",[12517]],[[65390,65390],"mapped",[12519]],[[65391,65391],"mapped",[12483]],[[65392,65392],"mapped",[12540]],[[65393,65393],"mapped",[12450]],[[65394,65394],"mapped",[12452]],[[65395,65395],"mapped",[12454]],[[65396,65396],"mapped",[12456]],[[65397,65397],"mapped",[12458]],[[65398,65398],"mapped",[12459]],[[65399,65399],"mapped",[12461]],[[65400,65400],"mapped",[12463]],[[65401,65401],"mapped",[12465]],[[65402,65402],"mapped",[12467]],[[65403,65403],"mapped",[12469]],[[65404,65404],"mapped",[12471]],[[65405,65405],"mapped",[12473]],[[65406,65406],"mapped",[12475]],[[65407,65407],"mapped",[12477]],[[65408,65408],"mapped",[12479]],[[65409,65409],"mapped",[12481]],[[65410,65410],"mapped",[12484]],[[65411,65411],"mapped",[12486]],[[65412,65412],"mapped",[12488]],[[65413,65413],"mapped",[12490]],[[65414,65414],"mapped",[12491]],[[65415,65415],"mapped",[12492]],[[65416,65416],"mapped",[12493]],[[65417,65417],"mapped",[12494]],[[65418,65418],"mapped",[12495]],[[65419,65419],"mapped",[12498]],[[65420,65420],"mapped",[12501]],[[65421,65421],"mapped",[12504]],[[65422,65422],"mapped",[12507]],[[65423,65423],"mapped",[12510]],[[65424,65424],"mapped",[12511]],[[65425,65425],"mapped",[12512]],[[65426,65426],"mapped",[12513]],[[65427,65427],"mapped",[12514]],[[65428,65428],"mapped",[12516]],[[65429,65429],"mapped",[12518]],[[65430,65430],"mapped",[12520]],[[65431,65431],"mapped",[12521]],[[65432,65432],"mapped",[12522]],[[65433,65433],"mapped",[12523]],[[65434,65434],"mapped",[12524]],[[65435,65435],"mapped",[12525]],[[65436,65436],"mapped",[12527]],[[65437,65437],"mapped",[12531]],[[65438,65438],"mapped",[12441]],[[65439,65439],"mapped",[12442]],[[65440,65440],"disallowed"],[[65441,65441],"mapped",[4352]],[[65442,65442],"mapped",[4353]],[[65443,65443],"mapped",[4522]],[[65444,65444],"mapped",[4354]],[[65445,65445],"mapped",[4524]],[[65446,65446],"mapped",[4525]],[[65447,65447],"mapped",[4355]],[[65448,65448],"mapped",[4356]],[[65449,65449],"mapped",[4357]],[[65450,65450],"mapped",[4528]],[[65451,65451],"mapped",[4529]],[[65452,65452],"mapped",[4530]],[[65453,65453],"mapped",[4531]],[[65454,65454],"mapped",[4532]],[[65455,65455],"mapped",[4533]],[[65456,65456],"mapped",[4378]],[[65457,65457],"mapped",[4358]],[[65458,65458],"mapped",[4359]],[[65459,65459],"mapped",[4360]],[[65460,65460],"mapped",[4385]],[[65461,65461],"mapped",[4361]],[[65462,65462],"mapped",[4362]],[[65463,65463],"mapped",[4363]],[[65464,65464],"mapped",[4364]],[[65465,65465],"mapped",[4365]],[[65466,65466],"mapped",[4366]],[[65467,65467],"mapped",[4367]],[[65468,65468],"mapped",[4368]],[[65469,65469],"mapped",[4369]],[[65470,65470],"mapped",[4370]],[[65471,65473],"disallowed"],[[65474,65474],"mapped",[4449]],[[65475,65475],"mapped",[4450]],[[65476,65476],"mapped",[4451]],[[65477,65477],"mapped",[4452]],[[65478,65478],"mapped",[4453]],[[65479,65479],"mapped",[4454]],[[65480,65481],"disallowed"],[[65482,65482],"mapped",[4455]],[[65483,65483],"mapped",[4456]],[[65484,65484],"mapped",[4457]],[[65485,65485],"mapped",[4458]],[[65486,65486],"mapped",[4459]],[[65487,65487],"mapped",[4460]],[[65488,65489],"disallowed"],[[65490,65490],"mapped",[4461]],[[65491,65491],"mapped",[4462]],[[65492,65492],"mapped",[4463]],[[65493,65493],"mapped",[4464]],[[65494,65494],"mapped",[4465]],[[65495,65495],"mapped",[4466]],[[65496,65497],"disallowed"],[[65498,65498],"mapped",[4467]],[[65499,65499],"mapped",[4468]],[[65500,65500],"mapped",[4469]],[[65501,65503],"disallowed"],[[65504,65504],"mapped",[162]],[[65505,65505],"mapped",[163]],[[65506,65506],"mapped",[172]],[[65507,65507],"disallowed_STD3_mapped",[32,772]],[[65508,65508],"mapped",[166]],[[65509,65509],"mapped",[165]],[[65510,65510],"mapped",[8361]],[[65511,65511],"disallowed"],[[65512,65512],"mapped",[9474]],[[65513,65513],"mapped",[8592]],[[65514,65514],"mapped",[8593]],[[65515,65515],"mapped",[8594]],[[65516,65516],"mapped",[8595]],[[65517,65517],"mapped",[9632]],[[65518,65518],"mapped",[9675]],[[65519,65528],"disallowed"],[[65529,65531],"disallowed"],[[65532,65532],"disallowed"],[[65533,65533],"disallowed"],[[65534,65535],"disallowed"],[[65536,65547],"valid"],[[65548,65548],"disallowed"],[[65549,65574],"valid"],[[65575,65575],"disallowed"],[[65576,65594],"valid"],[[65595,65595],"disallowed"],[[65596,65597],"valid"],[[65598,65598],"disallowed"],[[65599,65613],"valid"],[[65614,65615],"disallowed"],[[65616,65629],"valid"],[[65630,65663],"disallowed"],[[65664,65786],"valid"],[[65787,65791],"disallowed"],[[65792,65794],"valid",[],"NV8"],[[65795,65798],"disallowed"],[[65799,65843],"valid",[],"NV8"],[[65844,65846],"disallowed"],[[65847,65855],"valid",[],"NV8"],[[65856,65930],"valid",[],"NV8"],[[65931,65932],"valid",[],"NV8"],[[65933,65935],"disallowed"],[[65936,65947],"valid",[],"NV8"],[[65948,65951],"disallowed"],[[65952,65952],"valid",[],"NV8"],[[65953,65999],"disallowed"],[[66000,66044],"valid",[],"NV8"],[[66045,66045],"valid"],[[66046,66175],"disallowed"],[[66176,66204],"valid"],[[66205,66207],"disallowed"],[[66208,66256],"valid"],[[66257,66271],"disallowed"],[[66272,66272],"valid"],[[66273,66299],"valid",[],"NV8"],[[66300,66303],"disallowed"],[[66304,66334],"valid"],[[66335,66335],"valid"],[[66336,66339],"valid",[],"NV8"],[[66340,66351],"disallowed"],[[66352,66368],"valid"],[[66369,66369],"valid",[],"NV8"],[[66370,66377],"valid"],[[66378,66378],"valid",[],"NV8"],[[66379,66383],"disallowed"],[[66384,66426],"valid"],[[66427,66431],"disallowed"],[[66432,66461],"valid"],[[66462,66462],"disallowed"],[[66463,66463],"valid",[],"NV8"],[[66464,66499],"valid"],[[66500,66503],"disallowed"],[[66504,66511],"valid"],[[66512,66517],"valid",[],"NV8"],[[66518,66559],"disallowed"],[[66560,66560],"mapped",[66600]],[[66561,66561],"mapped",[66601]],[[66562,66562],"mapped",[66602]],[[66563,66563],"mapped",[66603]],[[66564,66564],"mapped",[66604]],[[66565,66565],"mapped",[66605]],[[66566,66566],"mapped",[66606]],[[66567,66567],"mapped",[66607]],[[66568,66568],"mapped",[66608]],[[66569,66569],"mapped",[66609]],[[66570,66570],"mapped",[66610]],[[66571,66571],"mapped",[66611]],[[66572,66572],"mapped",[66612]],[[66573,66573],"mapped",[66613]],[[66574,66574],"mapped",[66614]],[[66575,66575],"mapped",[66615]],[[66576,66576],"mapped",[66616]],[[66577,66577],"mapped",[66617]],[[66578,66578],"mapped",[66618]],[[66579,66579],"mapped",[66619]],[[66580,66580],"mapped",[66620]],[[66581,66581],"mapped",[66621]],[[66582,66582],"mapped",[66622]],[[66583,66583],"mapped",[66623]],[[66584,66584],"mapped",[66624]],[[66585,66585],"mapped",[66625]],[[66586,66586],"mapped",[66626]],[[66587,66587],"mapped",[66627]],[[66588,66588],"mapped",[66628]],[[66589,66589],"mapped",[66629]],[[66590,66590],"mapped",[66630]],[[66591,66591],"mapped",[66631]],[[66592,66592],"mapped",[66632]],[[66593,66593],"mapped",[66633]],[[66594,66594],"mapped",[66634]],[[66595,66595],"mapped",[66635]],[[66596,66596],"mapped",[66636]],[[66597,66597],"mapped",[66637]],[[66598,66598],"mapped",[66638]],[[66599,66599],"mapped",[66639]],[[66600,66637],"valid"],[[66638,66717],"valid"],[[66718,66719],"disallowed"],[[66720,66729],"valid"],[[66730,66815],"disallowed"],[[66816,66855],"valid"],[[66856,66863],"disallowed"],[[66864,66915],"valid"],[[66916,66926],"disallowed"],[[66927,66927],"valid",[],"NV8"],[[66928,67071],"disallowed"],[[67072,67382],"valid"],[[67383,67391],"disallowed"],[[67392,67413],"valid"],[[67414,67423],"disallowed"],[[67424,67431],"valid"],[[67432,67583],"disallowed"],[[67584,67589],"valid"],[[67590,67591],"disallowed"],[[67592,67592],"valid"],[[67593,67593],"disallowed"],[[67594,67637],"valid"],[[67638,67638],"disallowed"],[[67639,67640],"valid"],[[67641,67643],"disallowed"],[[67644,67644],"valid"],[[67645,67646],"disallowed"],[[67647,67647],"valid"],[[67648,67669],"valid"],[[67670,67670],"disallowed"],[[67671,67679],"valid",[],"NV8"],[[67680,67702],"valid"],[[67703,67711],"valid",[],"NV8"],[[67712,67742],"valid"],[[67743,67750],"disallowed"],[[67751,67759],"valid",[],"NV8"],[[67760,67807],"disallowed"],[[67808,67826],"valid"],[[67827,67827],"disallowed"],[[67828,67829],"valid"],[[67830,67834],"disallowed"],[[67835,67839],"valid",[],"NV8"],[[67840,67861],"valid"],[[67862,67865],"valid",[],"NV8"],[[67866,67867],"valid",[],"NV8"],[[67868,67870],"disallowed"],[[67871,67871],"valid",[],"NV8"],[[67872,67897],"valid"],[[67898,67902],"disallowed"],[[67903,67903],"valid",[],"NV8"],[[67904,67967],"disallowed"],[[67968,68023],"valid"],[[68024,68027],"disallowed"],[[68028,68029],"valid",[],"NV8"],[[68030,68031],"valid"],[[68032,68047],"valid",[],"NV8"],[[68048,68049],"disallowed"],[[68050,68095],"valid",[],"NV8"],[[68096,68099],"valid"],[[68100,68100],"disallowed"],[[68101,68102],"valid"],[[68103,68107],"disallowed"],[[68108,68115],"valid"],[[68116,68116],"disallowed"],[[68117,68119],"valid"],[[68120,68120],"disallowed"],[[68121,68147],"valid"],[[68148,68151],"disallowed"],[[68152,68154],"valid"],[[68155,68158],"disallowed"],[[68159,68159],"valid"],[[68160,68167],"valid",[],"NV8"],[[68168,68175],"disallowed"],[[68176,68184],"valid",[],"NV8"],[[68185,68191],"disallowed"],[[68192,68220],"valid"],[[68221,68223],"valid",[],"NV8"],[[68224,68252],"valid"],[[68253,68255],"valid",[],"NV8"],[[68256,68287],"disallowed"],[[68288,68295],"valid"],[[68296,68296],"valid",[],"NV8"],[[68297,68326],"valid"],[[68327,68330],"disallowed"],[[68331,68342],"valid",[],"NV8"],[[68343,68351],"disallowed"],[[68352,68405],"valid"],[[68406,68408],"disallowed"],[[68409,68415],"valid",[],"NV8"],[[68416,68437],"valid"],[[68438,68439],"disallowed"],[[68440,68447],"valid",[],"NV8"],[[68448,68466],"valid"],[[68467,68471],"disallowed"],[[68472,68479],"valid",[],"NV8"],[[68480,68497],"valid"],[[68498,68504],"disallowed"],[[68505,68508],"valid",[],"NV8"],[[68509,68520],"disallowed"],[[68521,68527],"valid",[],"NV8"],[[68528,68607],"disallowed"],[[68608,68680],"valid"],[[68681,68735],"disallowed"],[[68736,68736],"mapped",[68800]],[[68737,68737],"mapped",[68801]],[[68738,68738],"mapped",[68802]],[[68739,68739],"mapped",[68803]],[[68740,68740],"mapped",[68804]],[[68741,68741],"mapped",[68805]],[[68742,68742],"mapped",[68806]],[[68743,68743],"mapped",[68807]],[[68744,68744],"mapped",[68808]],[[68745,68745],"mapped",[68809]],[[68746,68746],"mapped",[68810]],[[68747,68747],"mapped",[68811]],[[68748,68748],"mapped",[68812]],[[68749,68749],"mapped",[68813]],[[68750,68750],"mapped",[68814]],[[68751,68751],"mapped",[68815]],[[68752,68752],"mapped",[68816]],[[68753,68753],"mapped",[68817]],[[68754,68754],"mapped",[68818]],[[68755,68755],"mapped",[68819]],[[68756,68756],"mapped",[68820]],[[68757,68757],"mapped",[68821]],[[68758,68758],"mapped",[68822]],[[68759,68759],"mapped",[68823]],[[68760,68760],"mapped",[68824]],[[68761,68761],"mapped",[68825]],[[68762,68762],"mapped",[68826]],[[68763,68763],"mapped",[68827]],[[68764,68764],"mapped",[68828]],[[68765,68765],"mapped",[68829]],[[68766,68766],"mapped",[68830]],[[68767,68767],"mapped",[68831]],[[68768,68768],"mapped",[68832]],[[68769,68769],"mapped",[68833]],[[68770,68770],"mapped",[68834]],[[68771,68771],"mapped",[68835]],[[68772,68772],"mapped",[68836]],[[68773,68773],"mapped",[68837]],[[68774,68774],"mapped",[68838]],[[68775,68775],"mapped",[68839]],[[68776,68776],"mapped",[68840]],[[68777,68777],"mapped",[68841]],[[68778,68778],"mapped",[68842]],[[68779,68779],"mapped",[68843]],[[68780,68780],"mapped",[68844]],[[68781,68781],"mapped",[68845]],[[68782,68782],"mapped",[68846]],[[68783,68783],"mapped",[68847]],[[68784,68784],"mapped",[68848]],[[68785,68785],"mapped",[68849]],[[68786,68786],"mapped",[68850]],[[68787,68799],"disallowed"],[[68800,68850],"valid"],[[68851,68857],"disallowed"],[[68858,68863],"valid",[],"NV8"],[[68864,69215],"disallowed"],[[69216,69246],"valid",[],"NV8"],[[69247,69631],"disallowed"],[[69632,69702],"valid"],[[69703,69709],"valid",[],"NV8"],[[69710,69713],"disallowed"],[[69714,69733],"valid",[],"NV8"],[[69734,69743],"valid"],[[69744,69758],"disallowed"],[[69759,69759],"valid"],[[69760,69818],"valid"],[[69819,69820],"valid",[],"NV8"],[[69821,69821],"disallowed"],[[69822,69825],"valid",[],"NV8"],[[69826,69839],"disallowed"],[[69840,69864],"valid"],[[69865,69871],"disallowed"],[[69872,69881],"valid"],[[69882,69887],"disallowed"],[[69888,69940],"valid"],[[69941,69941],"disallowed"],[[69942,69951],"valid"],[[69952,69955],"valid",[],"NV8"],[[69956,69967],"disallowed"],[[69968,70003],"valid"],[[70004,70005],"valid",[],"NV8"],[[70006,70006],"valid"],[[70007,70015],"disallowed"],[[70016,70084],"valid"],[[70085,70088],"valid",[],"NV8"],[[70089,70089],"valid",[],"NV8"],[[70090,70092],"valid"],[[70093,70093],"valid",[],"NV8"],[[70094,70095],"disallowed"],[[70096,70105],"valid"],[[70106,70106],"valid"],[[70107,70107],"valid",[],"NV8"],[[70108,70108],"valid"],[[70109,70111],"valid",[],"NV8"],[[70112,70112],"disallowed"],[[70113,70132],"valid",[],"NV8"],[[70133,70143],"disallowed"],[[70144,70161],"valid"],[[70162,70162],"disallowed"],[[70163,70199],"valid"],[[70200,70205],"valid",[],"NV8"],[[70206,70271],"disallowed"],[[70272,70278],"valid"],[[70279,70279],"disallowed"],[[70280,70280],"valid"],[[70281,70281],"disallowed"],[[70282,70285],"valid"],[[70286,70286],"disallowed"],[[70287,70301],"valid"],[[70302,70302],"disallowed"],[[70303,70312],"valid"],[[70313,70313],"valid",[],"NV8"],[[70314,70319],"disallowed"],[[70320,70378],"valid"],[[70379,70383],"disallowed"],[[70384,70393],"valid"],[[70394,70399],"disallowed"],[[70400,70400],"valid"],[[70401,70403],"valid"],[[70404,70404],"disallowed"],[[70405,70412],"valid"],[[70413,70414],"disallowed"],[[70415,70416],"valid"],[[70417,70418],"disallowed"],[[70419,70440],"valid"],[[70441,70441],"disallowed"],[[70442,70448],"valid"],[[70449,70449],"disallowed"],[[70450,70451],"valid"],[[70452,70452],"disallowed"],[[70453,70457],"valid"],[[70458,70459],"disallowed"],[[70460,70468],"valid"],[[70469,70470],"disallowed"],[[70471,70472],"valid"],[[70473,70474],"disallowed"],[[70475,70477],"valid"],[[70478,70479],"disallowed"],[[70480,70480],"valid"],[[70481,70486],"disallowed"],[[70487,70487],"valid"],[[70488,70492],"disallowed"],[[70493,70499],"valid"],[[70500,70501],"disallowed"],[[70502,70508],"valid"],[[70509,70511],"disallowed"],[[70512,70516],"valid"],[[70517,70783],"disallowed"],[[70784,70853],"valid"],[[70854,70854],"valid",[],"NV8"],[[70855,70855],"valid"],[[70856,70863],"disallowed"],[[70864,70873],"valid"],[[70874,71039],"disallowed"],[[71040,71093],"valid"],[[71094,71095],"disallowed"],[[71096,71104],"valid"],[[71105,71113],"valid",[],"NV8"],[[71114,71127],"valid",[],"NV8"],[[71128,71133],"valid"],[[71134,71167],"disallowed"],[[71168,71232],"valid"],[[71233,71235],"valid",[],"NV8"],[[71236,71236],"valid"],[[71237,71247],"disallowed"],[[71248,71257],"valid"],[[71258,71295],"disallowed"],[[71296,71351],"valid"],[[71352,71359],"disallowed"],[[71360,71369],"valid"],[[71370,71423],"disallowed"],[[71424,71449],"valid"],[[71450,71452],"disallowed"],[[71453,71467],"valid"],[[71468,71471],"disallowed"],[[71472,71481],"valid"],[[71482,71487],"valid",[],"NV8"],[[71488,71839],"disallowed"],[[71840,71840],"mapped",[71872]],[[71841,71841],"mapped",[71873]],[[71842,71842],"mapped",[71874]],[[71843,71843],"mapped",[71875]],[[71844,71844],"mapped",[71876]],[[71845,71845],"mapped",[71877]],[[71846,71846],"mapped",[71878]],[[71847,71847],"mapped",[71879]],[[71848,71848],"mapped",[71880]],[[71849,71849],"mapped",[71881]],[[71850,71850],"mapped",[71882]],[[71851,71851],"mapped",[71883]],[[71852,71852],"mapped",[71884]],[[71853,71853],"mapped",[71885]],[[71854,71854],"mapped",[71886]],[[71855,71855],"mapped",[71887]],[[71856,71856],"mapped",[71888]],[[71857,71857],"mapped",[71889]],[[71858,71858],"mapped",[71890]],[[71859,71859],"mapped",[71891]],[[71860,71860],"mapped",[71892]],[[71861,71861],"mapped",[71893]],[[71862,71862],"mapped",[71894]],[[71863,71863],"mapped",[71895]],[[71864,71864],"mapped",[71896]],[[71865,71865],"mapped",[71897]],[[71866,71866],"mapped",[71898]],[[71867,71867],"mapped",[71899]],[[71868,71868],"mapped",[71900]],[[71869,71869],"mapped",[71901]],[[71870,71870],"mapped",[71902]],[[71871,71871],"mapped",[71903]],[[71872,71913],"valid"],[[71914,71922],"valid",[],"NV8"],[[71923,71934],"disallowed"],[[71935,71935],"valid"],[[71936,72383],"disallowed"],[[72384,72440],"valid"],[[72441,73727],"disallowed"],[[73728,74606],"valid"],[[74607,74648],"valid"],[[74649,74649],"valid"],[[74650,74751],"disallowed"],[[74752,74850],"valid",[],"NV8"],[[74851,74862],"valid",[],"NV8"],[[74863,74863],"disallowed"],[[74864,74867],"valid",[],"NV8"],[[74868,74868],"valid",[],"NV8"],[[74869,74879],"disallowed"],[[74880,75075],"valid"],[[75076,77823],"disallowed"],[[77824,78894],"valid"],[[78895,82943],"disallowed"],[[82944,83526],"valid"],[[83527,92159],"disallowed"],[[92160,92728],"valid"],[[92729,92735],"disallowed"],[[92736,92766],"valid"],[[92767,92767],"disallowed"],[[92768,92777],"valid"],[[92778,92781],"disallowed"],[[92782,92783],"valid",[],"NV8"],[[92784,92879],"disallowed"],[[92880,92909],"valid"],[[92910,92911],"disallowed"],[[92912,92916],"valid"],[[92917,92917],"valid",[],"NV8"],[[92918,92927],"disallowed"],[[92928,92982],"valid"],[[92983,92991],"valid",[],"NV8"],[[92992,92995],"valid"],[[92996,92997],"valid",[],"NV8"],[[92998,93007],"disallowed"],[[93008,93017],"valid"],[[93018,93018],"disallowed"],[[93019,93025],"valid",[],"NV8"],[[93026,93026],"disallowed"],[[93027,93047],"valid"],[[93048,93052],"disallowed"],[[93053,93071],"valid"],[[93072,93951],"disallowed"],[[93952,94020],"valid"],[[94021,94031],"disallowed"],[[94032,94078],"valid"],[[94079,94094],"disallowed"],[[94095,94111],"valid"],[[94112,110591],"disallowed"],[[110592,110593],"valid"],[[110594,113663],"disallowed"],[[113664,113770],"valid"],[[113771,113775],"disallowed"],[[113776,113788],"valid"],[[113789,113791],"disallowed"],[[113792,113800],"valid"],[[113801,113807],"disallowed"],[[113808,113817],"valid"],[[113818,113819],"disallowed"],[[113820,113820],"valid",[],"NV8"],[[113821,113822],"valid"],[[113823,113823],"valid",[],"NV8"],[[113824,113827],"ignored"],[[113828,118783],"disallowed"],[[118784,119029],"valid",[],"NV8"],[[119030,119039],"disallowed"],[[119040,119078],"valid",[],"NV8"],[[119079,119080],"disallowed"],[[119081,119081],"valid",[],"NV8"],[[119082,119133],"valid",[],"NV8"],[[119134,119134],"mapped",[119127,119141]],[[119135,119135],"mapped",[119128,119141]],[[119136,119136],"mapped",[119128,119141,119150]],[[119137,119137],"mapped",[119128,119141,119151]],[[119138,119138],"mapped",[119128,119141,119152]],[[119139,119139],"mapped",[119128,119141,119153]],[[119140,119140],"mapped",[119128,119141,119154]],[[119141,119154],"valid",[],"NV8"],[[119155,119162],"disallowed"],[[119163,119226],"valid",[],"NV8"],[[119227,119227],"mapped",[119225,119141]],[[119228,119228],"mapped",[119226,119141]],[[119229,119229],"mapped",[119225,119141,119150]],[[119230,119230],"mapped",[119226,119141,119150]],[[119231,119231],"mapped",[119225,119141,119151]],[[119232,119232],"mapped",[119226,119141,119151]],[[119233,119261],"valid",[],"NV8"],[[119262,119272],"valid",[],"NV8"],[[119273,119295],"disallowed"],[[119296,119365],"valid",[],"NV8"],[[119366,119551],"disallowed"],[[119552,119638],"valid",[],"NV8"],[[119639,119647],"disallowed"],[[119648,119665],"valid",[],"NV8"],[[119666,119807],"disallowed"],[[119808,119808],"mapped",[97]],[[119809,119809],"mapped",[98]],[[119810,119810],"mapped",[99]],[[119811,119811],"mapped",[100]],[[119812,119812],"mapped",[101]],[[119813,119813],"mapped",[102]],[[119814,119814],"mapped",[103]],[[119815,119815],"mapped",[104]],[[119816,119816],"mapped",[105]],[[119817,119817],"mapped",[106]],[[119818,119818],"mapped",[107]],[[119819,119819],"mapped",[108]],[[119820,119820],"mapped",[109]],[[119821,119821],"mapped",[110]],[[119822,119822],"mapped",[111]],[[119823,119823],"mapped",[112]],[[119824,119824],"mapped",[113]],[[119825,119825],"mapped",[114]],[[119826,119826],"mapped",[115]],[[119827,119827],"mapped",[116]],[[119828,119828],"mapped",[117]],[[119829,119829],"mapped",[118]],[[119830,119830],"mapped",[119]],[[119831,119831],"mapped",[120]],[[119832,119832],"mapped",[121]],[[119833,119833],"mapped",[122]],[[119834,119834],"mapped",[97]],[[119835,119835],"mapped",[98]],[[119836,119836],"mapped",[99]],[[119837,119837],"mapped",[100]],[[119838,119838],"mapped",[101]],[[119839,119839],"mapped",[102]],[[119840,119840],"mapped",[103]],[[119841,119841],"mapped",[104]],[[119842,119842],"mapped",[105]],[[119843,119843],"mapped",[106]],[[119844,119844],"mapped",[107]],[[119845,119845],"mapped",[108]],[[119846,119846],"mapped",[109]],[[119847,119847],"mapped",[110]],[[119848,119848],"mapped",[111]],[[119849,119849],"mapped",[112]],[[119850,119850],"mapped",[113]],[[119851,119851],"mapped",[114]],[[119852,119852],"mapped",[115]],[[119853,119853],"mapped",[116]],[[119854,119854],"mapped",[117]],[[119855,119855],"mapped",[118]],[[119856,119856],"mapped",[119]],[[119857,119857],"mapped",[120]],[[119858,119858],"mapped",[121]],[[119859,119859],"mapped",[122]],[[119860,119860],"mapped",[97]],[[119861,119861],"mapped",[98]],[[119862,119862],"mapped",[99]],[[119863,119863],"mapped",[100]],[[119864,119864],"mapped",[101]],[[119865,119865],"mapped",[102]],[[119866,119866],"mapped",[103]],[[119867,119867],"mapped",[104]],[[119868,119868],"mapped",[105]],[[119869,119869],"mapped",[106]],[[119870,119870],"mapped",[107]],[[119871,119871],"mapped",[108]],[[119872,119872],"mapped",[109]],[[119873,119873],"mapped",[110]],[[119874,119874],"mapped",[111]],[[119875,119875],"mapped",[112]],[[119876,119876],"mapped",[113]],[[119877,119877],"mapped",[114]],[[119878,119878],"mapped",[115]],[[119879,119879],"mapped",[116]],[[119880,119880],"mapped",[117]],[[119881,119881],"mapped",[118]],[[119882,119882],"mapped",[119]],[[119883,119883],"mapped",[120]],[[119884,119884],"mapped",[121]],[[119885,119885],"mapped",[122]],[[119886,119886],"mapped",[97]],[[119887,119887],"mapped",[98]],[[119888,119888],"mapped",[99]],[[119889,119889],"mapped",[100]],[[119890,119890],"mapped",[101]],[[119891,119891],"mapped",[102]],[[119892,119892],"mapped",[103]],[[119893,119893],"disallowed"],[[119894,119894],"mapped",[105]],[[119895,119895],"mapped",[106]],[[119896,119896],"mapped",[107]],[[119897,119897],"mapped",[108]],[[119898,119898],"mapped",[109]],[[119899,119899],"mapped",[110]],[[119900,119900],"mapped",[111]],[[119901,119901],"mapped",[112]],[[119902,119902],"mapped",[113]],[[119903,119903],"mapped",[114]],[[119904,119904],"mapped",[115]],[[119905,119905],"mapped",[116]],[[119906,119906],"mapped",[117]],[[119907,119907],"mapped",[118]],[[119908,119908],"mapped",[119]],[[119909,119909],"mapped",[120]],[[119910,119910],"mapped",[121]],[[119911,119911],"mapped",[122]],[[119912,119912],"mapped",[97]],[[119913,119913],"mapped",[98]],[[119914,119914],"mapped",[99]],[[119915,119915],"mapped",[100]],[[119916,119916],"mapped",[101]],[[119917,119917],"mapped",[102]],[[119918,119918],"mapped",[103]],[[119919,119919],"mapped",[104]],[[119920,119920],"mapped",[105]],[[119921,119921],"mapped",[106]],[[119922,119922],"mapped",[107]],[[119923,119923],"mapped",[108]],[[119924,119924],"mapped",[109]],[[119925,119925],"mapped",[110]],[[119926,119926],"mapped",[111]],[[119927,119927],"mapped",[112]],[[119928,119928],"mapped",[113]],[[119929,119929],"mapped",[114]],[[119930,119930],"mapped",[115]],[[119931,119931],"mapped",[116]],[[119932,119932],"mapped",[117]],[[119933,119933],"mapped",[118]],[[119934,119934],"mapped",[119]],[[119935,119935],"mapped",[120]],[[119936,119936],"mapped",[121]],[[119937,119937],"mapped",[122]],[[119938,119938],"mapped",[97]],[[119939,119939],"mapped",[98]],[[119940,119940],"mapped",[99]],[[119941,119941],"mapped",[100]],[[119942,119942],"mapped",[101]],[[119943,119943],"mapped",[102]],[[119944,119944],"mapped",[103]],[[119945,119945],"mapped",[104]],[[119946,119946],"mapped",[105]],[[119947,119947],"mapped",[106]],[[119948,119948],"mapped",[107]],[[119949,119949],"mapped",[108]],[[119950,119950],"mapped",[109]],[[119951,119951],"mapped",[110]],[[119952,119952],"mapped",[111]],[[119953,119953],"mapped",[112]],[[119954,119954],"mapped",[113]],[[119955,119955],"mapped",[114]],[[119956,119956],"mapped",[115]],[[119957,119957],"mapped",[116]],[[119958,119958],"mapped",[117]],[[119959,119959],"mapped",[118]],[[119960,119960],"mapped",[119]],[[119961,119961],"mapped",[120]],[[119962,119962],"mapped",[121]],[[119963,119963],"mapped",[122]],[[119964,119964],"mapped",[97]],[[119965,119965],"disallowed"],[[119966,119966],"mapped",[99]],[[119967,119967],"mapped",[100]],[[119968,119969],"disallowed"],[[119970,119970],"mapped",[103]],[[119971,119972],"disallowed"],[[119973,119973],"mapped",[106]],[[119974,119974],"mapped",[107]],[[119975,119976],"disallowed"],[[119977,119977],"mapped",[110]],[[119978,119978],"mapped",[111]],[[119979,119979],"mapped",[112]],[[119980,119980],"mapped",[113]],[[119981,119981],"disallowed"],[[119982,119982],"mapped",[115]],[[119983,119983],"mapped",[116]],[[119984,119984],"mapped",[117]],[[119985,119985],"mapped",[118]],[[119986,119986],"mapped",[119]],[[119987,119987],"mapped",[120]],[[119988,119988],"mapped",[121]],[[119989,119989],"mapped",[122]],[[119990,119990],"mapped",[97]],[[119991,119991],"mapped",[98]],[[119992,119992],"mapped",[99]],[[119993,119993],"mapped",[100]],[[119994,119994],"disallowed"],[[119995,119995],"mapped",[102]],[[119996,119996],"disallowed"],[[119997,119997],"mapped",[104]],[[119998,119998],"mapped",[105]],[[119999,119999],"mapped",[106]],[[120000,120000],"mapped",[107]],[[120001,120001],"mapped",[108]],[[120002,120002],"mapped",[109]],[[120003,120003],"mapped",[110]],[[120004,120004],"disallowed"],[[120005,120005],"mapped",[112]],[[120006,120006],"mapped",[113]],[[120007,120007],"mapped",[114]],[[120008,120008],"mapped",[115]],[[120009,120009],"mapped",[116]],[[120010,120010],"mapped",[117]],[[120011,120011],"mapped",[118]],[[120012,120012],"mapped",[119]],[[120013,120013],"mapped",[120]],[[120014,120014],"mapped",[121]],[[120015,120015],"mapped",[122]],[[120016,120016],"mapped",[97]],[[120017,120017],"mapped",[98]],[[120018,120018],"mapped",[99]],[[120019,120019],"mapped",[100]],[[120020,120020],"mapped",[101]],[[120021,120021],"mapped",[102]],[[120022,120022],"mapped",[103]],[[120023,120023],"mapped",[104]],[[120024,120024],"mapped",[105]],[[120025,120025],"mapped",[106]],[[120026,120026],"mapped",[107]],[[120027,120027],"mapped",[108]],[[120028,120028],"mapped",[109]],[[120029,120029],"mapped",[110]],[[120030,120030],"mapped",[111]],[[120031,120031],"mapped",[112]],[[120032,120032],"mapped",[113]],[[120033,120033],"mapped",[114]],[[120034,120034],"mapped",[115]],[[120035,120035],"mapped",[116]],[[120036,120036],"mapped",[117]],[[120037,120037],"mapped",[118]],[[120038,120038],"mapped",[119]],[[120039,120039],"mapped",[120]],[[120040,120040],"mapped",[121]],[[120041,120041],"mapped",[122]],[[120042,120042],"mapped",[97]],[[120043,120043],"mapped",[98]],[[120044,120044],"mapped",[99]],[[120045,120045],"mapped",[100]],[[120046,120046],"mapped",[101]],[[120047,120047],"mapped",[102]],[[120048,120048],"mapped",[103]],[[120049,120049],"mapped",[104]],[[120050,120050],"mapped",[105]],[[120051,120051],"mapped",[106]],[[120052,120052],"mapped",[107]],[[120053,120053],"mapped",[108]],[[120054,120054],"mapped",[109]],[[120055,120055],"mapped",[110]],[[120056,120056],"mapped",[111]],[[120057,120057],"mapped",[112]],[[120058,120058],"mapped",[113]],[[120059,120059],"mapped",[114]],[[120060,120060],"mapped",[115]],[[120061,120061],"mapped",[116]],[[120062,120062],"mapped",[117]],[[120063,120063],"mapped",[118]],[[120064,120064],"mapped",[119]],[[120065,120065],"mapped",[120]],[[120066,120066],"mapped",[121]],[[120067,120067],"mapped",[122]],[[120068,120068],"mapped",[97]],[[120069,120069],"mapped",[98]],[[120070,120070],"disallowed"],[[120071,120071],"mapped",[100]],[[120072,120072],"mapped",[101]],[[120073,120073],"mapped",[102]],[[120074,120074],"mapped",[103]],[[120075,120076],"disallowed"],[[120077,120077],"mapped",[106]],[[120078,120078],"mapped",[107]],[[120079,120079],"mapped",[108]],[[120080,120080],"mapped",[109]],[[120081,120081],"mapped",[110]],[[120082,120082],"mapped",[111]],[[120083,120083],"mapped",[112]],[[120084,120084],"mapped",[113]],[[120085,120085],"disallowed"],[[120086,120086],"mapped",[115]],[[120087,120087],"mapped",[116]],[[120088,120088],"mapped",[117]],[[120089,120089],"mapped",[118]],[[120090,120090],"mapped",[119]],[[120091,120091],"mapped",[120]],[[120092,120092],"mapped",[121]],[[120093,120093],"disallowed"],[[120094,120094],"mapped",[97]],[[120095,120095],"mapped",[98]],[[120096,120096],"mapped",[99]],[[120097,120097],"mapped",[100]],[[120098,120098],"mapped",[101]],[[120099,120099],"mapped",[102]],[[120100,120100],"mapped",[103]],[[120101,120101],"mapped",[104]],[[120102,120102],"mapped",[105]],[[120103,120103],"mapped",[106]],[[120104,120104],"mapped",[107]],[[120105,120105],"mapped",[108]],[[120106,120106],"mapped",[109]],[[120107,120107],"mapped",[110]],[[120108,120108],"mapped",[111]],[[120109,120109],"mapped",[112]],[[120110,120110],"mapped",[113]],[[120111,120111],"mapped",[114]],[[120112,120112],"mapped",[115]],[[120113,120113],"mapped",[116]],[[120114,120114],"mapped",[117]],[[120115,120115],"mapped",[118]],[[120116,120116],"mapped",[119]],[[120117,120117],"mapped",[120]],[[120118,120118],"mapped",[121]],[[120119,120119],"mapped",[122]],[[120120,120120],"mapped",[97]],[[120121,120121],"mapped",[98]],[[120122,120122],"disallowed"],[[120123,120123],"mapped",[100]],[[120124,120124],"mapped",[101]],[[120125,120125],"mapped",[102]],[[120126,120126],"mapped",[103]],[[120127,120127],"disallowed"],[[120128,120128],"mapped",[105]],[[120129,120129],"mapped",[106]],[[120130,120130],"mapped",[107]],[[120131,120131],"mapped",[108]],[[120132,120132],"mapped",[109]],[[120133,120133],"disallowed"],[[120134,120134],"mapped",[111]],[[120135,120137],"disallowed"],[[120138,120138],"mapped",[115]],[[120139,120139],"mapped",[116]],[[120140,120140],"mapped",[117]],[[120141,120141],"mapped",[118]],[[120142,120142],"mapped",[119]],[[120143,120143],"mapped",[120]],[[120144,120144],"mapped",[121]],[[120145,120145],"disallowed"],[[120146,120146],"mapped",[97]],[[120147,120147],"mapped",[98]],[[120148,120148],"mapped",[99]],[[120149,120149],"mapped",[100]],[[120150,120150],"mapped",[101]],[[120151,120151],"mapped",[102]],[[120152,120152],"mapped",[103]],[[120153,120153],"mapped",[104]],[[120154,120154],"mapped",[105]],[[120155,120155],"mapped",[106]],[[120156,120156],"mapped",[107]],[[120157,120157],"mapped",[108]],[[120158,120158],"mapped",[109]],[[120159,120159],"mapped",[110]],[[120160,120160],"mapped",[111]],[[120161,120161],"mapped",[112]],[[120162,120162],"mapped",[113]],[[120163,120163],"mapped",[114]],[[120164,120164],"mapped",[115]],[[120165,120165],"mapped",[116]],[[120166,120166],"mapped",[117]],[[120167,120167],"mapped",[118]],[[120168,120168],"mapped",[119]],[[120169,120169],"mapped",[120]],[[120170,120170],"mapped",[121]],[[120171,120171],"mapped",[122]],[[120172,120172],"mapped",[97]],[[120173,120173],"mapped",[98]],[[120174,120174],"mapped",[99]],[[120175,120175],"mapped",[100]],[[120176,120176],"mapped",[101]],[[120177,120177],"mapped",[102]],[[120178,120178],"mapped",[103]],[[120179,120179],"mapped",[104]],[[120180,120180],"mapped",[105]],[[120181,120181],"mapped",[106]],[[120182,120182],"mapped",[107]],[[120183,120183],"mapped",[108]],[[120184,120184],"mapped",[109]],[[120185,120185],"mapped",[110]],[[120186,120186],"mapped",[111]],[[120187,120187],"mapped",[112]],[[120188,120188],"mapped",[113]],[[120189,120189],"mapped",[114]],[[120190,120190],"mapped",[115]],[[120191,120191],"mapped",[116]],[[120192,120192],"mapped",[117]],[[120193,120193],"mapped",[118]],[[120194,120194],"mapped",[119]],[[120195,120195],"mapped",[120]],[[120196,120196],"mapped",[121]],[[120197,120197],"mapped",[122]],[[120198,120198],"mapped",[97]],[[120199,120199],"mapped",[98]],[[120200,120200],"mapped",[99]],[[120201,120201],"mapped",[100]],[[120202,120202],"mapped",[101]],[[120203,120203],"mapped",[102]],[[120204,120204],"mapped",[103]],[[120205,120205],"mapped",[104]],[[120206,120206],"mapped",[105]],[[120207,120207],"mapped",[106]],[[120208,120208],"mapped",[107]],[[120209,120209],"mapped",[108]],[[120210,120210],"mapped",[109]],[[120211,120211],"mapped",[110]],[[120212,120212],"mapped",[111]],[[120213,120213],"mapped",[112]],[[120214,120214],"mapped",[113]],[[120215,120215],"mapped",[114]],[[120216,120216],"mapped",[115]],[[120217,120217],"mapped",[116]],[[120218,120218],"mapped",[117]],[[120219,120219],"mapped",[118]],[[120220,120220],"mapped",[119]],[[120221,120221],"mapped",[120]],[[120222,120222],"mapped",[121]],[[120223,120223],"mapped",[122]],[[120224,120224],"mapped",[97]],[[120225,120225],"mapped",[98]],[[120226,120226],"mapped",[99]],[[120227,120227],"mapped",[100]],[[120228,120228],"mapped",[101]],[[120229,120229],"mapped",[102]],[[120230,120230],"mapped",[103]],[[120231,120231],"mapped",[104]],[[120232,120232],"mapped",[105]],[[120233,120233],"mapped",[106]],[[120234,120234],"mapped",[107]],[[120235,120235],"mapped",[108]],[[120236,120236],"mapped",[109]],[[120237,120237],"mapped",[110]],[[120238,120238],"mapped",[111]],[[120239,120239],"mapped",[112]],[[120240,120240],"mapped",[113]],[[120241,120241],"mapped",[114]],[[120242,120242],"mapped",[115]],[[120243,120243],"mapped",[116]],[[120244,120244],"mapped",[117]],[[120245,120245],"mapped",[118]],[[120246,120246],"mapped",[119]],[[120247,120247],"mapped",[120]],[[120248,120248],"mapped",[121]],[[120249,120249],"mapped",[122]],[[120250,120250],"mapped",[97]],[[120251,120251],"mapped",[98]],[[120252,120252],"mapped",[99]],[[120253,120253],"mapped",[100]],[[120254,120254],"mapped",[101]],[[120255,120255],"mapped",[102]],[[120256,120256],"mapped",[103]],[[120257,120257],"mapped",[104]],[[120258,120258],"mapped",[105]],[[120259,120259],"mapped",[106]],[[120260,120260],"mapped",[107]],[[120261,120261],"mapped",[108]],[[120262,120262],"mapped",[109]],[[120263,120263],"mapped",[110]],[[120264,120264],"mapped",[111]],[[120265,120265],"mapped",[112]],[[120266,120266],"mapped",[113]],[[120267,120267],"mapped",[114]],[[120268,120268],"mapped",[115]],[[120269,120269],"mapped",[116]],[[120270,120270],"mapped",[117]],[[120271,120271],"mapped",[118]],[[120272,120272],"mapped",[119]],[[120273,120273],"mapped",[120]],[[120274,120274],"mapped",[121]],[[120275,120275],"mapped",[122]],[[120276,120276],"mapped",[97]],[[120277,120277],"mapped",[98]],[[120278,120278],"mapped",[99]],[[120279,120279],"mapped",[100]],[[120280,120280],"mapped",[101]],[[120281,120281],"mapped",[102]],[[120282,120282],"mapped",[103]],[[120283,120283],"mapped",[104]],[[120284,120284],"mapped",[105]],[[120285,120285],"mapped",[106]],[[120286,120286],"mapped",[107]],[[120287,120287],"mapped",[108]],[[120288,120288],"mapped",[109]],[[120289,120289],"mapped",[110]],[[120290,120290],"mapped",[111]],[[120291,120291],"mapped",[112]],[[120292,120292],"mapped",[113]],[[120293,120293],"mapped",[114]],[[120294,120294],"mapped",[115]],[[120295,120295],"mapped",[116]],[[120296,120296],"mapped",[117]],[[120297,120297],"mapped",[118]],[[120298,120298],"mapped",[119]],[[120299,120299],"mapped",[120]],[[120300,120300],"mapped",[121]],[[120301,120301],"mapped",[122]],[[120302,120302],"mapped",[97]],[[120303,120303],"mapped",[98]],[[120304,120304],"mapped",[99]],[[120305,120305],"mapped",[100]],[[120306,120306],"mapped",[101]],[[120307,120307],"mapped",[102]],[[120308,120308],"mapped",[103]],[[120309,120309],"mapped",[104]],[[120310,120310],"mapped",[105]],[[120311,120311],"mapped",[106]],[[120312,120312],"mapped",[107]],[[120313,120313],"mapped",[108]],[[120314,120314],"mapped",[109]],[[120315,120315],"mapped",[110]],[[120316,120316],"mapped",[111]],[[120317,120317],"mapped",[112]],[[120318,120318],"mapped",[113]],[[120319,120319],"mapped",[114]],[[120320,120320],"mapped",[115]],[[120321,120321],"mapped",[116]],[[120322,120322],"mapped",[117]],[[120323,120323],"mapped",[118]],[[120324,120324],"mapped",[119]],[[120325,120325],"mapped",[120]],[[120326,120326],"mapped",[121]],[[120327,120327],"mapped",[122]],[[120328,120328],"mapped",[97]],[[120329,120329],"mapped",[98]],[[120330,120330],"mapped",[99]],[[120331,120331],"mapped",[100]],[[120332,120332],"mapped",[101]],[[120333,120333],"mapped",[102]],[[120334,120334],"mapped",[103]],[[120335,120335],"mapped",[104]],[[120336,120336],"mapped",[105]],[[120337,120337],"mapped",[106]],[[120338,120338],"mapped",[107]],[[120339,120339],"mapped",[108]],[[120340,120340],"mapped",[109]],[[120341,120341],"mapped",[110]],[[120342,120342],"mapped",[111]],[[120343,120343],"mapped",[112]],[[120344,120344],"mapped",[113]],[[120345,120345],"mapped",[114]],[[120346,120346],"mapped",[115]],[[120347,120347],"mapped",[116]],[[120348,120348],"mapped",[117]],[[120349,120349],"mapped",[118]],[[120350,120350],"mapped",[119]],[[120351,120351],"mapped",[120]],[[120352,120352],"mapped",[121]],[[120353,120353],"mapped",[122]],[[120354,120354],"mapped",[97]],[[120355,120355],"mapped",[98]],[[120356,120356],"mapped",[99]],[[120357,120357],"mapped",[100]],[[120358,120358],"mapped",[101]],[[120359,120359],"mapped",[102]],[[120360,120360],"mapped",[103]],[[120361,120361],"mapped",[104]],[[120362,120362],"mapped",[105]],[[120363,120363],"mapped",[106]],[[120364,120364],"mapped",[107]],[[120365,120365],"mapped",[108]],[[120366,120366],"mapped",[109]],[[120367,120367],"mapped",[110]],[[120368,120368],"mapped",[111]],[[120369,120369],"mapped",[112]],[[120370,120370],"mapped",[113]],[[120371,120371],"mapped",[114]],[[120372,120372],"mapped",[115]],[[120373,120373],"mapped",[116]],[[120374,120374],"mapped",[117]],[[120375,120375],"mapped",[118]],[[120376,120376],"mapped",[119]],[[120377,120377],"mapped",[120]],[[120378,120378],"mapped",[121]],[[120379,120379],"mapped",[122]],[[120380,120380],"mapped",[97]],[[120381,120381],"mapped",[98]],[[120382,120382],"mapped",[99]],[[120383,120383],"mapped",[100]],[[120384,120384],"mapped",[101]],[[120385,120385],"mapped",[102]],[[120386,120386],"mapped",[103]],[[120387,120387],"mapped",[104]],[[120388,120388],"mapped",[105]],[[120389,120389],"mapped",[106]],[[120390,120390],"mapped",[107]],[[120391,120391],"mapped",[108]],[[120392,120392],"mapped",[109]],[[120393,120393],"mapped",[110]],[[120394,120394],"mapped",[111]],[[120395,120395],"mapped",[112]],[[120396,120396],"mapped",[113]],[[120397,120397],"mapped",[114]],[[120398,120398],"mapped",[115]],[[120399,120399],"mapped",[116]],[[120400,120400],"mapped",[117]],[[120401,120401],"mapped",[118]],[[120402,120402],"mapped",[119]],[[120403,120403],"mapped",[120]],[[120404,120404],"mapped",[121]],[[120405,120405],"mapped",[122]],[[120406,120406],"mapped",[97]],[[120407,120407],"mapped",[98]],[[120408,120408],"mapped",[99]],[[120409,120409],"mapped",[100]],[[120410,120410],"mapped",[101]],[[120411,120411],"mapped",[102]],[[120412,120412],"mapped",[103]],[[120413,120413],"mapped",[104]],[[120414,120414],"mapped",[105]],[[120415,120415],"mapped",[106]],[[120416,120416],"mapped",[107]],[[120417,120417],"mapped",[108]],[[120418,120418],"mapped",[109]],[[120419,120419],"mapped",[110]],[[120420,120420],"mapped",[111]],[[120421,120421],"mapped",[112]],[[120422,120422],"mapped",[113]],[[120423,120423],"mapped",[114]],[[120424,120424],"mapped",[115]],[[120425,120425],"mapped",[116]],[[120426,120426],"mapped",[117]],[[120427,120427],"mapped",[118]],[[120428,120428],"mapped",[119]],[[120429,120429],"mapped",[120]],[[120430,120430],"mapped",[121]],[[120431,120431],"mapped",[122]],[[120432,120432],"mapped",[97]],[[120433,120433],"mapped",[98]],[[120434,120434],"mapped",[99]],[[120435,120435],"mapped",[100]],[[120436,120436],"mapped",[101]],[[120437,120437],"mapped",[102]],[[120438,120438],"mapped",[103]],[[120439,120439],"mapped",[104]],[[120440,120440],"mapped",[105]],[[120441,120441],"mapped",[106]],[[120442,120442],"mapped",[107]],[[120443,120443],"mapped",[108]],[[120444,120444],"mapped",[109]],[[120445,120445],"mapped",[110]],[[120446,120446],"mapped",[111]],[[120447,120447],"mapped",[112]],[[120448,120448],"mapped",[113]],[[120449,120449],"mapped",[114]],[[120450,120450],"mapped",[115]],[[120451,120451],"mapped",[116]],[[120452,120452],"mapped",[117]],[[120453,120453],"mapped",[118]],[[120454,120454],"mapped",[119]],[[120455,120455],"mapped",[120]],[[120456,120456],"mapped",[121]],[[120457,120457],"mapped",[122]],[[120458,120458],"mapped",[97]],[[120459,120459],"mapped",[98]],[[120460,120460],"mapped",[99]],[[120461,120461],"mapped",[100]],[[120462,120462],"mapped",[101]],[[120463,120463],"mapped",[102]],[[120464,120464],"mapped",[103]],[[120465,120465],"mapped",[104]],[[120466,120466],"mapped",[105]],[[120467,120467],"mapped",[106]],[[120468,120468],"mapped",[107]],[[120469,120469],"mapped",[108]],[[120470,120470],"mapped",[109]],[[120471,120471],"mapped",[110]],[[120472,120472],"mapped",[111]],[[120473,120473],"mapped",[112]],[[120474,120474],"mapped",[113]],[[120475,120475],"mapped",[114]],[[120476,120476],"mapped",[115]],[[120477,120477],"mapped",[116]],[[120478,120478],"mapped",[117]],[[120479,120479],"mapped",[118]],[[120480,120480],"mapped",[119]],[[120481,120481],"mapped",[120]],[[120482,120482],"mapped",[121]],[[120483,120483],"mapped",[122]],[[120484,120484],"mapped",[305]],[[120485,120485],"mapped",[567]],[[120486,120487],"disallowed"],[[120488,120488],"mapped",[945]],[[120489,120489],"mapped",[946]],[[120490,120490],"mapped",[947]],[[120491,120491],"mapped",[948]],[[120492,120492],"mapped",[949]],[[120493,120493],"mapped",[950]],[[120494,120494],"mapped",[951]],[[120495,120495],"mapped",[952]],[[120496,120496],"mapped",[953]],[[120497,120497],"mapped",[954]],[[120498,120498],"mapped",[955]],[[120499,120499],"mapped",[956]],[[120500,120500],"mapped",[957]],[[120501,120501],"mapped",[958]],[[120502,120502],"mapped",[959]],[[120503,120503],"mapped",[960]],[[120504,120504],"mapped",[961]],[[120505,120505],"mapped",[952]],[[120506,120506],"mapped",[963]],[[120507,120507],"mapped",[964]],[[120508,120508],"mapped",[965]],[[120509,120509],"mapped",[966]],[[120510,120510],"mapped",[967]],[[120511,120511],"mapped",[968]],[[120512,120512],"mapped",[969]],[[120513,120513],"mapped",[8711]],[[120514,120514],"mapped",[945]],[[120515,120515],"mapped",[946]],[[120516,120516],"mapped",[947]],[[120517,120517],"mapped",[948]],[[120518,120518],"mapped",[949]],[[120519,120519],"mapped",[950]],[[120520,120520],"mapped",[951]],[[120521,120521],"mapped",[952]],[[120522,120522],"mapped",[953]],[[120523,120523],"mapped",[954]],[[120524,120524],"mapped",[955]],[[120525,120525],"mapped",[956]],[[120526,120526],"mapped",[957]],[[120527,120527],"mapped",[958]],[[120528,120528],"mapped",[959]],[[120529,120529],"mapped",[960]],[[120530,120530],"mapped",[961]],[[120531,120532],"mapped",[963]],[[120533,120533],"mapped",[964]],[[120534,120534],"mapped",[965]],[[120535,120535],"mapped",[966]],[[120536,120536],"mapped",[967]],[[120537,120537],"mapped",[968]],[[120538,120538],"mapped",[969]],[[120539,120539],"mapped",[8706]],[[120540,120540],"mapped",[949]],[[120541,120541],"mapped",[952]],[[120542,120542],"mapped",[954]],[[120543,120543],"mapped",[966]],[[120544,120544],"mapped",[961]],[[120545,120545],"mapped",[960]],[[120546,120546],"mapped",[945]],[[120547,120547],"mapped",[946]],[[120548,120548],"mapped",[947]],[[120549,120549],"mapped",[948]],[[120550,120550],"mapped",[949]],[[120551,120551],"mapped",[950]],[[120552,120552],"mapped",[951]],[[120553,120553],"mapped",[952]],[[120554,120554],"mapped",[953]],[[120555,120555],"mapped",[954]],[[120556,120556],"mapped",[955]],[[120557,120557],"mapped",[956]],[[120558,120558],"mapped",[957]],[[120559,120559],"mapped",[958]],[[120560,120560],"mapped",[959]],[[120561,120561],"mapped",[960]],[[120562,120562],"mapped",[961]],[[120563,120563],"mapped",[952]],[[120564,120564],"mapped",[963]],[[120565,120565],"mapped",[964]],[[120566,120566],"mapped",[965]],[[120567,120567],"mapped",[966]],[[120568,120568],"mapped",[967]],[[120569,120569],"mapped",[968]],[[120570,120570],"mapped",[969]],[[120571,120571],"mapped",[8711]],[[120572,120572],"mapped",[945]],[[120573,120573],"mapped",[946]],[[120574,120574],"mapped",[947]],[[120575,120575],"mapped",[948]],[[120576,120576],"mapped",[949]],[[120577,120577],"mapped",[950]],[[120578,120578],"mapped",[951]],[[120579,120579],"mapped",[952]],[[120580,120580],"mapped",[953]],[[120581,120581],"mapped",[954]],[[120582,120582],"mapped",[955]],[[120583,120583],"mapped",[956]],[[120584,120584],"mapped",[957]],[[120585,120585],"mapped",[958]],[[120586,120586],"mapped",[959]],[[120587,120587],"mapped",[960]],[[120588,120588],"mapped",[961]],[[120589,120590],"mapped",[963]],[[120591,120591],"mapped",[964]],[[120592,120592],"mapped",[965]],[[120593,120593],"mapped",[966]],[[120594,120594],"mapped",[967]],[[120595,120595],"mapped",[968]],[[120596,120596],"mapped",[969]],[[120597,120597],"mapped",[8706]],[[120598,120598],"mapped",[949]],[[120599,120599],"mapped",[952]],[[120600,120600],"mapped",[954]],[[120601,120601],"mapped",[966]],[[120602,120602],"mapped",[961]],[[120603,120603],"mapped",[960]],[[120604,120604],"mapped",[945]],[[120605,120605],"mapped",[946]],[[120606,120606],"mapped",[947]],[[120607,120607],"mapped",[948]],[[120608,120608],"mapped",[949]],[[120609,120609],"mapped",[950]],[[120610,120610],"mapped",[951]],[[120611,120611],"mapped",[952]],[[120612,120612],"mapped",[953]],[[120613,120613],"mapped",[954]],[[120614,120614],"mapped",[955]],[[120615,120615],"mapped",[956]],[[120616,120616],"mapped",[957]],[[120617,120617],"mapped",[958]],[[120618,120618],"mapped",[959]],[[120619,120619],"mapped",[960]],[[120620,120620],"mapped",[961]],[[120621,120621],"mapped",[952]],[[120622,120622],"mapped",[963]],[[120623,120623],"mapped",[964]],[[120624,120624],"mapped",[965]],[[120625,120625],"mapped",[966]],[[120626,120626],"mapped",[967]],[[120627,120627],"mapped",[968]],[[120628,120628],"mapped",[969]],[[120629,120629],"mapped",[8711]],[[120630,120630],"mapped",[945]],[[120631,120631],"mapped",[946]],[[120632,120632],"mapped",[947]],[[120633,120633],"mapped",[948]],[[120634,120634],"mapped",[949]],[[120635,120635],"mapped",[950]],[[120636,120636],"mapped",[951]],[[120637,120637],"mapped",[952]],[[120638,120638],"mapped",[953]],[[120639,120639],"mapped",[954]],[[120640,120640],"mapped",[955]],[[120641,120641],"mapped",[956]],[[120642,120642],"mapped",[957]],[[120643,120643],"mapped",[958]],[[120644,120644],"mapped",[959]],[[120645,120645],"mapped",[960]],[[120646,120646],"mapped",[961]],[[120647,120648],"mapped",[963]],[[120649,120649],"mapped",[964]],[[120650,120650],"mapped",[965]],[[120651,120651],"mapped",[966]],[[120652,120652],"mapped",[967]],[[120653,120653],"mapped",[968]],[[120654,120654],"mapped",[969]],[[120655,120655],"mapped",[8706]],[[120656,120656],"mapped",[949]],[[120657,120657],"mapped",[952]],[[120658,120658],"mapped",[954]],[[120659,120659],"mapped",[966]],[[120660,120660],"mapped",[961]],[[120661,120661],"mapped",[960]],[[120662,120662],"mapped",[945]],[[120663,120663],"mapped",[946]],[[120664,120664],"mapped",[947]],[[120665,120665],"mapped",[948]],[[120666,120666],"mapped",[949]],[[120667,120667],"mapped",[950]],[[120668,120668],"mapped",[951]],[[120669,120669],"mapped",[952]],[[120670,120670],"mapped",[953]],[[120671,120671],"mapped",[954]],[[120672,120672],"mapped",[955]],[[120673,120673],"mapped",[956]],[[120674,120674],"mapped",[957]],[[120675,120675],"mapped",[958]],[[120676,120676],"mapped",[959]],[[120677,120677],"mapped",[960]],[[120678,120678],"mapped",[961]],[[120679,120679],"mapped",[952]],[[120680,120680],"mapped",[963]],[[120681,120681],"mapped",[964]],[[120682,120682],"mapped",[965]],[[120683,120683],"mapped",[966]],[[120684,120684],"mapped",[967]],[[120685,120685],"mapped",[968]],[[120686,120686],"mapped",[969]],[[120687,120687],"mapped",[8711]],[[120688,120688],"mapped",[945]],[[120689,120689],"mapped",[946]],[[120690,120690],"mapped",[947]],[[120691,120691],"mapped",[948]],[[120692,120692],"mapped",[949]],[[120693,120693],"mapped",[950]],[[120694,120694],"mapped",[951]],[[120695,120695],"mapped",[952]],[[120696,120696],"mapped",[953]],[[120697,120697],"mapped",[954]],[[120698,120698],"mapped",[955]],[[120699,120699],"mapped",[956]],[[120700,120700],"mapped",[957]],[[120701,120701],"mapped",[958]],[[120702,120702],"mapped",[959]],[[120703,120703],"mapped",[960]],[[120704,120704],"mapped",[961]],[[120705,120706],"mapped",[963]],[[120707,120707],"mapped",[964]],[[120708,120708],"mapped",[965]],[[120709,120709],"mapped",[966]],[[120710,120710],"mapped",[967]],[[120711,120711],"mapped",[968]],[[120712,120712],"mapped",[969]],[[120713,120713],"mapped",[8706]],[[120714,120714],"mapped",[949]],[[120715,120715],"mapped",[952]],[[120716,120716],"mapped",[954]],[[120717,120717],"mapped",[966]],[[120718,120718],"mapped",[961]],[[120719,120719],"mapped",[960]],[[120720,120720],"mapped",[945]],[[120721,120721],"mapped",[946]],[[120722,120722],"mapped",[947]],[[120723,120723],"mapped",[948]],[[120724,120724],"mapped",[949]],[[120725,120725],"mapped",[950]],[[120726,120726],"mapped",[951]],[[120727,120727],"mapped",[952]],[[120728,120728],"mapped",[953]],[[120729,120729],"mapped",[954]],[[120730,120730],"mapped",[955]],[[120731,120731],"mapped",[956]],[[120732,120732],"mapped",[957]],[[120733,120733],"mapped",[958]],[[120734,120734],"mapped",[959]],[[120735,120735],"mapped",[960]],[[120736,120736],"mapped",[961]],[[120737,120737],"mapped",[952]],[[120738,120738],"mapped",[963]],[[120739,120739],"mapped",[964]],[[120740,120740],"mapped",[965]],[[120741,120741],"mapped",[966]],[[120742,120742],"mapped",[967]],[[120743,120743],"mapped",[968]],[[120744,120744],"mapped",[969]],[[120745,120745],"mapped",[8711]],[[120746,120746],"mapped",[945]],[[120747,120747],"mapped",[946]],[[120748,120748],"mapped",[947]],[[120749,120749],"mapped",[948]],[[120750,120750],"mapped",[949]],[[120751,120751],"mapped",[950]],[[120752,120752],"mapped",[951]],[[120753,120753],"mapped",[952]],[[120754,120754],"mapped",[953]],[[120755,120755],"mapped",[954]],[[120756,120756],"mapped",[955]],[[120757,120757],"mapped",[956]],[[120758,120758],"mapped",[957]],[[120759,120759],"mapped",[958]],[[120760,120760],"mapped",[959]],[[120761,120761],"mapped",[960]],[[120762,120762],"mapped",[961]],[[120763,120764],"mapped",[963]],[[120765,120765],"mapped",[964]],[[120766,120766],"mapped",[965]],[[120767,120767],"mapped",[966]],[[120768,120768],"mapped",[967]],[[120769,120769],"mapped",[968]],[[120770,120770],"mapped",[969]],[[120771,120771],"mapped",[8706]],[[120772,120772],"mapped",[949]],[[120773,120773],"mapped",[952]],[[120774,120774],"mapped",[954]],[[120775,120775],"mapped",[966]],[[120776,120776],"mapped",[961]],[[120777,120777],"mapped",[960]],[[120778,120779],"mapped",[989]],[[120780,120781],"disallowed"],[[120782,120782],"mapped",[48]],[[120783,120783],"mapped",[49]],[[120784,120784],"mapped",[50]],[[120785,120785],"mapped",[51]],[[120786,120786],"mapped",[52]],[[120787,120787],"mapped",[53]],[[120788,120788],"mapped",[54]],[[120789,120789],"mapped",[55]],[[120790,120790],"mapped",[56]],[[120791,120791],"mapped",[57]],[[120792,120792],"mapped",[48]],[[120793,120793],"mapped",[49]],[[120794,120794],"mapped",[50]],[[120795,120795],"mapped",[51]],[[120796,120796],"mapped",[52]],[[120797,120797],"mapped",[53]],[[120798,120798],"mapped",[54]],[[120799,120799],"mapped",[55]],[[120800,120800],"mapped",[56]],[[120801,120801],"mapped",[57]],[[120802,120802],"mapped",[48]],[[120803,120803],"mapped",[49]],[[120804,120804],"mapped",[50]],[[120805,120805],"mapped",[51]],[[120806,120806],"mapped",[52]],[[120807,120807],"mapped",[53]],[[120808,120808],"mapped",[54]],[[120809,120809],"mapped",[55]],[[120810,120810],"mapped",[56]],[[120811,120811],"mapped",[57]],[[120812,120812],"mapped",[48]],[[120813,120813],"mapped",[49]],[[120814,120814],"mapped",[50]],[[120815,120815],"mapped",[51]],[[120816,120816],"mapped",[52]],[[120817,120817],"mapped",[53]],[[120818,120818],"mapped",[54]],[[120819,120819],"mapped",[55]],[[120820,120820],"mapped",[56]],[[120821,120821],"mapped",[57]],[[120822,120822],"mapped",[48]],[[120823,120823],"mapped",[49]],[[120824,120824],"mapped",[50]],[[120825,120825],"mapped",[51]],[[120826,120826],"mapped",[52]],[[120827,120827],"mapped",[53]],[[120828,120828],"mapped",[54]],[[120829,120829],"mapped",[55]],[[120830,120830],"mapped",[56]],[[120831,120831],"mapped",[57]],[[120832,121343],"valid",[],"NV8"],[[121344,121398],"valid"],[[121399,121402],"valid",[],"NV8"],[[121403,121452],"valid"],[[121453,121460],"valid",[],"NV8"],[[121461,121461],"valid"],[[121462,121475],"valid",[],"NV8"],[[121476,121476],"valid"],[[121477,121483],"valid",[],"NV8"],[[121484,121498],"disallowed"],[[121499,121503],"valid"],[[121504,121504],"disallowed"],[[121505,121519],"valid"],[[121520,124927],"disallowed"],[[124928,125124],"valid"],[[125125,125126],"disallowed"],[[125127,125135],"valid",[],"NV8"],[[125136,125142],"valid"],[[125143,126463],"disallowed"],[[126464,126464],"mapped",[1575]],[[126465,126465],"mapped",[1576]],[[126466,126466],"mapped",[1580]],[[126467,126467],"mapped",[1583]],[[126468,126468],"disallowed"],[[126469,126469],"mapped",[1608]],[[126470,126470],"mapped",[1586]],[[126471,126471],"mapped",[1581]],[[126472,126472],"mapped",[1591]],[[126473,126473],"mapped",[1610]],[[126474,126474],"mapped",[1603]],[[126475,126475],"mapped",[1604]],[[126476,126476],"mapped",[1605]],[[126477,126477],"mapped",[1606]],[[126478,126478],"mapped",[1587]],[[126479,126479],"mapped",[1593]],[[126480,126480],"mapped",[1601]],[[126481,126481],"mapped",[1589]],[[126482,126482],"mapped",[1602]],[[126483,126483],"mapped",[1585]],[[126484,126484],"mapped",[1588]],[[126485,126485],"mapped",[1578]],[[126486,126486],"mapped",[1579]],[[126487,126487],"mapped",[1582]],[[126488,126488],"mapped",[1584]],[[126489,126489],"mapped",[1590]],[[126490,126490],"mapped",[1592]],[[126491,126491],"mapped",[1594]],[[126492,126492],"mapped",[1646]],[[126493,126493],"mapped",[1722]],[[126494,126494],"mapped",[1697]],[[126495,126495],"mapped",[1647]],[[126496,126496],"disallowed"],[[126497,126497],"mapped",[1576]],[[126498,126498],"mapped",[1580]],[[126499,126499],"disallowed"],[[126500,126500],"mapped",[1607]],[[126501,126502],"disallowed"],[[126503,126503],"mapped",[1581]],[[126504,126504],"disallowed"],[[126505,126505],"mapped",[1610]],[[126506,126506],"mapped",[1603]],[[126507,126507],"mapped",[1604]],[[126508,126508],"mapped",[1605]],[[126509,126509],"mapped",[1606]],[[126510,126510],"mapped",[1587]],[[126511,126511],"mapped",[1593]],[[126512,126512],"mapped",[1601]],[[126513,126513],"mapped",[1589]],[[126514,126514],"mapped",[1602]],[[126515,126515],"disallowed"],[[126516,126516],"mapped",[1588]],[[126517,126517],"mapped",[1578]],[[126518,126518],"mapped",[1579]],[[126519,126519],"mapped",[1582]],[[126520,126520],"disallowed"],[[126521,126521],"mapped",[1590]],[[126522,126522],"disallowed"],[[126523,126523],"mapped",[1594]],[[126524,126529],"disallowed"],[[126530,126530],"mapped",[1580]],[[126531,126534],"disallowed"],[[126535,126535],"mapped",[1581]],[[126536,126536],"disallowed"],[[126537,126537],"mapped",[1610]],[[126538,126538],"disallowed"],[[126539,126539],"mapped",[1604]],[[126540,126540],"disallowed"],[[126541,126541],"mapped",[1606]],[[126542,126542],"mapped",[1587]],[[126543,126543],"mapped",[1593]],[[126544,126544],"disallowed"],[[126545,126545],"mapped",[1589]],[[126546,126546],"mapped",[1602]],[[126547,126547],"disallowed"],[[126548,126548],"mapped",[1588]],[[126549,126550],"disallowed"],[[126551,126551],"mapped",[1582]],[[126552,126552],"disallowed"],[[126553,126553],"mapped",[1590]],[[126554,126554],"disallowed"],[[126555,126555],"mapped",[1594]],[[126556,126556],"disallowed"],[[126557,126557],"mapped",[1722]],[[126558,126558],"disallowed"],[[126559,126559],"mapped",[1647]],[[126560,126560],"disallowed"],[[126561,126561],"mapped",[1576]],[[126562,126562],"mapped",[1580]],[[126563,126563],"disallowed"],[[126564,126564],"mapped",[1607]],[[126565,126566],"disallowed"],[[126567,126567],"mapped",[1581]],[[126568,126568],"mapped",[1591]],[[126569,126569],"mapped",[1610]],[[126570,126570],"mapped",[1603]],[[126571,126571],"disallowed"],[[126572,126572],"mapped",[1605]],[[126573,126573],"mapped",[1606]],[[126574,126574],"mapped",[1587]],[[126575,126575],"mapped",[1593]],[[126576,126576],"mapped",[1601]],[[126577,126577],"mapped",[1589]],[[126578,126578],"mapped",[1602]],[[126579,126579],"disallowed"],[[126580,126580],"mapped",[1588]],[[126581,126581],"mapped",[1578]],[[126582,126582],"mapped",[1579]],[[126583,126583],"mapped",[1582]],[[126584,126584],"disallowed"],[[126585,126585],"mapped",[1590]],[[126586,126586],"mapped",[1592]],[[126587,126587],"mapped",[1594]],[[126588,126588],"mapped",[1646]],[[126589,126589],"disallowed"],[[126590,126590],"mapped",[1697]],[[126591,126591],"disallowed"],[[126592,126592],"mapped",[1575]],[[126593,126593],"mapped",[1576]],[[126594,126594],"mapped",[1580]],[[126595,126595],"mapped",[1583]],[[126596,126596],"mapped",[1607]],[[126597,126597],"mapped",[1608]],[[126598,126598],"mapped",[1586]],[[126599,126599],"mapped",[1581]],[[126600,126600],"mapped",[1591]],[[126601,126601],"mapped",[1610]],[[126602,126602],"disallowed"],[[126603,126603],"mapped",[1604]],[[126604,126604],"mapped",[1605]],[[126605,126605],"mapped",[1606]],[[126606,126606],"mapped",[1587]],[[126607,126607],"mapped",[1593]],[[126608,126608],"mapped",[1601]],[[126609,126609],"mapped",[1589]],[[126610,126610],"mapped",[1602]],[[126611,126611],"mapped",[1585]],[[126612,126612],"mapped",[1588]],[[126613,126613],"mapped",[1578]],[[126614,126614],"mapped",[1579]],[[126615,126615],"mapped",[1582]],[[126616,126616],"mapped",[1584]],[[126617,126617],"mapped",[1590]],[[126618,126618],"mapped",[1592]],[[126619,126619],"mapped",[1594]],[[126620,126624],"disallowed"],[[126625,126625],"mapped",[1576]],[[126626,126626],"mapped",[1580]],[[126627,126627],"mapped",[1583]],[[126628,126628],"disallowed"],[[126629,126629],"mapped",[1608]],[[126630,126630],"mapped",[1586]],[[126631,126631],"mapped",[1581]],[[126632,126632],"mapped",[1591]],[[126633,126633],"mapped",[1610]],[[126634,126634],"disallowed"],[[126635,126635],"mapped",[1604]],[[126636,126636],"mapped",[1605]],[[126637,126637],"mapped",[1606]],[[126638,126638],"mapped",[1587]],[[126639,126639],"mapped",[1593]],[[126640,126640],"mapped",[1601]],[[126641,126641],"mapped",[1589]],[[126642,126642],"mapped",[1602]],[[126643,126643],"mapped",[1585]],[[126644,126644],"mapped",[1588]],[[126645,126645],"mapped",[1578]],[[126646,126646],"mapped",[1579]],[[126647,126647],"mapped",[1582]],[[126648,126648],"mapped",[1584]],[[126649,126649],"mapped",[1590]],[[126650,126650],"mapped",[1592]],[[126651,126651],"mapped",[1594]],[[126652,126703],"disallowed"],[[126704,126705],"valid",[],"NV8"],[[126706,126975],"disallowed"],[[126976,127019],"valid",[],"NV8"],[[127020,127023],"disallowed"],[[127024,127123],"valid",[],"NV8"],[[127124,127135],"disallowed"],[[127136,127150],"valid",[],"NV8"],[[127151,127152],"disallowed"],[[127153,127166],"valid",[],"NV8"],[[127167,127167],"valid",[],"NV8"],[[127168,127168],"disallowed"],[[127169,127183],"valid",[],"NV8"],[[127184,127184],"disallowed"],[[127185,127199],"valid",[],"NV8"],[[127200,127221],"valid",[],"NV8"],[[127222,127231],"disallowed"],[[127232,127232],"disallowed"],[[127233,127233],"disallowed_STD3_mapped",[48,44]],[[127234,127234],"disallowed_STD3_mapped",[49,44]],[[127235,127235],"disallowed_STD3_mapped",[50,44]],[[127236,127236],"disallowed_STD3_mapped",[51,44]],[[127237,127237],"disallowed_STD3_mapped",[52,44]],[[127238,127238],"disallowed_STD3_mapped",[53,44]],[[127239,127239],"disallowed_STD3_mapped",[54,44]],[[127240,127240],"disallowed_STD3_mapped",[55,44]],[[127241,127241],"disallowed_STD3_mapped",[56,44]],[[127242,127242],"disallowed_STD3_mapped",[57,44]],[[127243,127244],"valid",[],"NV8"],[[127245,127247],"disallowed"],[[127248,127248],"disallowed_STD3_mapped",[40,97,41]],[[127249,127249],"disallowed_STD3_mapped",[40,98,41]],[[127250,127250],"disallowed_STD3_mapped",[40,99,41]],[[127251,127251],"disallowed_STD3_mapped",[40,100,41]],[[127252,127252],"disallowed_STD3_mapped",[40,101,41]],[[127253,127253],"disallowed_STD3_mapped",[40,102,41]],[[127254,127254],"disallowed_STD3_mapped",[40,103,41]],[[127255,127255],"disallowed_STD3_mapped",[40,104,41]],[[127256,127256],"disallowed_STD3_mapped",[40,105,41]],[[127257,127257],"disallowed_STD3_mapped",[40,106,41]],[[127258,127258],"disallowed_STD3_mapped",[40,107,41]],[[127259,127259],"disallowed_STD3_mapped",[40,108,41]],[[127260,127260],"disallowed_STD3_mapped",[40,109,41]],[[127261,127261],"disallowed_STD3_mapped",[40,110,41]],[[127262,127262],"disallowed_STD3_mapped",[40,111,41]],[[127263,127263],"disallowed_STD3_mapped",[40,112,41]],[[127264,127264],"disallowed_STD3_mapped",[40,113,41]],[[127265,127265],"disallowed_STD3_mapped",[40,114,41]],[[127266,127266],"disallowed_STD3_mapped",[40,115,41]],[[127267,127267],"disallowed_STD3_mapped",[40,116,41]],[[127268,127268],"disallowed_STD3_mapped",[40,117,41]],[[127269,127269],"disallowed_STD3_mapped",[40,118,41]],[[127270,127270],"disallowed_STD3_mapped",[40,119,41]],[[127271,127271],"disallowed_STD3_mapped",[40,120,41]],[[127272,127272],"disallowed_STD3_mapped",[40,121,41]],[[127273,127273],"disallowed_STD3_mapped",[40,122,41]],[[127274,127274],"mapped",[12308,115,12309]],[[127275,127275],"mapped",[99]],[[127276,127276],"mapped",[114]],[[127277,127277],"mapped",[99,100]],[[127278,127278],"mapped",[119,122]],[[127279,127279],"disallowed"],[[127280,127280],"mapped",[97]],[[127281,127281],"mapped",[98]],[[127282,127282],"mapped",[99]],[[127283,127283],"mapped",[100]],[[127284,127284],"mapped",[101]],[[127285,127285],"mapped",[102]],[[127286,127286],"mapped",[103]],[[127287,127287],"mapped",[104]],[[127288,127288],"mapped",[105]],[[127289,127289],"mapped",[106]],[[127290,127290],"mapped",[107]],[[127291,127291],"mapped",[108]],[[127292,127292],"mapped",[109]],[[127293,127293],"mapped",[110]],[[127294,127294],"mapped",[111]],[[127295,127295],"mapped",[112]],[[127296,127296],"mapped",[113]],[[127297,127297],"mapped",[114]],[[127298,127298],"mapped",[115]],[[127299,127299],"mapped",[116]],[[127300,127300],"mapped",[117]],[[127301,127301],"mapped",[118]],[[127302,127302],"mapped",[119]],[[127303,127303],"mapped",[120]],[[127304,127304],"mapped",[121]],[[127305,127305],"mapped",[122]],[[127306,127306],"mapped",[104,118]],[[127307,127307],"mapped",[109,118]],[[127308,127308],"mapped",[115,100]],[[127309,127309],"mapped",[115,115]],[[127310,127310],"mapped",[112,112,118]],[[127311,127311],"mapped",[119,99]],[[127312,127318],"valid",[],"NV8"],[[127319,127319],"valid",[],"NV8"],[[127320,127326],"valid",[],"NV8"],[[127327,127327],"valid",[],"NV8"],[[127328,127337],"valid",[],"NV8"],[[127338,127338],"mapped",[109,99]],[[127339,127339],"mapped",[109,100]],[[127340,127343],"disallowed"],[[127344,127352],"valid",[],"NV8"],[[127353,127353],"valid",[],"NV8"],[[127354,127354],"valid",[],"NV8"],[[127355,127356],"valid",[],"NV8"],[[127357,127358],"valid",[],"NV8"],[[127359,127359],"valid",[],"NV8"],[[127360,127369],"valid",[],"NV8"],[[127370,127373],"valid",[],"NV8"],[[127374,127375],"valid",[],"NV8"],[[127376,127376],"mapped",[100,106]],[[127377,127386],"valid",[],"NV8"],[[127387,127461],"disallowed"],[[127462,127487],"valid",[],"NV8"],[[127488,127488],"mapped",[12411,12363]],[[127489,127489],"mapped",[12467,12467]],[[127490,127490],"mapped",[12469]],[[127491,127503],"disallowed"],[[127504,127504],"mapped",[25163]],[[127505,127505],"mapped",[23383]],[[127506,127506],"mapped",[21452]],[[127507,127507],"mapped",[12487]],[[127508,127508],"mapped",[20108]],[[127509,127509],"mapped",[22810]],[[127510,127510],"mapped",[35299]],[[127511,127511],"mapped",[22825]],[[127512,127512],"mapped",[20132]],[[127513,127513],"mapped",[26144]],[[127514,127514],"mapped",[28961]],[[127515,127515],"mapped",[26009]],[[127516,127516],"mapped",[21069]],[[127517,127517],"mapped",[24460]],[[127518,127518],"mapped",[20877]],[[127519,127519],"mapped",[26032]],[[127520,127520],"mapped",[21021]],[[127521,127521],"mapped",[32066]],[[127522,127522],"mapped",[29983]],[[127523,127523],"mapped",[36009]],[[127524,127524],"mapped",[22768]],[[127525,127525],"mapped",[21561]],[[127526,127526],"mapped",[28436]],[[127527,127527],"mapped",[25237]],[[127528,127528],"mapped",[25429]],[[127529,127529],"mapped",[19968]],[[127530,127530],"mapped",[19977]],[[127531,127531],"mapped",[36938]],[[127532,127532],"mapped",[24038]],[[127533,127533],"mapped",[20013]],[[127534,127534],"mapped",[21491]],[[127535,127535],"mapped",[25351]],[[127536,127536],"mapped",[36208]],[[127537,127537],"mapped",[25171]],[[127538,127538],"mapped",[31105]],[[127539,127539],"mapped",[31354]],[[127540,127540],"mapped",[21512]],[[127541,127541],"mapped",[28288]],[[127542,127542],"mapped",[26377]],[[127543,127543],"mapped",[26376]],[[127544,127544],"mapped",[30003]],[[127545,127545],"mapped",[21106]],[[127546,127546],"mapped",[21942]],[[127547,127551],"disallowed"],[[127552,127552],"mapped",[12308,26412,12309]],[[127553,127553],"mapped",[12308,19977,12309]],[[127554,127554],"mapped",[12308,20108,12309]],[[127555,127555],"mapped",[12308,23433,12309]],[[127556,127556],"mapped",[12308,28857,12309]],[[127557,127557],"mapped",[12308,25171,12309]],[[127558,127558],"mapped",[12308,30423,12309]],[[127559,127559],"mapped",[12308,21213,12309]],[[127560,127560],"mapped",[12308,25943,12309]],[[127561,127567],"disallowed"],[[127568,127568],"mapped",[24471]],[[127569,127569],"mapped",[21487]],[[127570,127743],"disallowed"],[[127744,127776],"valid",[],"NV8"],[[127777,127788],"valid",[],"NV8"],[[127789,127791],"valid",[],"NV8"],[[127792,127797],"valid",[],"NV8"],[[127798,127798],"valid",[],"NV8"],[[127799,127868],"valid",[],"NV8"],[[127869,127869],"valid",[],"NV8"],[[127870,127871],"valid",[],"NV8"],[[127872,127891],"valid",[],"NV8"],[[127892,127903],"valid",[],"NV8"],[[127904,127940],"valid",[],"NV8"],[[127941,127941],"valid",[],"NV8"],[[127942,127946],"valid",[],"NV8"],[[127947,127950],"valid",[],"NV8"],[[127951,127955],"valid",[],"NV8"],[[127956,127967],"valid",[],"NV8"],[[127968,127984],"valid",[],"NV8"],[[127985,127991],"valid",[],"NV8"],[[127992,127999],"valid",[],"NV8"],[[128000,128062],"valid",[],"NV8"],[[128063,128063],"valid",[],"NV8"],[[128064,128064],"valid",[],"NV8"],[[128065,128065],"valid",[],"NV8"],[[128066,128247],"valid",[],"NV8"],[[128248,128248],"valid",[],"NV8"],[[128249,128252],"valid",[],"NV8"],[[128253,128254],"valid",[],"NV8"],[[128255,128255],"valid",[],"NV8"],[[128256,128317],"valid",[],"NV8"],[[128318,128319],"valid",[],"NV8"],[[128320,128323],"valid",[],"NV8"],[[128324,128330],"valid",[],"NV8"],[[128331,128335],"valid",[],"NV8"],[[128336,128359],"valid",[],"NV8"],[[128360,128377],"valid",[],"NV8"],[[128378,128378],"disallowed"],[[128379,128419],"valid",[],"NV8"],[[128420,128420],"disallowed"],[[128421,128506],"valid",[],"NV8"],[[128507,128511],"valid",[],"NV8"],[[128512,128512],"valid",[],"NV8"],[[128513,128528],"valid",[],"NV8"],[[128529,128529],"valid",[],"NV8"],[[128530,128532],"valid",[],"NV8"],[[128533,128533],"valid",[],"NV8"],[[128534,128534],"valid",[],"NV8"],[[128535,128535],"valid",[],"NV8"],[[128536,128536],"valid",[],"NV8"],[[128537,128537],"valid",[],"NV8"],[[128538,128538],"valid",[],"NV8"],[[128539,128539],"valid",[],"NV8"],[[128540,128542],"valid",[],"NV8"],[[128543,128543],"valid",[],"NV8"],[[128544,128549],"valid",[],"NV8"],[[128550,128551],"valid",[],"NV8"],[[128552,128555],"valid",[],"NV8"],[[128556,128556],"valid",[],"NV8"],[[128557,128557],"valid",[],"NV8"],[[128558,128559],"valid",[],"NV8"],[[128560,128563],"valid",[],"NV8"],[[128564,128564],"valid",[],"NV8"],[[128565,128576],"valid",[],"NV8"],[[128577,128578],"valid",[],"NV8"],[[128579,128580],"valid",[],"NV8"],[[128581,128591],"valid",[],"NV8"],[[128592,128639],"valid",[],"NV8"],[[128640,128709],"valid",[],"NV8"],[[128710,128719],"valid",[],"NV8"],[[128720,128720],"valid",[],"NV8"],[[128721,128735],"disallowed"],[[128736,128748],"valid",[],"NV8"],[[128749,128751],"disallowed"],[[128752,128755],"valid",[],"NV8"],[[128756,128767],"disallowed"],[[128768,128883],"valid",[],"NV8"],[[128884,128895],"disallowed"],[[128896,128980],"valid",[],"NV8"],[[128981,129023],"disallowed"],[[129024,129035],"valid",[],"NV8"],[[129036,129039],"disallowed"],[[129040,129095],"valid",[],"NV8"],[[129096,129103],"disallowed"],[[129104,129113],"valid",[],"NV8"],[[129114,129119],"disallowed"],[[129120,129159],"valid",[],"NV8"],[[129160,129167],"disallowed"],[[129168,129197],"valid",[],"NV8"],[[129198,129295],"disallowed"],[[129296,129304],"valid",[],"NV8"],[[129305,129407],"disallowed"],[[129408,129412],"valid",[],"NV8"],[[129413,129471],"disallowed"],[[129472,129472],"valid",[],"NV8"],[[129473,131069],"disallowed"],[[131070,131071],"disallowed"],[[131072,173782],"valid"],[[173783,173823],"disallowed"],[[173824,177972],"valid"],[[177973,177983],"disallowed"],[[177984,178205],"valid"],[[178206,178207],"disallowed"],[[178208,183969],"valid"],[[183970,194559],"disallowed"],[[194560,194560],"mapped",[20029]],[[194561,194561],"mapped",[20024]],[[194562,194562],"mapped",[20033]],[[194563,194563],"mapped",[131362]],[[194564,194564],"mapped",[20320]],[[194565,194565],"mapped",[20398]],[[194566,194566],"mapped",[20411]],[[194567,194567],"mapped",[20482]],[[194568,194568],"mapped",[20602]],[[194569,194569],"mapped",[20633]],[[194570,194570],"mapped",[20711]],[[194571,194571],"mapped",[20687]],[[194572,194572],"mapped",[13470]],[[194573,194573],"mapped",[132666]],[[194574,194574],"mapped",[20813]],[[194575,194575],"mapped",[20820]],[[194576,194576],"mapped",[20836]],[[194577,194577],"mapped",[20855]],[[194578,194578],"mapped",[132380]],[[194579,194579],"mapped",[13497]],[[194580,194580],"mapped",[20839]],[[194581,194581],"mapped",[20877]],[[194582,194582],"mapped",[132427]],[[194583,194583],"mapped",[20887]],[[194584,194584],"mapped",[20900]],[[194585,194585],"mapped",[20172]],[[194586,194586],"mapped",[20908]],[[194587,194587],"mapped",[20917]],[[194588,194588],"mapped",[168415]],[[194589,194589],"mapped",[20981]],[[194590,194590],"mapped",[20995]],[[194591,194591],"mapped",[13535]],[[194592,194592],"mapped",[21051]],[[194593,194593],"mapped",[21062]],[[194594,194594],"mapped",[21106]],[[194595,194595],"mapped",[21111]],[[194596,194596],"mapped",[13589]],[[194597,194597],"mapped",[21191]],[[194598,194598],"mapped",[21193]],[[194599,194599],"mapped",[21220]],[[194600,194600],"mapped",[21242]],[[194601,194601],"mapped",[21253]],[[194602,194602],"mapped",[21254]],[[194603,194603],"mapped",[21271]],[[194604,194604],"mapped",[21321]],[[194605,194605],"mapped",[21329]],[[194606,194606],"mapped",[21338]],[[194607,194607],"mapped",[21363]],[[194608,194608],"mapped",[21373]],[[194609,194611],"mapped",[21375]],[[194612,194612],"mapped",[133676]],[[194613,194613],"mapped",[28784]],[[194614,194614],"mapped",[21450]],[[194615,194615],"mapped",[21471]],[[194616,194616],"mapped",[133987]],[[194617,194617],"mapped",[21483]],[[194618,194618],"mapped",[21489]],[[194619,194619],"mapped",[21510]],[[194620,194620],"mapped",[21662]],[[194621,194621],"mapped",[21560]],[[194622,194622],"mapped",[21576]],[[194623,194623],"mapped",[21608]],[[194624,194624],"mapped",[21666]],[[194625,194625],"mapped",[21750]],[[194626,194626],"mapped",[21776]],[[194627,194627],"mapped",[21843]],[[194628,194628],"mapped",[21859]],[[194629,194630],"mapped",[21892]],[[194631,194631],"mapped",[21913]],[[194632,194632],"mapped",[21931]],[[194633,194633],"mapped",[21939]],[[194634,194634],"mapped",[21954]],[[194635,194635],"mapped",[22294]],[[194636,194636],"mapped",[22022]],[[194637,194637],"mapped",[22295]],[[194638,194638],"mapped",[22097]],[[194639,194639],"mapped",[22132]],[[194640,194640],"mapped",[20999]],[[194641,194641],"mapped",[22766]],[[194642,194642],"mapped",[22478]],[[194643,194643],"mapped",[22516]],[[194644,194644],"mapped",[22541]],[[194645,194645],"mapped",[22411]],[[194646,194646],"mapped",[22578]],[[194647,194647],"mapped",[22577]],[[194648,194648],"mapped",[22700]],[[194649,194649],"mapped",[136420]],[[194650,194650],"mapped",[22770]],[[194651,194651],"mapped",[22775]],[[194652,194652],"mapped",[22790]],[[194653,194653],"mapped",[22810]],[[194654,194654],"mapped",[22818]],[[194655,194655],"mapped",[22882]],[[194656,194656],"mapped",[136872]],[[194657,194657],"mapped",[136938]],[[194658,194658],"mapped",[23020]],[[194659,194659],"mapped",[23067]],[[194660,194660],"mapped",[23079]],[[194661,194661],"mapped",[23000]],[[194662,194662],"mapped",[23142]],[[194663,194663],"mapped",[14062]],[[194664,194664],"disallowed"],[[194665,194665],"mapped",[23304]],[[194666,194667],"mapped",[23358]],[[194668,194668],"mapped",[137672]],[[194669,194669],"mapped",[23491]],[[194670,194670],"mapped",[23512]],[[194671,194671],"mapped",[23527]],[[194672,194672],"mapped",[23539]],[[194673,194673],"mapped",[138008]],[[194674,194674],"mapped",[23551]],[[194675,194675],"mapped",[23558]],[[194676,194676],"disallowed"],[[194677,194677],"mapped",[23586]],[[194678,194678],"mapped",[14209]],[[194679,194679],"mapped",[23648]],[[194680,194680],"mapped",[23662]],[[194681,194681],"mapped",[23744]],[[194682,194682],"mapped",[23693]],[[194683,194683],"mapped",[138724]],[[194684,194684],"mapped",[23875]],[[194685,194685],"mapped",[138726]],[[194686,194686],"mapped",[23918]],[[194687,194687],"mapped",[23915]],[[194688,194688],"mapped",[23932]],[[194689,194689],"mapped",[24033]],[[194690,194690],"mapped",[24034]],[[194691,194691],"mapped",[14383]],[[194692,194692],"mapped",[24061]],[[194693,194693],"mapped",[24104]],[[194694,194694],"mapped",[24125]],[[194695,194695],"mapped",[24169]],[[194696,194696],"mapped",[14434]],[[194697,194697],"mapped",[139651]],[[194698,194698],"mapped",[14460]],[[194699,194699],"mapped",[24240]],[[194700,194700],"mapped",[24243]],[[194701,194701],"mapped",[24246]],[[194702,194702],"mapped",[24266]],[[194703,194703],"mapped",[172946]],[[194704,194704],"mapped",[24318]],[[194705,194706],"mapped",[140081]],[[194707,194707],"mapped",[33281]],[[194708,194709],"mapped",[24354]],[[194710,194710],"mapped",[14535]],[[194711,194711],"mapped",[144056]],[[194712,194712],"mapped",[156122]],[[194713,194713],"mapped",[24418]],[[194714,194714],"mapped",[24427]],[[194715,194715],"mapped",[14563]],[[194716,194716],"mapped",[24474]],[[194717,194717],"mapped",[24525]],[[194718,194718],"mapped",[24535]],[[194719,194719],"mapped",[24569]],[[194720,194720],"mapped",[24705]],[[194721,194721],"mapped",[14650]],[[194722,194722],"mapped",[14620]],[[194723,194723],"mapped",[24724]],[[194724,194724],"mapped",[141012]],[[194725,194725],"mapped",[24775]],[[194726,194726],"mapped",[24904]],[[194727,194727],"mapped",[24908]],[[194728,194728],"mapped",[24910]],[[194729,194729],"mapped",[24908]],[[194730,194730],"mapped",[24954]],[[194731,194731],"mapped",[24974]],[[194732,194732],"mapped",[25010]],[[194733,194733],"mapped",[24996]],[[194734,194734],"mapped",[25007]],[[194735,194735],"mapped",[25054]],[[194736,194736],"mapped",[25074]],[[194737,194737],"mapped",[25078]],[[194738,194738],"mapped",[25104]],[[194739,194739],"mapped",[25115]],[[194740,194740],"mapped",[25181]],[[194741,194741],"mapped",[25265]],[[194742,194742],"mapped",[25300]],[[194743,194743],"mapped",[25424]],[[194744,194744],"mapped",[142092]],[[194745,194745],"mapped",[25405]],[[194746,194746],"mapped",[25340]],[[194747,194747],"mapped",[25448]],[[194748,194748],"mapped",[25475]],[[194749,194749],"mapped",[25572]],[[194750,194750],"mapped",[142321]],[[194751,194751],"mapped",[25634]],[[194752,194752],"mapped",[25541]],[[194753,194753],"mapped",[25513]],[[194754,194754],"mapped",[14894]],[[194755,194755],"mapped",[25705]],[[194756,194756],"mapped",[25726]],[[194757,194757],"mapped",[25757]],[[194758,194758],"mapped",[25719]],[[194759,194759],"mapped",[14956]],[[194760,194760],"mapped",[25935]],[[194761,194761],"mapped",[25964]],[[194762,194762],"mapped",[143370]],[[194763,194763],"mapped",[26083]],[[194764,194764],"mapped",[26360]],[[194765,194765],"mapped",[26185]],[[194766,194766],"mapped",[15129]],[[194767,194767],"mapped",[26257]],[[194768,194768],"mapped",[15112]],[[194769,194769],"mapped",[15076]],[[194770,194770],"mapped",[20882]],[[194771,194771],"mapped",[20885]],[[194772,194772],"mapped",[26368]],[[194773,194773],"mapped",[26268]],[[194774,194774],"mapped",[32941]],[[194775,194775],"mapped",[17369]],[[194776,194776],"mapped",[26391]],[[194777,194777],"mapped",[26395]],[[194778,194778],"mapped",[26401]],[[194779,194779],"mapped",[26462]],[[194780,194780],"mapped",[26451]],[[194781,194781],"mapped",[144323]],[[194782,194782],"mapped",[15177]],[[194783,194783],"mapped",[26618]],[[194784,194784],"mapped",[26501]],[[194785,194785],"mapped",[26706]],[[194786,194786],"mapped",[26757]],[[194787,194787],"mapped",[144493]],[[194788,194788],"mapped",[26766]],[[194789,194789],"mapped",[26655]],[[194790,194790],"mapped",[26900]],[[194791,194791],"mapped",[15261]],[[194792,194792],"mapped",[26946]],[[194793,194793],"mapped",[27043]],[[194794,194794],"mapped",[27114]],[[194795,194795],"mapped",[27304]],[[194796,194796],"mapped",[145059]],[[194797,194797],"mapped",[27355]],[[194798,194798],"mapped",[15384]],[[194799,194799],"mapped",[27425]],[[194800,194800],"mapped",[145575]],[[194801,194801],"mapped",[27476]],[[194802,194802],"mapped",[15438]],[[194803,194803],"mapped",[27506]],[[194804,194804],"mapped",[27551]],[[194805,194805],"mapped",[27578]],[[194806,194806],"mapped",[27579]],[[194807,194807],"mapped",[146061]],[[194808,194808],"mapped",[138507]],[[194809,194809],"mapped",[146170]],[[194810,194810],"mapped",[27726]],[[194811,194811],"mapped",[146620]],[[194812,194812],"mapped",[27839]],[[194813,194813],"mapped",[27853]],[[194814,194814],"mapped",[27751]],[[194815,194815],"mapped",[27926]],[[194816,194816],"mapped",[27966]],[[194817,194817],"mapped",[28023]],[[194818,194818],"mapped",[27969]],[[194819,194819],"mapped",[28009]],[[194820,194820],"mapped",[28024]],[[194821,194821],"mapped",[28037]],[[194822,194822],"mapped",[146718]],[[194823,194823],"mapped",[27956]],[[194824,194824],"mapped",[28207]],[[194825,194825],"mapped",[28270]],[[194826,194826],"mapped",[15667]],[[194827,194827],"mapped",[28363]],[[194828,194828],"mapped",[28359]],[[194829,194829],"mapped",[147153]],[[194830,194830],"mapped",[28153]],[[194831,194831],"mapped",[28526]],[[194832,194832],"mapped",[147294]],[[194833,194833],"mapped",[147342]],[[194834,194834],"mapped",[28614]],[[194835,194835],"mapped",[28729]],[[194836,194836],"mapped",[28702]],[[194837,194837],"mapped",[28699]],[[194838,194838],"mapped",[15766]],[[194839,194839],"mapped",[28746]],[[194840,194840],"mapped",[28797]],[[194841,194841],"mapped",[28791]],[[194842,194842],"mapped",[28845]],[[194843,194843],"mapped",[132389]],[[194844,194844],"mapped",[28997]],[[194845,194845],"mapped",[148067]],[[194846,194846],"mapped",[29084]],[[194847,194847],"disallowed"],[[194848,194848],"mapped",[29224]],[[194849,194849],"mapped",[29237]],[[194850,194850],"mapped",[29264]],[[194851,194851],"mapped",[149000]],[[194852,194852],"mapped",[29312]],[[194853,194853],"mapped",[29333]],[[194854,194854],"mapped",[149301]],[[194855,194855],"mapped",[149524]],[[194856,194856],"mapped",[29562]],[[194857,194857],"mapped",[29579]],[[194858,194858],"mapped",[16044]],[[194859,194859],"mapped",[29605]],[[194860,194861],"mapped",[16056]],[[194862,194862],"mapped",[29767]],[[194863,194863],"mapped",[29788]],[[194864,194864],"mapped",[29809]],[[194865,194865],"mapped",[29829]],[[194866,194866],"mapped",[29898]],[[194867,194867],"mapped",[16155]],[[194868,194868],"mapped",[29988]],[[194869,194869],"mapped",[150582]],[[194870,194870],"mapped",[30014]],[[194871,194871],"mapped",[150674]],[[194872,194872],"mapped",[30064]],[[194873,194873],"mapped",[139679]],[[194874,194874],"mapped",[30224]],[[194875,194875],"mapped",[151457]],[[194876,194876],"mapped",[151480]],[[194877,194877],"mapped",[151620]],[[194878,194878],"mapped",[16380]],[[194879,194879],"mapped",[16392]],[[194880,194880],"mapped",[30452]],[[194881,194881],"mapped",[151795]],[[194882,194882],"mapped",[151794]],[[194883,194883],"mapped",[151833]],[[194884,194884],"mapped",[151859]],[[194885,194885],"mapped",[30494]],[[194886,194887],"mapped",[30495]],[[194888,194888],"mapped",[30538]],[[194889,194889],"mapped",[16441]],[[194890,194890],"mapped",[30603]],[[194891,194891],"mapped",[16454]],[[194892,194892],"mapped",[16534]],[[194893,194893],"mapped",[152605]],[[194894,194894],"mapped",[30798]],[[194895,194895],"mapped",[30860]],[[194896,194896],"mapped",[30924]],[[194897,194897],"mapped",[16611]],[[194898,194898],"mapped",[153126]],[[194899,194899],"mapped",[31062]],[[194900,194900],"mapped",[153242]],[[194901,194901],"mapped",[153285]],[[194902,194902],"mapped",[31119]],[[194903,194903],"mapped",[31211]],[[194904,194904],"mapped",[16687]],[[194905,194905],"mapped",[31296]],[[194906,194906],"mapped",[31306]],[[194907,194907],"mapped",[31311]],[[194908,194908],"mapped",[153980]],[[194909,194910],"mapped",[154279]],[[194911,194911],"disallowed"],[[194912,194912],"mapped",[16898]],[[194913,194913],"mapped",[154539]],[[194914,194914],"mapped",[31686]],[[194915,194915],"mapped",[31689]],[[194916,194916],"mapped",[16935]],[[194917,194917],"mapped",[154752]],[[194918,194918],"mapped",[31954]],[[194919,194919],"mapped",[17056]],[[194920,194920],"mapped",[31976]],[[194921,194921],"mapped",[31971]],[[194922,194922],"mapped",[32000]],[[194923,194923],"mapped",[155526]],[[194924,194924],"mapped",[32099]],[[194925,194925],"mapped",[17153]],[[194926,194926],"mapped",[32199]],[[194927,194927],"mapped",[32258]],[[194928,194928],"mapped",[32325]],[[194929,194929],"mapped",[17204]],[[194930,194930],"mapped",[156200]],[[194931,194931],"mapped",[156231]],[[194932,194932],"mapped",[17241]],[[194933,194933],"mapped",[156377]],[[194934,194934],"mapped",[32634]],[[194935,194935],"mapped",[156478]],[[194936,194936],"mapped",[32661]],[[194937,194937],"mapped",[32762]],[[194938,194938],"mapped",[32773]],[[194939,194939],"mapped",[156890]],[[194940,194940],"mapped",[156963]],[[194941,194941],"mapped",[32864]],[[194942,194942],"mapped",[157096]],[[194943,194943],"mapped",[32880]],[[194944,194944],"mapped",[144223]],[[194945,194945],"mapped",[17365]],[[194946,194946],"mapped",[32946]],[[194947,194947],"mapped",[33027]],[[194948,194948],"mapped",[17419]],[[194949,194949],"mapped",[33086]],[[194950,194950],"mapped",[23221]],[[194951,194951],"mapped",[157607]],[[194952,194952],"mapped",[157621]],[[194953,194953],"mapped",[144275]],[[194954,194954],"mapped",[144284]],[[194955,194955],"mapped",[33281]],[[194956,194956],"mapped",[33284]],[[194957,194957],"mapped",[36766]],[[194958,194958],"mapped",[17515]],[[194959,194959],"mapped",[33425]],[[194960,194960],"mapped",[33419]],[[194961,194961],"mapped",[33437]],[[194962,194962],"mapped",[21171]],[[194963,194963],"mapped",[33457]],[[194964,194964],"mapped",[33459]],[[194965,194965],"mapped",[33469]],[[194966,194966],"mapped",[33510]],[[194967,194967],"mapped",[158524]],[[194968,194968],"mapped",[33509]],[[194969,194969],"mapped",[33565]],[[194970,194970],"mapped",[33635]],[[194971,194971],"mapped",[33709]],[[194972,194972],"mapped",[33571]],[[194973,194973],"mapped",[33725]],[[194974,194974],"mapped",[33767]],[[194975,194975],"mapped",[33879]],[[194976,194976],"mapped",[33619]],[[194977,194977],"mapped",[33738]],[[194978,194978],"mapped",[33740]],[[194979,194979],"mapped",[33756]],[[194980,194980],"mapped",[158774]],[[194981,194981],"mapped",[159083]],[[194982,194982],"mapped",[158933]],[[194983,194983],"mapped",[17707]],[[194984,194984],"mapped",[34033]],[[194985,194985],"mapped",[34035]],[[194986,194986],"mapped",[34070]],[[194987,194987],"mapped",[160714]],[[194988,194988],"mapped",[34148]],[[194989,194989],"mapped",[159532]],[[194990,194990],"mapped",[17757]],[[194991,194991],"mapped",[17761]],[[194992,194992],"mapped",[159665]],[[194993,194993],"mapped",[159954]],[[194994,194994],"mapped",[17771]],[[194995,194995],"mapped",[34384]],[[194996,194996],"mapped",[34396]],[[194997,194997],"mapped",[34407]],[[194998,194998],"mapped",[34409]],[[194999,194999],"mapped",[34473]],[[195000,195000],"mapped",[34440]],[[195001,195001],"mapped",[34574]],[[195002,195002],"mapped",[34530]],[[195003,195003],"mapped",[34681]],[[195004,195004],"mapped",[34600]],[[195005,195005],"mapped",[34667]],[[195006,195006],"mapped",[34694]],[[195007,195007],"disallowed"],[[195008,195008],"mapped",[34785]],[[195009,195009],"mapped",[34817]],[[195010,195010],"mapped",[17913]],[[195011,195011],"mapped",[34912]],[[195012,195012],"mapped",[34915]],[[195013,195013],"mapped",[161383]],[[195014,195014],"mapped",[35031]],[[195015,195015],"mapped",[35038]],[[195016,195016],"mapped",[17973]],[[195017,195017],"mapped",[35066]],[[195018,195018],"mapped",[13499]],[[195019,195019],"mapped",[161966]],[[195020,195020],"mapped",[162150]],[[195021,195021],"mapped",[18110]],[[195022,195022],"mapped",[18119]],[[195023,195023],"mapped",[35488]],[[195024,195024],"mapped",[35565]],[[195025,195025],"mapped",[35722]],[[195026,195026],"mapped",[35925]],[[195027,195027],"mapped",[162984]],[[195028,195028],"mapped",[36011]],[[195029,195029],"mapped",[36033]],[[195030,195030],"mapped",[36123]],[[195031,195031],"mapped",[36215]],[[195032,195032],"mapped",[163631]],[[195033,195033],"mapped",[133124]],[[195034,195034],"mapped",[36299]],[[195035,195035],"mapped",[36284]],[[195036,195036],"mapped",[36336]],[[195037,195037],"mapped",[133342]],[[195038,195038],"mapped",[36564]],[[195039,195039],"mapped",[36664]],[[195040,195040],"mapped",[165330]],[[195041,195041],"mapped",[165357]],[[195042,195042],"mapped",[37012]],[[195043,195043],"mapped",[37105]],[[195044,195044],"mapped",[37137]],[[195045,195045],"mapped",[165678]],[[195046,195046],"mapped",[37147]],[[195047,195047],"mapped",[37432]],[[195048,195048],"mapped",[37591]],[[195049,195049],"mapped",[37592]],[[195050,195050],"mapped",[37500]],[[195051,195051],"mapped",[37881]],[[195052,195052],"mapped",[37909]],[[195053,195053],"mapped",[166906]],[[195054,195054],"mapped",[38283]],[[195055,195055],"mapped",[18837]],[[195056,195056],"mapped",[38327]],[[195057,195057],"mapped",[167287]],[[195058,195058],"mapped",[18918]],[[195059,195059],"mapped",[38595]],[[195060,195060],"mapped",[23986]],[[195061,195061],"mapped",[38691]],[[195062,195062],"mapped",[168261]],[[195063,195063],"mapped",[168474]],[[195064,195064],"mapped",[19054]],[[195065,195065],"mapped",[19062]],[[195066,195066],"mapped",[38880]],[[195067,195067],"mapped",[168970]],[[195068,195068],"mapped",[19122]],[[195069,195069],"mapped",[169110]],[[195070,195071],"mapped",[38923]],[[195072,195072],"mapped",[38953]],[[195073,195073],"mapped",[169398]],[[195074,195074],"mapped",[39138]],[[195075,195075],"mapped",[19251]],[[195076,195076],"mapped",[39209]],[[195077,195077],"mapped",[39335]],[[195078,195078],"mapped",[39362]],[[195079,195079],"mapped",[39422]],[[195080,195080],"mapped",[19406]],[[195081,195081],"mapped",[170800]],[[195082,195082],"mapped",[39698]],[[195083,195083],"mapped",[40000]],[[195084,195084],"mapped",[40189]],[[195085,195085],"mapped",[19662]],[[195086,195086],"mapped",[19693]],[[195087,195087],"mapped",[40295]],[[195088,195088],"mapped",[172238]],[[195089,195089],"mapped",[19704]],[[195090,195090],"mapped",[172293]],[[195091,195091],"mapped",[172558]],[[195092,195092],"mapped",[172689]],[[195093,195093],"mapped",[40635]],[[195094,195094],"mapped",[19798]],[[195095,195095],"mapped",[40697]],[[195096,195096],"mapped",[40702]],[[195097,195097],"mapped",[40709]],[[195098,195098],"mapped",[40719]],[[195099,195099],"mapped",[40726]],[[195100,195100],"mapped",[40763]],[[195101,195101],"mapped",[173568]],[[195102,196605],"disallowed"],[[196606,196607],"disallowed"],[[196608,262141],"disallowed"],[[262142,262143],"disallowed"],[[262144,327677],"disallowed"],[[327678,327679],"disallowed"],[[327680,393213],"disallowed"],[[393214,393215],"disallowed"],[[393216,458749],"disallowed"],[[458750,458751],"disallowed"],[[458752,524285],"disallowed"],[[524286,524287],"disallowed"],[[524288,589821],"disallowed"],[[589822,589823],"disallowed"],[[589824,655357],"disallowed"],[[655358,655359],"disallowed"],[[655360,720893],"disallowed"],[[720894,720895],"disallowed"],[[720896,786429],"disallowed"],[[786430,786431],"disallowed"],[[786432,851965],"disallowed"],[[851966,851967],"disallowed"],[[851968,917501],"disallowed"],[[917502,917503],"disallowed"],[[917504,917504],"disallowed"],[[917505,917505],"disallowed"],[[917506,917535],"disallowed"],[[917536,917631],"disallowed"],[[917632,917759],"disallowed"],[[917760,917999],"ignored"],[[918000,983037],"disallowed"],[[983038,983039],"disallowed"],[[983040,1048573],"disallowed"],[[1048574,1048575],"disallowed"],[[1048576,1114109],"disallowed"],[[1114110,1114111],"disallowed"]]')},78677:(e,a,p)=>{let{kForOnEventAttribute:d,kListener:t}=p(31394),i=Symbol("kCode"),s=Symbol("kData"),r=Symbol("kError"),l=Symbol("kMessage"),o=Symbol("kReason"),n=Symbol("kTarget"),m=Symbol("kType"),h=Symbol("kWasClean");class u{constructor(e){this[n]=null,this[m]=e}get target(){return this[n]}get type(){return this[m]}}Object.defineProperty(u.prototype,"target",{enumerable:!0}),Object.defineProperty(u.prototype,"type",{enumerable:!0});class c extends u{constructor(e,a={}){super(e),this[i]=void 0===a.code?0:a.code,this[o]=void 0===a.reason?"":a.reason,this[h]=void 0!==a.wasClean&&a.wasClean}get code(){return this[i]}get reason(){return this[o]}get wasClean(){return this[h]}}Object.defineProperty(c.prototype,"code",{enumerable:!0}),Object.defineProperty(c.prototype,"reason",{enumerable:!0}),Object.defineProperty(c.prototype,"wasClean",{enumerable:!0});class v extends u{constructor(e,a={}){super(e),this[r]=void 0===a.error?null:a.error,this[l]=void 0===a.message?"":a.message}get error(){return this[r]}get message(){return this[l]}}Object.defineProperty(v.prototype,"error",{enumerable:!0}),Object.defineProperty(v.prototype,"message",{enumerable:!0});class f extends u{constructor(e,a={}){super(e),this[s]=void 0===a.data?null:a.data}get data(){return this[s]}}function w(e,a,p){"object"==typeof e&&e.handleEvent?e.handleEvent.call(e,p):e.call(a,p)}Object.defineProperty(f.prototype,"data",{enumerable:!0}),e.exports={CloseEvent:c,ErrorEvent:v,Event:u,EventTarget:{addEventListener(e,a,p={}){let i;for(let i of this.listeners(e))if(!p[d]&&i[t]===a&&!i[d])return;if("message"===e)i=function(e,p){let d=new f("message",{data:p?e:e.toString()});d[n]=this,w(a,this,d)};else if("close"===e)i=function(e,p){let d=new c("close",{code:e,reason:p.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});d[n]=this,w(a,this,d)};else if("error"===e)i=function(e){let p=new v("error",{error:e,message:e.message});p[n]=this,w(a,this,p)};else{if("open"!==e)return;i=function(){let e=new u("open");e[n]=this,w(a,this,e)}}i[d]=!!p[d],i[t]=a,p.once?this.once(e,i):this.on(e,i)},removeEventListener(e,a){for(let p of this.listeners(e))if(p[t]===a&&!p[d]){this.removeListener(e,p);break}}},MessageEvent:f}},79432:(e,a,p)=>{let d=p(38624),t=p(5092),i=p(95515),s=t.implSymbol;function r(a){if(!this||this[s]||!(this instanceof r))throw TypeError("Failed to construct 'URL': Please use the 'new' operator, this DOM object constructor cannot be called as a function.");if(arguments.length<1)throw TypeError("Failed to construct 'URL': 1 argument required, but only "+arguments.length+" present.");let p=[];for(let e=0;e<arguments.length&&e<2;++e)p[e]=arguments[e];p[0]=d.USVString(p[0]),void 0!==p[1]&&(p[1]=d.USVString(p[1])),e.exports.setup(this,p)}r.prototype.toJSON=function(){if(!this||!e.exports.is(this))throw TypeError("Illegal invocation");let a=[];for(let e=0;e<arguments.length&&e<0;++e)a[e]=arguments[e];return this[s].toJSON.apply(this[s],a)},Object.defineProperty(r.prototype,"href",{get(){return this[s].href},set(e){e=d.USVString(e),this[s].href=e},enumerable:!0,configurable:!0}),r.prototype.toString=function(){if(!this||!e.exports.is(this))throw TypeError("Illegal invocation");return this.href},Object.defineProperty(r.prototype,"origin",{get(){return this[s].origin},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"protocol",{get(){return this[s].protocol},set(e){e=d.USVString(e),this[s].protocol=e},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"username",{get(){return this[s].username},set(e){e=d.USVString(e),this[s].username=e},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"password",{get(){return this[s].password},set(e){e=d.USVString(e),this[s].password=e},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"host",{get(){return this[s].host},set(e){e=d.USVString(e),this[s].host=e},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"hostname",{get(){return this[s].hostname},set(e){e=d.USVString(e),this[s].hostname=e},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"port",{get(){return this[s].port},set(e){e=d.USVString(e),this[s].port=e},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"pathname",{get(){return this[s].pathname},set(e){e=d.USVString(e),this[s].pathname=e},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"search",{get(){return this[s].search},set(e){e=d.USVString(e),this[s].search=e},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"hash",{get(){return this[s].hash},set(e){e=d.USVString(e),this[s].hash=e},enumerable:!0,configurable:!0}),e.exports={is:e=>!!e&&e[s]instanceof i.implementation,create(e,a){let p=Object.create(r.prototype);return this.setup(p,e,a),p},setup(e,a,p){p||(p={}),p.wrapper=e,e[s]=new i.implementation(a,p),e[s][t.wrapperSymbol]=e},interface:r,expose:{Window:{URL:r},Worker:{URL:r}}}},82657:function(e,a,p){var d=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(a,"__esModule",{value:!0});let t=d(p(9301)),i=d(p(56537)),s=p(58238);class r{constructor(e,{headers:a={},schema:p,fetch:d}={}){this.url=e,this.headers=Object.assign(Object.assign({},s.DEFAULT_HEADERS),a),this.schemaName=p,this.fetch=d}from(e){let a=new URL(`${this.url}/${e}`);return new t.default(a,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new r(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(e,a={},{head:p=!1,get:d=!1,count:t}={}){let s,r,l=new URL(`${this.url}/rpc/${e}`);p||d?(s=p?"HEAD":"GET",Object.entries(a).filter(([e,a])=>void 0!==a).map(([e,a])=>[e,Array.isArray(a)?`{${a.join(",")}}`:`${a}`]).forEach(([e,a])=>{l.searchParams.append(e,a)})):(s="POST",r=a);let o=Object.assign({},this.headers);return t&&(o.Prefer=`count=${t}`),new i.default({method:s,url:l,headers:o,schema:this.schemaName,body:r,fetch:this.fetch,allowEmpty:!1})}}a.default=r},83899:(e,a,p)=>{function d(e){return e&&"object"==typeof e&&"default"in e?e.default:e}Object.defineProperty(a,"__esModule",{value:!0});var t=d(p(27910)),i=d(p(81630)),s=d(p(79551)),r=d(p(73425)),l=d(p(55591)),o=d(p(74075));let n=t.Readable,m=Symbol("buffer"),h=Symbol("type");class u{constructor(){this[h]="";let e=arguments[0],a=arguments[1],p=[];if(e){let a=Number(e.length);for(let d=0;d<a;d++){let a,t=e[d];(a=t instanceof Buffer?t:ArrayBuffer.isView(t)?Buffer.from(t.buffer,t.byteOffset,t.byteLength):t instanceof ArrayBuffer?Buffer.from(t):t instanceof u?t[m]:Buffer.from("string"==typeof t?t:String(t))).length,p.push(a)}}this[m]=Buffer.concat(p);let d=a&&void 0!==a.type&&String(a.type).toLowerCase();d&&!/[^\u0020-\u007E]/.test(d)&&(this[h]=d)}get size(){return this[m].length}get type(){return this[h]}text(){return Promise.resolve(this[m].toString())}arrayBuffer(){let e=this[m];return Promise.resolve(e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength))}stream(){let e=new n;return e._read=function(){},e.push(this[m]),e.push(null),e}toString(){return"[object Blob]"}slice(){let e,a,p=this.size,d=arguments[0],t=arguments[1];e=void 0===d?0:d<0?Math.max(p+d,0):Math.min(d,p);let i=Math.max((void 0===t?p:t<0?Math.max(p+t,0):Math.min(t,p))-e,0),s=this[m].slice(e,e+i),r=new u([],{type:arguments[2]});return r[m]=s,r}}function c(e,a,p){Error.call(this,e),this.message=e,this.type=a,p&&(this.code=this.errno=p.code),Error.captureStackTrace(this,this.constructor)}Object.defineProperties(u.prototype,{size:{enumerable:!0},type:{enumerable:!0},slice:{enumerable:!0}}),Object.defineProperty(u.prototype,Symbol.toStringTag,{value:"Blob",writable:!1,enumerable:!1,configurable:!0}),c.prototype=Object.create(Error.prototype),c.prototype.constructor=c,c.prototype.name="FetchError";let v=Symbol("Body internals"),f=t.PassThrough;function w(e){var a=this,p=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},d=p.size,i=p.timeout;null==e?e=null:g(e)?e=Buffer.from(e.toString()):y(e)||Buffer.isBuffer(e)||("[object ArrayBuffer]"===Object.prototype.toString.call(e)?e=Buffer.from(e):ArrayBuffer.isView(e)?e=Buffer.from(e.buffer,e.byteOffset,e.byteLength):e instanceof t||(e=Buffer.from(String(e)))),this[v]={body:e,disturbed:!1,error:null},this.size=void 0===d?0:d,this.timeout=void 0===i?0:i,e instanceof t&&e.on("error",function(e){let p="AbortError"===e.name?e:new c(`Invalid response body while trying to fetch ${a.url}: ${e.message}`,"system",e);a[v].error=p})}function _(){var e=this;if(this[v].disturbed)return w.Promise.reject(TypeError(`body used already for: ${this.url}`));if(this[v].disturbed=!0,this[v].error)return w.Promise.reject(this[v].error);let a=this.body;if(null===a)return w.Promise.resolve(Buffer.alloc(0));if(y(a)&&(a=a.stream()),Buffer.isBuffer(a))return w.Promise.resolve(a);if(!(a instanceof t))return w.Promise.resolve(Buffer.alloc(0));let p=[],d=0,i=!1;return new w.Promise(function(t,s){let r;e.timeout&&(r=setTimeout(function(){i=!0,s(new c(`Response timeout while trying to fetch ${e.url} (over ${e.timeout}ms)`,"body-timeout"))},e.timeout)),a.on("error",function(a){"AbortError"===a.name?(i=!0,s(a)):s(new c(`Invalid response body while trying to fetch ${e.url}: ${a.message}`,"system",a))}),a.on("data",function(a){if(!i&&null!==a){if(e.size&&d+a.length>e.size){i=!0,s(new c(`content size at ${e.url} over limit: ${e.size}`,"max-size"));return}d+=a.length,p.push(a)}}),a.on("end",function(){if(!i){clearTimeout(r);try{t(Buffer.concat(p,d))}catch(a){s(new c(`Could not create Buffer from response body for ${e.url}: ${a.message}`,"system",a))}}})})}function g(e){return"object"==typeof e&&"function"==typeof e.append&&"function"==typeof e.delete&&"function"==typeof e.get&&"function"==typeof e.getAll&&"function"==typeof e.has&&"function"==typeof e.set&&("URLSearchParams"===e.constructor.name||"[object URLSearchParams]"===Object.prototype.toString.call(e)||"function"==typeof e.sort)}function y(e){return"object"==typeof e&&"function"==typeof e.arrayBuffer&&"string"==typeof e.type&&"function"==typeof e.stream&&"function"==typeof e.constructor&&"string"==typeof e.constructor.name&&/^(Blob|File)$/.test(e.constructor.name)&&/^(Blob|File)$/.test(e[Symbol.toStringTag])}function b(e){let a,p,d=e.body;if(e.bodyUsed)throw Error("cannot clone body after it is used");return d instanceof t&&"function"!=typeof d.getBoundary&&(a=new f,p=new f,d.pipe(a),d.pipe(p),e[v].body=a,d=p),d}function S(e){if(null===e)return null;if("string"==typeof e)return"text/plain;charset=UTF-8";if(g(e))return"application/x-www-form-urlencoded;charset=UTF-8";if(y(e))return e.type||null;if(Buffer.isBuffer(e))return null;else if("[object ArrayBuffer]"===Object.prototype.toString.call(e))return null;else if(ArrayBuffer.isView(e))return null;else if("function"==typeof e.getBoundary)return`multipart/form-data;boundary=${e.getBoundary()}`;else if(e instanceof t)return null;else return"text/plain;charset=UTF-8"}function T(e){let a=e.body;return null===a?0:y(a)?a.size:Buffer.isBuffer(a)?a.length:a&&"function"==typeof a.getLengthSync?a._lengthRetrievers&&0==a._lengthRetrievers.length||a.hasKnownLength&&a.hasKnownLength()?a.getLengthSync():null:null}w.prototype={get body(){return this[v].body},get bodyUsed(){return this[v].disturbed},arrayBuffer(){return _.call(this).then(function(e){return e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength)})},blob(){let e=this.headers&&this.headers.get("content-type")||"";return _.call(this).then(function(a){return Object.assign(new u([],{type:e.toLowerCase()}),{[m]:a})})},json(){var e=this;return _.call(this).then(function(a){try{return JSON.parse(a.toString())}catch(a){return w.Promise.reject(new c(`invalid json response body at ${e.url} reason: ${a.message}`,"invalid-json"))}})},text(){return _.call(this).then(function(e){return e.toString()})},buffer(){return _.call(this)},textConverted(){var e=this;return _.call(this).then(function(a){var p=0,d=e.headers;throw Error("The package `encoding` must be installed to use the textConverted() function")})}},Object.defineProperties(w.prototype,{body:{enumerable:!0},bodyUsed:{enumerable:!0},arrayBuffer:{enumerable:!0},blob:{enumerable:!0},json:{enumerable:!0},text:{enumerable:!0}}),w.mixIn=function(e){for(let a of Object.getOwnPropertyNames(w.prototype))if(!(a in e)){let p=Object.getOwnPropertyDescriptor(w.prototype,a);Object.defineProperty(e,a,p)}},w.Promise=global.Promise;let N=/[^\^_`a-zA-Z\-0-9!#$%&'*+.|~]/,k=/[^\t\x20-\x7e\x80-\xff]/;function E(e){if(e=`${e}`,N.test(e)||""===e)throw TypeError(`${e} is not a legal HTTP header name`)}function D(e){if(e=`${e}`,k.test(e))throw TypeError(`${e} is not a legal HTTP header value`)}function V(e,a){for(let p in a=a.toLowerCase(),e)if(p.toLowerCase()===a)return p}let O=Symbol("map");class C{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0;if(this[O]=Object.create(null),e instanceof C){let a=e.raw();for(let e of Object.keys(a))for(let p of a[e])this.append(e,p);return}if(null==e);else if("object"==typeof e){let a=e[Symbol.iterator];if(null!=a){if("function"!=typeof a)throw TypeError("Header pairs must be iterable");let p=[];for(let a of e){if("object"!=typeof a||"function"!=typeof a[Symbol.iterator])throw TypeError("Each header pair must be iterable");p.push(Array.from(a))}for(let e of p){if(2!==e.length)throw TypeError("Each header pair must be a name/value tuple");this.append(e[0],e[1])}}else for(let a of Object.keys(e)){let p=e[a];this.append(a,p)}}else throw TypeError("Provided initializer must be an object")}get(e){E(e=`${e}`);let a=V(this[O],e);return void 0===a?null:this[O][a].join(", ")}forEach(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,p=A(this),d=0;for(;d<p.length;){var t=p[d];let i=t[0],s=t[1];e.call(a,s,i,this),p=A(this),d++}}set(e,a){e=`${e}`,a=`${a}`,E(e),D(a);let p=V(this[O],e);this[O][void 0!==p?p:e]=[a]}append(e,a){e=`${e}`,a=`${a}`,E(e),D(a);let p=V(this[O],e);void 0!==p?this[O][p].push(a):this[O][e]=[a]}has(e){return E(e=`${e}`),void 0!==V(this[O],e)}delete(e){E(e=`${e}`);let a=V(this[O],e);void 0!==a&&delete this[O][a]}raw(){return this[O]}keys(){return P(this,"key")}values(){return P(this,"value")}[Symbol.iterator](){return P(this,"key+value")}}function A(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"key+value";return Object.keys(e[O]).sort().map("key"===a?function(e){return e.toLowerCase()}:"value"===a?function(a){return e[O][a].join(", ")}:function(a){return[a.toLowerCase(),e[O][a].join(", ")]})}C.prototype.entries=C.prototype[Symbol.iterator],Object.defineProperty(C.prototype,Symbol.toStringTag,{value:"Headers",writable:!1,enumerable:!1,configurable:!0}),Object.defineProperties(C.prototype,{get:{enumerable:!0},forEach:{enumerable:!0},set:{enumerable:!0},append:{enumerable:!0},has:{enumerable:!0},delete:{enumerable:!0},keys:{enumerable:!0},values:{enumerable:!0},entries:{enumerable:!0}});let x=Symbol("internal");function P(e,a){let p=Object.create(j);return p[x]={target:e,kind:a,index:0},p}let j=Object.setPrototypeOf({next(){if(!this||Object.getPrototypeOf(this)!==j)throw TypeError("Value of `this` is not a HeadersIterator");var e=this[x];let a=e.target,p=e.kind,d=e.index,t=A(a,p);return d>=t.length?{value:void 0,done:!0}:(this[x].index=d+1,{value:t[d],done:!1})}},Object.getPrototypeOf(Object.getPrototypeOf([][Symbol.iterator]())));Object.defineProperty(j,Symbol.toStringTag,{value:"HeadersIterator",writable:!1,enumerable:!1,configurable:!0});let R=Symbol("Response internals"),L=i.STATUS_CODES;class B{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};w.call(this,e,a);let p=a.status||200,d=new C(a.headers);if(null!=e&&!d.has("Content-Type")){let a=S(e);a&&d.append("Content-Type",a)}this[R]={url:a.url,status:p,statusText:a.statusText||L[p],headers:d,counter:a.counter}}get url(){return this[R].url||""}get status(){return this[R].status}get ok(){return this[R].status>=200&&this[R].status<300}get redirected(){return this[R].counter>0}get statusText(){return this[R].statusText}get headers(){return this[R].headers}clone(){return new B(b(this),{url:this.url,status:this.status,statusText:this.statusText,headers:this.headers,ok:this.ok,redirected:this.redirected})}}w.mixIn(B.prototype),Object.defineProperties(B.prototype,{url:{enumerable:!0},status:{enumerable:!0},ok:{enumerable:!0},redirected:{enumerable:!0},statusText:{enumerable:!0},headers:{enumerable:!0},clone:{enumerable:!0}}),Object.defineProperty(B.prototype,Symbol.toStringTag,{value:"Response",writable:!1,enumerable:!1,configurable:!0});let I=Symbol("Request internals"),$=s.URL||r.URL,U=s.parse,F=s.format;function M(e){return/^[a-zA-Z][a-zA-Z\d+\-.]*:/.exec(e)&&(e=new $(e).toString()),U(e)}let q="destroy"in t.Readable.prototype;function W(e){return"object"==typeof e&&"object"==typeof e[I]}class z{constructor(e){let a,p=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};W(e)?a=M(e.url):(a=e&&e.href?M(e.href):M(`${e}`),e={});let d=p.method||e.method||"GET";if(d=d.toUpperCase(),(null!=p.body||W(e)&&null!==e.body)&&("GET"===d||"HEAD"===d))throw TypeError("Request with GET/HEAD method cannot have body");let t=null!=p.body?p.body:W(e)&&null!==e.body?b(e):null;w.call(this,t,{timeout:p.timeout||e.timeout||0,size:p.size||e.size||0});let i=new C(p.headers||e.headers||{});if(null!=t&&!i.has("Content-Type")){let e=S(t);e&&i.append("Content-Type",e)}let s=W(e)?e.signal:null;if("signal"in p&&(s=p.signal),null!=s&&!function(e){let a=e&&"object"==typeof e&&Object.getPrototypeOf(e);return!!(a&&"AbortSignal"===a.constructor.name)}(s))throw TypeError("Expected signal to be an instanceof AbortSignal");this[I]={method:d,redirect:p.redirect||e.redirect||"follow",headers:i,parsedURL:a,signal:s},this.follow=void 0!==p.follow?p.follow:void 0!==e.follow?e.follow:20,this.compress=void 0!==p.compress?p.compress:void 0===e.compress||e.compress,this.counter=p.counter||e.counter||0,this.agent=p.agent||e.agent}get method(){return this[I].method}get url(){return F(this[I].parsedURL)}get headers(){return this[I].headers}get redirect(){return this[I].redirect}get signal(){return this[I].signal}clone(){return new z(this)}}function G(e){Error.call(this,e),this.type="aborted",this.message=e,Error.captureStackTrace(this,this.constructor)}w.mixIn(z.prototype),Object.defineProperty(z.prototype,Symbol.toStringTag,{value:"Request",writable:!1,enumerable:!1,configurable:!0}),Object.defineProperties(z.prototype,{method:{enumerable:!0},url:{enumerable:!0},headers:{enumerable:!0},redirect:{enumerable:!0},clone:{enumerable:!0},signal:{enumerable:!0}}),G.prototype=Object.create(Error.prototype),G.prototype.constructor=G,G.prototype.name="AbortError";let H=s.URL||r.URL,K=t.PassThrough,J=function(e,a){let p=new H(a).hostname,d=new H(e).hostname;return p===d||"."===p[p.length-d.length-1]&&p.endsWith(d)};function Y(e,a){if(!Y.Promise)throw Error("native promise missing, set fetch.Promise to your favorite alternative");return w.Promise=Y.Promise,new Y.Promise(function(p,d){var s,r;let n,m,h=new z(e,a),u=function(e){let a=e[I].parsedURL,p=new C(e[I].headers);if(p.has("Accept")||p.set("Accept","*/*"),!a.protocol||!a.hostname)throw TypeError("Only absolute URLs are supported");if(!/^https?:$/.test(a.protocol))throw TypeError("Only HTTP(S) protocols are supported");if(e.signal&&e.body instanceof t.Readable&&!q)throw Error("Cancellation of streamed requests with AbortSignal is not supported in node < 8");let d=null;if(null==e.body&&/^(POST|PUT)$/i.test(e.method)&&(d="0"),null!=e.body){let a=T(e);"number"==typeof a&&(d=String(a))}d&&p.set("Content-Length",d),p.has("User-Agent")||p.set("User-Agent","node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"),e.compress&&!p.has("Accept-Encoding")&&p.set("Accept-Encoding","gzip,deflate");let i=e.agent;return"function"==typeof i&&(i=i(a)),p.has("Connection")||i||p.set("Connection","close"),Object.assign({},a,{method:e.method,headers:function(e){let a=Object.assign({__proto__:null},e[O]),p=V(e[O],"Host");return void 0!==p&&(a[p]=a[p][0]),a}(p),agent:i})}(h),v=("https:"===u.protocol?l:i).request,f=h.signal,w=null,_=function(){let e=new G("The user aborted a request.");d(e),h.body&&h.body instanceof t.Readable&&X(h.body,e),w&&w.body&&w.body.emit("error",e)};if(f&&f.aborted)return void _();let g=function(){_(),S()},b=v(u);function S(){b.abort(),f&&f.removeEventListener("abort",g),clearTimeout(n)}f&&f.addEventListener("abort",g),h.timeout&&b.once("socket",function(e){n=setTimeout(function(){d(new c(`network timeout at: ${h.url}`,"request-timeout")),S()},h.timeout)}),b.on("error",function(e){d(new c(`request to ${h.url} failed, reason: ${e.message}`,"system",e)),w&&w.body&&X(w.body,e),S()}),s=b,r=function(e){(!f||!f.aborted)&&w&&w.body&&X(w.body,e)},s.on("socket",function(e){m=e}),s.on("response",function(e){let a=e.headers;"chunked"!==a["transfer-encoding"]||a["content-length"]||e.once("close",function(e){if(m&&m.listenerCount("data")>0&&!e){let e=Error("Premature close");e.code="ERR_STREAM_PREMATURE_CLOSE",r(e)}})}),14>parseInt(process.version.substring(1))&&b.on("socket",function(e){e.addListener("close",function(a){let p=e.listenerCount("data")>0;if(w&&p&&!a&&!(f&&f.aborted)){let e=Error("Premature close");e.code="ERR_STREAM_PREMATURE_CLOSE",w.body.emit("error",e)}})}),b.on("response",function(e){clearTimeout(n);let a=function(e){let a=new C;for(let p of Object.keys(e))if(!N.test(p))if(Array.isArray(e[p]))for(let d of e[p])k.test(d)||(void 0===a[O][p]?a[O][p]=[d]:a[O][p].push(d));else k.test(e[p])||(a[O][p]=[e[p]]);return a}(e.headers);if(Y.isRedirect(e.statusCode)){let i=a.get("Location"),s=null;try{s=null===i?null:new H(i,h.url).toString()}catch(e){if("manual"!==h.redirect){d(new c(`uri requested responds with an invalid redirect URL: ${i}`,"invalid-redirect")),S();return}}switch(h.redirect){case"error":d(new c(`uri requested responds with a redirect, redirect mode is set to error: ${h.url}`,"no-redirect")),S();return;case"manual":if(null!==s)try{a.set("Location",s)}catch(e){d(e)}break;case"follow":var t;if(null===s)break;if(h.counter>=h.follow){d(new c(`maximum redirect reached at: ${h.url}`,"max-redirect")),S();return}let r={headers:new C(h.headers),follow:h.follow,counter:h.counter+1,agent:h.agent,compress:h.compress,method:h.method,body:h.body,signal:h.signal,timeout:h.timeout,size:h.size};if(!J(h.url,s)||(t=h.url,new H(s).protocol!==new H(t).protocol))for(let e of["authorization","www-authenticate","cookie","cookie2"])r.headers.delete(e);if(303!==e.statusCode&&h.body&&null===T(h)){d(new c("Cannot follow redirect with body being a readable stream","unsupported-redirect")),S();return}(303===e.statusCode||(301===e.statusCode||302===e.statusCode)&&"POST"===h.method)&&(r.method="GET",r.body=void 0,r.headers.delete("content-length")),p(Y(new z(s,r))),S();return}}e.once("end",function(){f&&f.removeEventListener("abort",g)});let i=e.pipe(new K),s={url:h.url,status:e.statusCode,statusText:e.statusMessage,headers:a,size:h.size,timeout:h.timeout,counter:h.counter},r=a.get("Content-Encoding");if(!h.compress||"HEAD"===h.method||null===r||204===e.statusCode||304===e.statusCode)return void p(w=new B(i,s));let l={flush:o.Z_SYNC_FLUSH,finishFlush:o.Z_SYNC_FLUSH};if("gzip"==r||"x-gzip"==r)return void p(w=new B(i=i.pipe(o.createGunzip(l)),s));if("deflate"==r||"x-deflate"==r){let a=e.pipe(new K);a.once("data",function(e){p(w=new B(i=(15&e[0])==8?i.pipe(o.createInflate()):i.pipe(o.createInflateRaw()),s))}),a.on("end",function(){w||p(w=new B(i,s))});return}if("br"==r&&"function"==typeof o.createBrotliDecompress)return void p(w=new B(i=i.pipe(o.createBrotliDecompress()),s));p(w=new B(i,s))});let E=h.body;null===E?b.end():y(E)?E.stream().pipe(b):Buffer.isBuffer(E)?(b.write(E),b.end()):E.pipe(b)})}function X(e,a){e.destroy?e.destroy(a):(e.emit("error",a),e.end())}Y.isRedirect=function(e){return 301===e||302===e||303===e||307===e||308===e},Y.Promise=global.Promise,e.exports=a=Y,Object.defineProperty(a,"__esModule",{value:!0}),a.default=a,a.Headers=C,a.Request=z,a.Response=B,a.FetchError=c},86693:function(e,a,p){var d=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(a,"__esModule",{value:!0});let t=d(p(88699));class i extends t.default{select(e){let a=!1,p=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!a?"":('"'===e&&(a=!a),e)).join("");return this.url.searchParams.set("select",p),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:a=!0,nullsFirst:p,foreignTable:d,referencedTable:t=d}={}){let i=t?`${t}.order`:"order",s=this.url.searchParams.get(i);return this.url.searchParams.set(i,`${s?`${s},`:""}${e}.${a?"asc":"desc"}${void 0===p?"":p?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:a,referencedTable:p=a}={}){let d=void 0===p?"limit":`${p}.limit`;return this.url.searchParams.set(d,`${e}`),this}range(e,a,{foreignTable:p,referencedTable:d=p}={}){let t=void 0===d?"offset":`${d}.offset`,i=void 0===d?"limit":`${d}.limit`;return this.url.searchParams.set(t,`${e}`),this.url.searchParams.set(i,`${a-e+1}`),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:a=!1,settings:p=!1,buffers:d=!1,wal:t=!1,format:i="text"}={}){var s;let r=[e?"analyze":null,a?"verbose":null,p?"settings":null,d?"buffers":null,t?"wal":null].filter(Boolean).join("|"),l=null!=(s=this.headers.Accept)?s:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${i}; for="${l}"; options=${r};`,this}rollback(){var e;return(null!=(e=this.headers.Prefer)?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}a.default=i},88699:function(e,a,p){var d=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(a,"__esModule",{value:!0});let t=d(p(83899)),i=d(p(20246));class s{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=t.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,a){return this.headers=Object.assign({},this.headers),this.headers[e]=a,this}then(e,a){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let p=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var a,p,d;let t=null,s=null,r=null,l=e.status,o=e.statusText;if(e.ok){if("HEAD"!==this.method){let a=await e.text();""===a||(s="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?a:JSON.parse(a))}let d=null==(a=this.headers.Prefer)?void 0:a.match(/count=(exact|planned|estimated)/),i=null==(p=e.headers.get("content-range"))?void 0:p.split("/");d&&i&&i.length>1&&(r=parseInt(i[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(s)&&(s.length>1?(t={code:"PGRST116",details:`Results contain ${s.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},s=null,r=null,l=406,o="Not Acceptable"):s=1===s.length?s[0]:null)}else{let a=await e.text();try{t=JSON.parse(a),Array.isArray(t)&&404===e.status&&(s=[],t=null,l=200,o="OK")}catch(p){404===e.status&&""===a?(l=204,o="No Content"):t={message:a}}if(t&&this.isMaybeSingle&&(null==(d=null==t?void 0:t.details)?void 0:d.includes("0 rows"))&&(t=null,l=200,o="OK"),t&&this.shouldThrowOnError)throw new i.default(t)}return{error:t,data:s,count:r,status:l,statusText:o}});return this.shouldThrowOnError||(p=p.catch(e=>{var a,p,d;return{error:{message:`${null!=(a=null==e?void 0:e.name)?a:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!=(p=null==e?void 0:e.stack)?p:""}`,hint:"",code:`${null!=(d=null==e?void 0:e.code)?d:""}`},data:null,count:null,status:0,statusText:""}})),p.then(e,a)}returns(){return this}overrideTypes(){return this}}a.default=s},89771:e=>{let a=Symbol("kDone"),p=Symbol("kRun");class d{constructor(e){this[a]=()=>{this.pending--,this[p]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[p]()}[p](){if(this.pending!==this.concurrency&&this.jobs.length){let e=this.jobs.shift();this.pending++,e(this[a])}}}e.exports=d},90939:(e,a,p)=>{p(97628);let{Duplex:d}=p(27910);function t(e){e.emit("close")}function i(){!this.destroyed&&this._writableState.finished&&this.destroy()}function s(e){this.removeListener("error",s),this.destroy(),0===this.listenerCount("error")&&this.emit("error",e)}e.exports=function(e,a){let p=!0,r=new d({...a,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return e.on("message",function(a,p){let d=!p&&r._readableState.objectMode?a.toString():a;r.push(d)||e.pause()}),e.once("error",function(e){r.destroyed||(p=!1,r.destroy(e))}),e.once("close",function(){r.destroyed||r.push(null)}),r._destroy=function(a,d){if(e.readyState===e.CLOSED){d(a),process.nextTick(t,r);return}let i=!1;e.once("error",function(e){i=!0,d(e)}),e.once("close",function(){i||d(a),process.nextTick(t,r)}),p&&e.terminate()},r._final=function(a){if(e.readyState===e.CONNECTING)return void e.once("open",function(){r._final(a)});null!==e._socket&&(e._socket._writableState.finished?(a(),r._readableState.endEmitted&&r.destroy()):(e._socket.once("finish",function(){a()}),e.close()))},r._read=function(){e.isPaused&&e.resume()},r._write=function(a,p,d){if(e.readyState===e.CONNECTING)return void e.once("open",function(){r._write(a,p,d)});e.send(a,d)},r.on("end",i),r.on("error",s),r}},95515:(e,a,p)=>{let d=p(32980);a.implementation=class{constructor(e){let a=e[0],p=e[1],t=null;if(void 0!==p&&"failure"===(t=d.basicURLParse(p)))throw TypeError("Invalid base URL");let i=d.basicURLParse(a,{baseURL:t});if("failure"===i)throw TypeError("Invalid URL");this._url=i}get href(){return d.serializeURL(this._url)}set href(e){let a=d.basicURLParse(e);if("failure"===a)throw TypeError("Invalid URL");this._url=a}get origin(){return d.serializeURLOrigin(this._url)}get protocol(){return this._url.scheme+":"}set protocol(e){d.basicURLParse(e+":",{url:this._url,stateOverride:"scheme start"})}get username(){return this._url.username}set username(e){d.cannotHaveAUsernamePasswordPort(this._url)||d.setTheUsername(this._url,e)}get password(){return this._url.password}set password(e){d.cannotHaveAUsernamePasswordPort(this._url)||d.setThePassword(this._url,e)}get host(){let e=this._url;return null===e.host?"":null===e.port?d.serializeHost(e.host):d.serializeHost(e.host)+":"+d.serializeInteger(e.port)}set host(e){this._url.cannotBeABaseURL||d.basicURLParse(e,{url:this._url,stateOverride:"host"})}get hostname(){return null===this._url.host?"":d.serializeHost(this._url.host)}set hostname(e){this._url.cannotBeABaseURL||d.basicURLParse(e,{url:this._url,stateOverride:"hostname"})}get port(){return null===this._url.port?"":d.serializeInteger(this._url.port)}set port(e){d.cannotHaveAUsernamePasswordPort(this._url)||(""===e?this._url.port=null:d.basicURLParse(e,{url:this._url,stateOverride:"port"}))}get pathname(){return this._url.cannotBeABaseURL?this._url.path[0]:0===this._url.path.length?"":"/"+this._url.path.join("/")}set pathname(e){this._url.cannotBeABaseURL||(this._url.path=[],d.basicURLParse(e,{url:this._url,stateOverride:"path start"}))}get search(){return null===this._url.query||""===this._url.query?"":"?"+this._url.query}set search(e){let a=this._url;if(""===e){a.query=null;return}let p="?"===e[0]?e.substring(1):e;a.query="",d.basicURLParse(p,{url:a,stateOverride:"query"})}get hash(){return null===this._url.fragment||""===this._url.fragment?"":"#"+this._url.fragment}set hash(e){if(""===e){this._url.fragment=null;return}let a="#"===e[0]?e.substring(1):e;this._url.fragment="",d.basicURLParse(a,{url:this._url,stateOverride:"fragment"})}toJSON(){return this.href}}},97628:(e,a,p)=>{let d=p(94735),t=p(55591),i=p(81630),s=p(91645),r=p(34631),{randomBytes:l,createHash:o}=p(55511),{Duplex:n,Readable:m}=p(27910),{URL:h}=p(79551),u=p(7367),c=p(36122),v=p(8630),{isBlob:f}=p(10248),{BINARY_TYPES:w,EMPTY_BUFFER:_,GUID:g,kForOnEventAttribute:y,kListener:b,kStatusCode:S,kWebSocket:T,NOOP:N}=p(31394),{EventTarget:{addEventListener:k,removeEventListener:E}}=p(78677),{format:D,parse:V}=p(47762),{toBuffer:O}=p(12942),C=Symbol("kAborted"),A=[8,13],x=["CONNECTING","OPEN","CLOSING","CLOSED"],P=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/;class j extends d{constructor(e,a,p){super(),this._binaryType=w[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=_,this._closeTimer=null,this._errorEmitted=!1,this._extensions={},this._paused=!1,this._protocol="",this._readyState=j.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,null!==e?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,void 0===a?a=[]:Array.isArray(a)||("object"==typeof a&&null!==a?(p=a,a=[]):a=[a]),function e(a,p,d,s){let r,n,m,c,v={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:A[1],maxPayload:0x6400000,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...s,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(a._autoPong=v.autoPong,!A.includes(v.protocolVersion))throw RangeError(`Unsupported protocol version: ${v.protocolVersion} (supported versions: ${A.join(", ")})`);if(p instanceof h)r=p;else try{r=new h(p)}catch(e){throw SyntaxError(`Invalid URL: ${p}`)}"http:"===r.protocol?r.protocol="ws:":"https:"===r.protocol&&(r.protocol="wss:"),a._url=r.href;let f="wss:"===r.protocol,w="ws+unix:"===r.protocol;if("ws:"===r.protocol||f||w?w&&!r.pathname?n="The URL's pathname is empty":r.hash&&(n="The URL contains a fragment identifier"):n='The URL\'s protocol must be one of "ws:", "wss:", "http:", "https:", or "ws+unix:"',n){let e=SyntaxError(n);if(0!==a._redirects)return void R(a,e);throw e}let _=f?443:80,y=l(16).toString("base64"),b=f?t.request:i.request,S=new Set;if(v.createConnection=v.createConnection||(f?B:L),v.defaultPort=v.defaultPort||_,v.port=r.port||_,v.host=r.hostname.startsWith("[")?r.hostname.slice(1,-1):r.hostname,v.headers={...v.headers,"Sec-WebSocket-Version":v.protocolVersion,"Sec-WebSocket-Key":y,Connection:"Upgrade",Upgrade:"websocket"},v.path=r.pathname+r.search,v.timeout=v.handshakeTimeout,v.perMessageDeflate&&(m=new u(!0!==v.perMessageDeflate?v.perMessageDeflate:{},!1,v.maxPayload),v.headers["Sec-WebSocket-Extensions"]=D({[u.extensionName]:m.offer()})),d.length){for(let e of d){if("string"!=typeof e||!P.test(e)||S.has(e))throw SyntaxError("An invalid or duplicated subprotocol was specified");S.add(e)}v.headers["Sec-WebSocket-Protocol"]=d.join(",")}if(v.origin&&(v.protocolVersion<13?v.headers["Sec-WebSocket-Origin"]=v.origin:v.headers.Origin=v.origin),(r.username||r.password)&&(v.auth=`${r.username}:${r.password}`),w){let e=v.path.split(":");v.socketPath=e[0],v.path=e[1]}if(v.followRedirects){if(0===a._redirects){a._originalIpc=w,a._originalSecure=f,a._originalHostOrSocketPath=w?v.socketPath:r.host;let e=s&&s.headers;if(s={...s,headers:{}},e)for(let[a,p]of Object.entries(e))s.headers[a.toLowerCase()]=p}else if(0===a.listenerCount("redirect")){let e=w?!!a._originalIpc&&v.socketPath===a._originalHostOrSocketPath:!a._originalIpc&&r.host===a._originalHostOrSocketPath;e&&(!a._originalSecure||f)||(delete v.headers.authorization,delete v.headers.cookie,e||delete v.headers.host,v.auth=void 0)}v.auth&&!s.headers.authorization&&(s.headers.authorization="Basic "+Buffer.from(v.auth).toString("base64")),c=a._req=b(v),a._redirects&&a.emit("redirect",a.url,c)}else c=a._req=b(v);v.timeout&&c.on("timeout",()=>{I(a,c,"Opening handshake has timed out")}),c.on("error",e=>{null===c||c[C]||(c=a._req=null,R(a,e))}),c.on("response",t=>{let i=t.headers.location,r=t.statusCode;if(i&&v.followRedirects&&r>=300&&r<400){let t;if(++a._redirects>v.maxRedirects)return void I(a,c,"Maximum redirects exceeded");c.abort();try{t=new h(i,p)}catch(e){R(a,SyntaxError(`Invalid URL: ${i}`));return}e(a,t,d,s)}else a.emit("unexpected-response",c,t)||I(a,c,`Unexpected server response: ${t.statusCode}`)}),c.on("upgrade",(e,p,d)=>{let t;if(a.emit("upgrade",e),a.readyState!==j.CONNECTING)return;c=a._req=null;let i=e.headers.upgrade;if(void 0===i||"websocket"!==i.toLowerCase())return void I(a,p,"Invalid Upgrade header");let s=o("sha1").update(y+g).digest("base64");if(e.headers["sec-websocket-accept"]!==s)return void I(a,p,"Invalid Sec-WebSocket-Accept header");let r=e.headers["sec-websocket-protocol"];if(void 0!==r?S.size?S.has(r)||(t="Server sent an invalid subprotocol"):t="Server sent a subprotocol but none was requested":S.size&&(t="Server sent no subprotocol"),t)return void I(a,p,t);r&&(a._protocol=r);let l=e.headers["sec-websocket-extensions"];if(void 0!==l){let e;if(!m)return void I(a,p,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");try{e=V(l)}catch(e){I(a,p,"Invalid Sec-WebSocket-Extensions header");return}let d=Object.keys(e);if(1!==d.length||d[0]!==u.extensionName)return void I(a,p,"Server indicated an extension that was not requested");try{m.accept(e[u.extensionName])}catch(e){I(a,p,"Invalid Sec-WebSocket-Extensions header");return}a._extensions[u.extensionName]=m}a.setSocket(p,d,{allowSynchronousEvents:v.allowSynchronousEvents,generateMask:v.generateMask,maxPayload:v.maxPayload,skipUTF8Validation:v.skipUTF8Validation})}),v.finishRequest?v.finishRequest(c,a):c.end()}(this,e,a,p)):(this._autoPong=p.autoPong,this._isServer=!0)}get binaryType(){return this._binaryType}set binaryType(e){w.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,a,p){let d=new c({allowSynchronousEvents:p.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:p.maxPayload,skipUTF8Validation:p.skipUTF8Validation}),t=new v(e,this._extensions,p.generateMask);this._receiver=d,this._sender=t,this._socket=e,d[T]=this,t[T]=this,e[T]=this,d.on("conclude",U),d.on("drain",F),d.on("error",M),d.on("message",W),d.on("ping",z),d.on("pong",G),t.onerror=K,e.setTimeout&&e.setTimeout(0),e.setNoDelay&&e.setNoDelay(),a.length>0&&e.unshift(a),e.on("close",Y),e.on("data",X),e.on("end",Z),e.on("error",Q),this._readyState=j.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=j.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[u.extensionName]&&this._extensions[u.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=j.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,a){if(this.readyState!==j.CLOSED){if(this.readyState===j.CONNECTING)return void I(this,this._req,"WebSocket was closed before the connection was established");if(this.readyState===j.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=j.CLOSING,this._sender.close(e,a,!this._isServer,e=>{!e&&(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),J(this)}}pause(){this.readyState!==j.CONNECTING&&this.readyState!==j.CLOSED&&(this._paused=!0,this._socket.pause())}ping(e,a,p){if(this.readyState===j.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(p=e,e=a=void 0):"function"==typeof a&&(p=a,a=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==j.OPEN)return void $(this,e,p);void 0===a&&(a=!this._isServer),this._sender.ping(e||_,a,p)}pong(e,a,p){if(this.readyState===j.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(p=e,e=a=void 0):"function"==typeof a&&(p=a,a=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==j.OPEN)return void $(this,e,p);void 0===a&&(a=!this._isServer),this._sender.pong(e||_,a,p)}resume(){this.readyState!==j.CONNECTING&&this.readyState!==j.CLOSED&&(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,a,p){if(this.readyState===j.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof a&&(p=a,a={}),"number"==typeof e&&(e=e.toString()),this.readyState!==j.OPEN)return void $(this,e,p);let d={binary:"string"!=typeof e,mask:!this._isServer,compress:!0,fin:!0,...a};this._extensions[u.extensionName]||(d.compress=!1),this._sender.send(e||_,d,p)}terminate(){if(this.readyState!==j.CLOSED){if(this.readyState===j.CONNECTING)return void I(this,this._req,"WebSocket was closed before the connection was established");this._socket&&(this._readyState=j.CLOSING,this._socket.destroy())}}}function R(e,a){e._readyState=j.CLOSING,e._errorEmitted=!0,e.emit("error",a),e.emitClose()}function L(e){return e.path=e.socketPath,s.connect(e)}function B(e){return e.path=void 0,e.servername||""===e.servername||(e.servername=s.isIP(e.host)?"":e.host),r.connect(e)}function I(e,a,p){e._readyState=j.CLOSING;let d=Error(p);Error.captureStackTrace(d,I),a.setHeader?(a[C]=!0,a.abort(),a.socket&&!a.socket.destroyed&&a.socket.destroy(),process.nextTick(R,e,d)):(a.destroy(d),a.once("error",e.emit.bind(e,"error")),a.once("close",e.emitClose.bind(e)))}function $(e,a,p){if(a){let p=f(a)?a.size:O(a).length;e._socket?e._sender._bufferedBytes+=p:e._bufferedAmount+=p}if(p){let a=Error(`WebSocket is not open: readyState ${e.readyState} (${x[e.readyState]})`);process.nextTick(p,a)}}function U(e,a){let p=this[T];p._closeFrameReceived=!0,p._closeMessage=a,p._closeCode=e,void 0!==p._socket[T]&&(p._socket.removeListener("data",X),process.nextTick(H,p._socket),1005===e?p.close():p.close(e,a))}function F(){let e=this[T];e.isPaused||e._socket.resume()}function M(e){let a=this[T];void 0!==a._socket[T]&&(a._socket.removeListener("data",X),process.nextTick(H,a._socket),a.close(e[S])),a._errorEmitted||(a._errorEmitted=!0,a.emit("error",e))}function q(){this[T].emitClose()}function W(e,a){this[T].emit("message",e,a)}function z(e){let a=this[T];a._autoPong&&a.pong(e,!this._isServer,N),a.emit("ping",e)}function G(e){this[T].emit("pong",e)}function H(e){e.resume()}function K(e){let a=this[T];a.readyState!==j.CLOSED&&(a.readyState===j.OPEN&&(a._readyState=j.CLOSING,J(a)),this._socket.end(),a._errorEmitted||(a._errorEmitted=!0,a.emit("error",e)))}function J(e){e._closeTimer=setTimeout(e._socket.destroy.bind(e._socket),3e4)}function Y(){let e,a=this[T];this.removeListener("close",Y),this.removeListener("data",X),this.removeListener("end",Z),a._readyState=j.CLOSING,this._readableState.endEmitted||a._closeFrameReceived||a._receiver._writableState.errorEmitted||null===(e=a._socket.read())||a._receiver.write(e),a._receiver.end(),this[T]=void 0,clearTimeout(a._closeTimer),a._receiver._writableState.finished||a._receiver._writableState.errorEmitted?a.emitClose():(a._receiver.on("error",q),a._receiver.on("finish",q))}function X(e){this[T]._receiver.write(e)||this.pause()}function Z(){let e=this[T];e._readyState=j.CLOSING,e._receiver.end(),this.end()}function Q(){let e=this[T];this.removeListener("error",Q),this.on("error",N),e&&(e._readyState=j.CLOSING,this.destroy())}Object.defineProperty(j,"CONNECTING",{enumerable:!0,value:x.indexOf("CONNECTING")}),Object.defineProperty(j.prototype,"CONNECTING",{enumerable:!0,value:x.indexOf("CONNECTING")}),Object.defineProperty(j,"OPEN",{enumerable:!0,value:x.indexOf("OPEN")}),Object.defineProperty(j.prototype,"OPEN",{enumerable:!0,value:x.indexOf("OPEN")}),Object.defineProperty(j,"CLOSING",{enumerable:!0,value:x.indexOf("CLOSING")}),Object.defineProperty(j.prototype,"CLOSING",{enumerable:!0,value:x.indexOf("CLOSING")}),Object.defineProperty(j,"CLOSED",{enumerable:!0,value:x.indexOf("CLOSED")}),Object.defineProperty(j.prototype,"CLOSED",{enumerable:!0,value:x.indexOf("CLOSED")}),["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(e=>{Object.defineProperty(j.prototype,e,{enumerable:!0})}),["open","error","close","message"].forEach(e=>{Object.defineProperty(j.prototype,`on${e}`,{enumerable:!0,get(){for(let a of this.listeners(e))if(a[y])return a[b];return null},set(a){for(let a of this.listeners(e))if(a[y]){this.removeListener(e,a);break}"function"==typeof a&&this.addEventListener(e,a,{[y]:!0})}})}),j.prototype.addEventListener=k,j.prototype.removeEventListener=E,e.exports=j}};