(()=>{var e={};e.id=492,e.ids=[492],e.modules={154:(e,i,r)=>{Promise.resolve().then(r.t.bind(r,385,23)),Promise.resolve().then(r.t.bind(r,3737,23)),Promise.resolve().then(r.t.bind(r,6081,23)),Promise.resolve().then(r.t.bind(r,1904,23)),Promise.resolve().then(r.t.bind(r,5856,23)),Promise.resolve().then(r.t.bind(r,5492,23)),Promise.resolve().then(r.t.bind(r,9082,23)),Promise.resolve().then(r.t.bind(r,5812,23))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1971:()=>{},2002:(e,i,r)=>{Promise.resolve().then(r.t.bind(r,9355,23)),Promise.resolve().then(r.t.bind(r,4439,23)),Promise.resolve().then(r.t.bind(r,7851,23)),Promise.resolve().then(r.t.bind(r,4730,23)),Promise.resolve().then(r.t.bind(r,9774,23)),Promise.resolve().then(r.t.bind(r,3170,23)),Promise.resolve().then(r.t.bind(r,968,23)),Promise.resolve().then(r.t.bind(r,8298,23))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3514:()=>{},3873:e=>{"use strict";e.exports=require("path")},4356:(e,i,r)=>{"use strict";r.r(i),r.d(i,{default:()=>s,metadata:()=>o});var a=r(8828),t=r(7666),n=r.n(t);r(1971);let o={title:{default:"CHIA - Portal Ciudadano Digital",template:"%s | CHIA - Portal Ciudadano"},description:"Portal oficial de servicios ciudadanos para el municipio de Ch\xeda, Cundinamarca. Realiza tr\xe1mites en l\xednea, consulta informaci\xf3n municipal y accede a servicios digitales las 24 horas.",keywords:["Ch\xeda","Cundinamarca","servicios ciudadanos","gobierno digital","tr\xe1mites en l\xednea","certificados","impuestos","licencias","portal ciudadano","alcald\xeda","municipio"],authors:[{name:"Alcald\xeda Municipal de Ch\xeda"}],creator:"Alcald\xeda Municipal de Ch\xeda",publisher:"Alcald\xeda Municipal de Ch\xeda",viewport:"width=device-width, initial-scale=1",robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},openGraph:{type:"website",locale:"es_CO",url:"https://portal.chia-cundinamarca.gov.co",siteName:"CHIA - Portal Ciudadano",title:"CHIA - Portal Ciudadano Digital",description:"Portal oficial de servicios ciudadanos para el municipio de Ch\xeda, Cundinamarca. Tr\xe1mites en l\xednea las 24 horas.",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"Portal Ciudadano de Ch\xeda"}]},twitter:{card:"summary_large_image",title:"CHIA - Portal Ciudadano Digital",description:"Portal oficial de servicios ciudadanos para el municipio de Ch\xeda, Cundinamarca.",images:["/og-image.jpg"],creator:"@AlcaldiaChia"},alternates:{canonical:"https://portal.chia-cundinamarca.gov.co"},category:"government"};function s({children:e}){return(0,a.jsx)("html",{lang:"es",className:"h-full",children:(0,a.jsx)("body",{className:`${n().className} h-full`,children:(0,a.jsx)("div",{id:"root",className:"min-h-full",children:e})})})}},5362:()=>{},6732:(e,i,r)=>{"use strict";r.r(i),r.d(i,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var a=r(4332),t=r(8819),n=r(7851),o=r.n(n),s=r(7540),d={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>s[e]);r.d(i,d);let l={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,9033,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4356)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,2341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=[],u={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var i=require("../../webpack-runtime.js");i.C(e);var r=e=>i(i.s=e),a=i.X(0,[363,118],()=>r(6732));module.exports=a})();