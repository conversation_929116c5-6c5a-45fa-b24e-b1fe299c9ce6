(()=>{var e={};e.id=315,e.ids=[315],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13484:(e,r,t)=>{Promise.resolve().then(t.bind(t,62257)),Promise.resolve().then(t.t.bind(t,42671,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22614:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>p,tree:()=>c});var s=t(24332),n=t(48819),a=t(67851),o=t.n(a),i=t(97540),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let c={children:["",{children:["(auth)",{children:["auth",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,36586)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\(auth)\\auth\\login\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,86580)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\(auth)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,92341,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,34356)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\(auth)\\auth\\login\\page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(auth)/auth/login/page",pathname:"/auth/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33647:(e,r,t)=>{"use strict";t.d(r,{LoginForm:()=>i});var s=t(13486),n=t(60159),a=t(2984),o=t(34714);function i(){let[e,r]=(0,n.useState)(""),[t,i]=(0,n.useState)(""),[l,c]=(0,n.useState)(!1),[u,d]=(0,n.useState)(null),p=(0,a.useRouter)(),m=(0,o.createClientSupabase)(),x=async r=>{r.preventDefault(),c(!0),d(null);try{let{data:r,error:s}=await m.auth.signInWithPassword({email:e,password:t});if(s)return void d(s.message);r.user&&(p.push("/dashboard"),p.refresh())}catch(e){d("Error inesperado. Por favor intenta de nuevo."),console.error("Login error:",e)}finally{c(!1)}},h=async()=>{c(!0),d(null);try{let{error:e}=await m.auth.signInWithOAuth({provider:"google",options:{redirectTo:`${window.location.origin}/auth/callback`}});e&&d(e.message)}catch(e){d("Error inesperado. Por favor intenta de nuevo."),console.error("Google login error:",e)}finally{c(!1)}};return(0,s.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:x,children:[u&&(0,s.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,s.jsx)("div",{className:"text-sm text-red-700",children:u})}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"form-label",children:"Correo Electr\xf3nico"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"form-input",value:e,onChange:e=>r(e.target.value),disabled:l})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"form-label",children:"Contrase\xf1a"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,className:"form-input",value:t,onChange:e=>i(e.target.value),disabled:l})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("button",{type:"submit",disabled:l,className:"btn-primary w-full",children:l?"Iniciando sesi\xf3n...":"Iniciar Sesi\xf3n"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,s.jsx)("span",{className:"px-2 bg-gray-50 text-gray-500",children:"O contin\xfaa con"})})]}),(0,s.jsxs)("button",{type:"button",onClick:h,disabled:l,className:"btn-secondary w-full flex items-center justify-center",children:[(0,s.jsxs)("svg",{className:"w-5 h-5 mr-2",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Google"]})]})]})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},34714:()=>{throw Error("Module build failed (from ../../node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31mx\x1b[0m You're importing a component that needs \"next/headers\". That only works in a Server Component which is not supported in the pages/ directory. Read more: https://nextjs.org/docs/app/building-your-application/rendering/server-components\n  \x1b[31m|\x1b[0m\n\n   ,-[\x1b[36;1;4mC:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\lib\\supabase.ts\x1b[0m:3:1]\n \x1b[2m1\x1b[0m | import { createClient } from '@supabase/supabase-js';\n \x1b[2m2\x1b[0m | import { createClientComponentClient, createServerComponentClient } from '@supabase/auth-helpers-nextjs';\n \x1b[2m3\x1b[0m | import { cookies } from 'next/headers';\n   : \x1b[35;1m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\x1b[0m\n \x1b[2m4\x1b[0m | \n \x1b[2m5\x1b[0m | // Environment variables validation\n \x1b[2m6\x1b[0m | const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\n   `----\n")},36586:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(38828),n=t(42671),a=t.n(n),o=t(62257);function i(){return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100",children:(0,s.jsx)("svg",{className:"h-6 w-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})})}),(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Iniciar Sesi\xf3n"}),(0,s.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Accede a tu cuenta del portal ciudadano"})]}),(0,s.jsx)(o.LoginForm,{}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["\xbfNo tienes una cuenta?"," ",(0,s.jsx)(a(),{href:"/auth/register",className:"font-medium text-primary-600 hover:text-primary-500",children:"Reg\xedstrate aqu\xed"})]})})]})})}},42671:(e,r,t)=>{let{createProxy:s}=t(47927);e.exports=s("C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\node_modules\\next\\dist\\client\\app-dir\\link.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62257:(e,r,t)=>{"use strict";t.d(r,{LoginForm:()=>s});let s=(0,t(33952).registerClientReference)(function(){throw Error("Attempted to call LoginForm() from the server but LoginForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\components\\auth\\LoginForm.tsx","LoginForm")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77036:(e,r,t)=>{Promise.resolve().then(t.bind(t,33647)),Promise.resolve().then(t.t.bind(t,49989,23))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[363,118,114,85,900],()=>t(22614));module.exports=s})();