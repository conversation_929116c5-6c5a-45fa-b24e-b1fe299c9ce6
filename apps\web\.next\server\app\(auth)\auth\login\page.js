(()=>{var e={};e.id=315,e.ids=[315],e.modules={154:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,385,23)),Promise.resolve().then(t.t.bind(t,3737,23)),Promise.resolve().then(t.t.bind(t,6081,23)),Promise.resolve().then(t.t.bind(t,1904,23)),Promise.resolve().then(t.t.bind(t,5856,23)),Promise.resolve().then(t.t.bind(t,5492,23)),Promise.resolve().then(t.t.bind(t,9082,23)),Promise.resolve().then(t.t.bind(t,5812,23))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1971:()=>{},2002:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,9355,23)),Promise.resolve().then(t.t.bind(t,4439,23)),Promise.resolve().then(t.t.bind(t,7851,23)),Promise.resolve().then(t.t.bind(t,4730,23)),Promise.resolve().then(t.t.bind(t,9774,23)),Promise.resolve().then(t.t.bind(t,3170,23)),Promise.resolve().then(t.t.bind(t,968,23)),Promise.resolve().then(t.t.bind(t,8298,23))},2257:(e,r,t)=>{"use strict";t.d(r,{LoginForm:()=>a});let a=(0,t(3952).registerClientReference)(function(){throw Error("Attempted to call LoginForm() from the server but LoginForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\components\\auth\\LoginForm.tsx","LoginForm")},2614:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var a=t(4332),n=t(8819),s=t(7851),i=t.n(s),o=t(7540),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["(auth)",{children:["auth",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,6586)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\(auth)\\auth\\login\\page.tsx"]}]},{}]},{}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,9033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,2341,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,4356)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,2341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\(auth)\\auth\\login\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(auth)/auth/login/page",pathname:"/auth/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2671:(e,r,t)=>{let{createProxy:a}=t(7927);e.exports=a("C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\node_modules\\next\\dist\\client\\app-dir\\link.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3484:(e,r,t)=>{Promise.resolve().then(t.bind(t,2257)),Promise.resolve().then(t.t.bind(t,2671,23))},3514:()=>{},3647:(e,r,t)=>{"use strict";t.d(r,{LoginForm:()=>o});var a=t(3486),n=t(159),s=t(2984),i=t(4714);function o(){let[e,r]=(0,n.useState)(""),[t,o]=(0,n.useState)(""),[l,d]=(0,n.useState)(!1),[c,m]=(0,n.useState)(null),u=(0,s.useRouter)(),p=(0,i.createClientSupabase)(),h=async r=>{r.preventDefault(),d(!0),m(null);try{let{data:r,error:a}=await p.auth.signInWithPassword({email:e,password:t});if(a)return void m(a.message);r.user&&(u.push("/dashboard"),u.refresh())}catch(e){m("Error inesperado. Por favor intenta de nuevo."),console.error("Login error:",e)}finally{d(!1)}},x=async()=>{d(!0),m(null);try{let{error:e}=await p.auth.signInWithOAuth({provider:"google",options:{redirectTo:`${window.location.origin}/auth/callback`}});e&&m(e.message)}catch(e){m("Error inesperado. Por favor intenta de nuevo."),console.error("Google login error:",e)}finally{d(!1)}};return(0,a.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:h,children:[c&&(0,a.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,a.jsx)("div",{className:"text-sm text-red-700",children:c})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"form-label",children:"Correo Electr\xf3nico"}),(0,a.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"form-input",value:e,onChange:e=>r(e.target.value),disabled:l})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"form-label",children:"Contrase\xf1a"}),(0,a.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,className:"form-input",value:t,onChange:e=>o(e.target.value),disabled:l})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("button",{type:"submit",disabled:l,className:"btn-primary w-full",children:l?"Iniciando sesi\xf3n...":"Iniciar Sesi\xf3n"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,a.jsx)("span",{className:"px-2 bg-gray-50 text-gray-500",children:"O contin\xfaa con"})})]}),(0,a.jsxs)("button",{type:"button",onClick:x,disabled:l,className:"btn-secondary w-full flex items-center justify-center",children:[(0,a.jsxs)("svg",{className:"w-5 h-5 mr-2",viewBox:"0 0 24 24",children:[(0,a.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,a.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,a.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,a.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Google"]})]})]})}},3873:e=>{"use strict";e.exports=require("path")},4356:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o,metadata:()=>i});var a=t(8828),n=t(7666),s=t.n(n);t(1971);let i={title:{default:"CHIA - Portal Ciudadano Digital",template:"%s | CHIA - Portal Ciudadano"},description:"Portal oficial de servicios ciudadanos para el municipio de Ch\xeda, Cundinamarca. Realiza tr\xe1mites en l\xednea, consulta informaci\xf3n municipal y accede a servicios digitales las 24 horas.",keywords:["Ch\xeda","Cundinamarca","servicios ciudadanos","gobierno digital","tr\xe1mites en l\xednea","certificados","impuestos","licencias","portal ciudadano","alcald\xeda","municipio"],authors:[{name:"Alcald\xeda Municipal de Ch\xeda"}],creator:"Alcald\xeda Municipal de Ch\xeda",publisher:"Alcald\xeda Municipal de Ch\xeda",viewport:"width=device-width, initial-scale=1",robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},openGraph:{type:"website",locale:"es_CO",url:"https://portal.chia-cundinamarca.gov.co",siteName:"CHIA - Portal Ciudadano",title:"CHIA - Portal Ciudadano Digital",description:"Portal oficial de servicios ciudadanos para el municipio de Ch\xeda, Cundinamarca. Tr\xe1mites en l\xednea las 24 horas.",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"Portal Ciudadano de Ch\xeda"}]},twitter:{card:"summary_large_image",title:"CHIA - Portal Ciudadano Digital",description:"Portal oficial de servicios ciudadanos para el municipio de Ch\xeda, Cundinamarca.",images:["/og-image.jpg"],creator:"@AlcaldiaChia"},alternates:{canonical:"https://portal.chia-cundinamarca.gov.co"},category:"government"};function o({children:e}){return(0,a.jsx)("html",{lang:"es",className:"h-full",children:(0,a.jsx)("body",{className:`${s().className} h-full`,children:(0,a.jsx)("div",{id:"root",className:"min-h-full",children:e})})})}},4714:()=>{throw Error("Module build failed (from ../../node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m You're importing a component that needs \"next/headers\". That only works in a Server Component which is not supported in the pages/ directory. Read more: https://nextjs.org/docs/app/building-your-application/rendering/server-components\n  \x1b[31m│\x1b[0m\n\n   ╭─[\x1b[36;1;4mC:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\lib\\supabase.ts\x1b[0m:3:1]\n \x1b[2m1\x1b[0m │ import { createClient } from '@supabase/supabase-js';\n \x1b[2m2\x1b[0m │ import { createClientComponentClient, createServerComponentClient } from '@supabase/auth-helpers-nextjs';\n \x1b[2m3\x1b[0m │ import { cookies } from 'next/headers';\n   \xb7 \x1b[35;1m───────────────────────────────────────\x1b[0m\n \x1b[2m4\x1b[0m │ \n \x1b[2m5\x1b[0m │ // Environment variables validation\n \x1b[2m6\x1b[0m │ const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\n   ╰────\n")},5362:()=>{},6586:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var a=t(8828),n=t(2671),s=t.n(n),i=t(2257);function o(){return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100",children:(0,a.jsx)("svg",{className:"h-6 w-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})})}),(0,a.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Iniciar Sesi\xf3n"}),(0,a.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Accede a tu cuenta del portal ciudadano"})]}),(0,a.jsx)(i.LoginForm,{}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["\xbfNo tienes una cuenta?"," ",(0,a.jsx)(s(),{href:"/auth/register",className:"font-medium text-primary-600 hover:text-primary-500",children:"Reg\xedstrate aqu\xed"})]})})]})})}},7036:(e,r,t)=>{Promise.resolve().then(t.bind(t,3647)),Promise.resolve().then(t.t.bind(t,9989,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[363,118,114],()=>t(2614));module.exports=a})();