(()=>{var e={};e.id=315,e.ids=[315],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13484:(e,t,r)=>{Promise.resolve().then(r.bind(r,62257)),Promise.resolve().then(r.t.bind(r,42671,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22614:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>u});var o=r(24332),s=r(48819),i=r(67851),n=r.n(i),a=r(97540),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let u={children:["",{children:["(auth)",{children:["auth",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,36586)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\(auth)\\auth\\login\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,86580)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\(auth)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,92341,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,34356)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\(auth)\\auth\\login\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},p=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(auth)/auth/login/page",pathname:"/auth/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},26928:(e,t,r)=>{"use strict";var o,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,l={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(l,{createBrowserSupabaseClient:()=>E,createClientComponentClient:()=>c,createMiddlewareClient:()=>C,createMiddlewareSupabaseClient:()=>N,createPagesBrowserClient:()=>d,createPagesServerClient:()=>m,createRouteHandlerClient:()=>A,createServerActionClient:()=>P,createServerComponentClient:()=>S,createServerSupabaseClient:()=>w}),e.exports=((e,t,r,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let r of n(t))a.call(e,r)||void 0===r||s(e,r,{get:()=>t[r],enumerable:!(o=i(t,r))||o.enumerable});return e})(s({},"__esModule",{value:!0}),l);var u=r(33988);function c({supabaseUrl:e=process.env.NEXT_PUBLIC_SUPABASE_URL,supabaseKey:t=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,options:r,cookieOptions:s,isSingleton:i=!0}={}){if(!e||!t)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");let n=()=>{var o;return(0,u.createSupabaseClient)(e,t,{...r,global:{...null==r?void 0:r.global,headers:{...null==(o=null==r?void 0:r.global)?void 0:o.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.8.7"}},auth:{storage:new u.BrowserCookieAuthStorageAdapter(s)}})};if(i){let e=o??n();return"undefined"==typeof window?e:(o||(o=e),o)}return n()}var d=c,p=r(33988),h=r(64341),f=class extends p.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t,r,o;return(0,h.splitCookiesString)((null==(r=null==(t=this.context.res)?void 0:t.getHeader("set-cookie"))?void 0:r.toString())??"").map(t=>(0,p.parseCookies)(t)[e]).find(e=>!!e)??(null==(o=this.context.req)?void 0:o.cookies[e])}setCookie(e,t){this._setCookie(e,t)}deleteCookie(e){this._setCookie(e,"",{maxAge:0})}_setCookie(e,t,r){var o;let s=(0,h.splitCookiesString)((null==(o=this.context.res.getHeader("set-cookie"))?void 0:o.toString())??"").filter(t=>!(e in(0,p.parseCookies)(t))),i=(0,p.serializeCookie)(e,t,{...this.cookieOptions,...r,httpOnly:!1});this.context.res.setHeader("set-cookie",[...s,i])}};function m(e,{supabaseUrl:t=process.env.NEXT_PUBLIC_SUPABASE_URL,supabaseKey:r=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,options:o,cookieOptions:s}={}){var i;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,p.createSupabaseClient)(t,r,{...o,global:{...null==o?void 0:o.global,headers:{...null==(i=null==o?void 0:o.global)?void 0:i.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.8.7"}},auth:{storage:new f(e,s)}})}var v=r(33988),g=r(64341),x=class extends v.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;let r=(0,g.splitCookiesString)((null==(t=this.context.res.headers.get("set-cookie"))?void 0:t.toString())??"").map(t=>(0,v.parseCookies)(t)[e]).find(e=>!!e);return r||(0,v.parseCookies)(this.context.req.headers.get("cookie")??"")[e]}setCookie(e,t){this._setCookie(e,t)}deleteCookie(e){this._setCookie(e,"",{maxAge:0})}_setCookie(e,t,r){let o=(0,v.serializeCookie)(e,t,{...this.cookieOptions,...r,httpOnly:!1});this.context.res.headers&&this.context.res.headers.append("set-cookie",o)}};function C(e,{supabaseUrl:t=process.env.NEXT_PUBLIC_SUPABASE_URL,supabaseKey:r=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,options:o,cookieOptions:s}={}){var i;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,v.createSupabaseClient)(t,r,{...o,global:{...null==o?void 0:o.global,headers:{...null==(i=null==o?void 0:o.global)?void 0:i.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.8.7"}},auth:{storage:new x(e,s)}})}var b=r(33988),_=class extends b.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;return null==(t=this.context.cookies().get(e))?void 0:t.value}setCookie(e,t){}deleteCookie(e){}};function S(e,{supabaseUrl:t=process.env.NEXT_PUBLIC_SUPABASE_URL,supabaseKey:r=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,options:o,cookieOptions:s}={}){var i;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,b.createSupabaseClient)(t,r,{...o,global:{...null==o?void 0:o.global,headers:{...null==(i=null==o?void 0:o.global)?void 0:i.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.8.7"}},auth:{storage:new _(e,s)}})}var k=r(33988),y=class extends k.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;return null==(t=this.context.cookies().get(e))?void 0:t.value}setCookie(e,t){this.context.cookies().set(e,t,this.cookieOptions)}deleteCookie(e){this.context.cookies().set(e,"",{...this.cookieOptions,maxAge:0})}};function A(e,{supabaseUrl:t=process.env.NEXT_PUBLIC_SUPABASE_URL,supabaseKey:r=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,options:o,cookieOptions:s}={}){var i;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,k.createSupabaseClient)(t,r,{...o,global:{...null==o?void 0:o.global,headers:{...null==(i=null==o?void 0:o.global)?void 0:i.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.8.7"}},auth:{storage:new y(e,s)}})}var P=A;function E({supabaseUrl:e=process.env.NEXT_PUBLIC_SUPABASE_URL,supabaseKey:t=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,options:r,cookieOptions:o}={}){return console.warn("Please utilize the `createPagesBrowserClient` function instead of the deprecated `createBrowserSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages"),d({supabaseUrl:e,supabaseKey:t,options:r,cookieOptions:o})}function w(e,{supabaseUrl:t=process.env.NEXT_PUBLIC_SUPABASE_URL,supabaseKey:r=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,options:o,cookieOptions:s}={}){return console.warn("Please utilize the `createPagesServerClient` function instead of the deprecated `createServerSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages"),m(e,{supabaseUrl:t,supabaseKey:r,options:o,cookieOptions:s})}function N(e,{supabaseUrl:t=process.env.NEXT_PUBLIC_SUPABASE_URL,supabaseKey:r=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,options:o,cookieOptions:s}={}){return console.warn("Please utilize the `createMiddlewareClient` function instead of the deprecated `createMiddlewareSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs#middleware"),C(e,{supabaseUrl:t,supabaseKey:r,options:o,cookieOptions:s})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},33988:(e,t,r)=>{"use strict";r.r(t),r.d(t,{BrowserCookieAuthStorageAdapter:()=>y,CookieAuthStorageAdapter:()=>k,DEFAULT_COOKIE_OPTIONS:()=>_,createSupabaseClient:()=>A,isBrowser:()=>b,parseCookies:()=>P,parseSupabaseCookie:()=>x,serializeCookie:()=>E,stringifySupabaseSession:()=>C});var o=r(79428);new TextEncoder;let s=new TextDecoder;o.Buffer.isEncoding("base64url");let i=e=>o.Buffer.from(function(e){let t=e;return t instanceof Uint8Array&&(t=s.decode(t)),t}(e),"base64");var n=r(78126),a=Object.create,l=Object.defineProperty,u=Object.getOwnPropertyDescriptor,c=Object.getOwnPropertyNames,d=Object.getPrototypeOf,p=Object.prototype.hasOwnProperty,h=(e,t,r,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of c(t))p.call(e,s)||s===r||l(e,s,{get:()=>t[s],enumerable:!(o=u(t,s))||o.enumerable});return e},f=(e,t,r)=>(r=null!=e?a(d(e)):{},h(!t&&e&&e.__esModule?r:l(r,"default",{value:e,enumerable:!0}),e)),m=((e,t)=>function(){return t||(0,e[c(e)[0]])((t={exports:{}}).exports,t),t.exports})({"../../node_modules/.pnpm/cookie@0.5.0/node_modules/cookie/index.js"(e){e.parse=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");for(var r={},s=(t||{}).decode||o,i=0;i<e.length;){var n=e.indexOf("=",i);if(-1===n)break;var a=e.indexOf(";",i);if(-1===a)a=e.length;else if(a<n){i=e.lastIndexOf(";",n-1)+1;continue}var l=e.slice(i,n).trim();if(void 0===r[l]){var u=e.slice(n+1,a).trim();34===u.charCodeAt(0)&&(u=u.slice(1,-1)),r[l]=function(e,t){try{return t(e)}catch(t){return e}}(u,s)}i=a+1}return r},e.serialize=function(e,o,i){var n=i||{},a=n.encode||s;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!r.test(e))throw TypeError("argument name is invalid");var l=a(o);if(l&&!r.test(l))throw TypeError("argument val is invalid");var u=e+"="+l;if(null!=n.maxAge){var c=n.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");u+="; Max-Age="+Math.floor(c)}if(n.domain){if(!r.test(n.domain))throw TypeError("option domain is invalid");u+="; Domain="+n.domain}if(n.path){if(!r.test(n.path))throw TypeError("option path is invalid");u+="; Path="+n.path}if(n.expires){var d,p=n.expires;if(d=p,"[object Date]"!==t.call(d)&&!(d instanceof Date)||isNaN(p.valueOf()))throw TypeError("option expires is invalid");u+="; Expires="+p.toUTCString()}if(n.httpOnly&&(u+="; HttpOnly"),n.secure&&(u+="; Secure"),n.priority)switch("string"==typeof n.priority?n.priority.toLowerCase():n.priority){case"low":u+="; Priority=Low";break;case"medium":u+="; Priority=Medium";break;case"high":u+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(n.sameSite)switch("string"==typeof n.sameSite?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":u+="; SameSite=Strict";break;case"lax":u+="; SameSite=Lax";break;case"none":u+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return u};var t=Object.prototype.toString,r=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;function o(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}function s(e){return encodeURIComponent(e)}}}),v=f(m()),g=f(m());function x(e){if(!e)return null;try{let t=JSON.parse(e);if(!t)return null;if("Object"===t.constructor.name)return t;if("Array"!==t.constructor.name)throw Error(`Unexpected format: ${t.constructor.name}`);let[r,o,s]=t[0].split("."),n=i(o),a=new TextDecoder,{exp:l,sub:u,...c}=JSON.parse(a.decode(n));return{expires_at:l,expires_in:l-Math.round(Date.now()/1e3),token_type:"bearer",access_token:t[0],refresh_token:t[1],provider_token:t[2],provider_refresh_token:t[3],user:{id:u,factors:t[4],...c}}}catch(e){return console.warn("Failed to parse cookie string:",e),null}}function C(e){var t;return JSON.stringify([e.access_token,e.refresh_token,e.provider_token,e.provider_refresh_token,(null==(t=e.user)?void 0:t.factors)??null])}function b(){return"undefined"!=typeof window&&void 0!==window.document}var _={path:"/",sameSite:"lax",maxAge:31536e6},S=RegExp(".{1,3180}","g"),k=class{constructor(e){this.cookieOptions={..._,...e,maxAge:_.maxAge}}getItem(e){let t=this.getCookie(e);if(e.endsWith("-code-verifier")&&t)return t;if(t)return JSON.stringify(x(t));let r=function(e,t=()=>null){let r=[];for(let o=0;;o++){let s=t(`${e}.${o}`);if(!s)break;r.push(s)}return r.length?r.join(""):null}(e,e=>this.getCookie(e));return null!==r?JSON.stringify(x(r)):null}setItem(e,t){if(e.endsWith("-code-verifier"))return void this.setCookie(e,t);(function(e,t,r){if(1===Math.ceil(t.length/((void 0)??3180)))return[{name:e,value:t}];let o=[],s=t.match(S);return null==s||s.forEach((t,r)=>{let s=`${e}.${r}`;o.push({name:s,value:t})}),o})(e,C(JSON.parse(t))).forEach(e=>{this.setCookie(e.name,e.value)})}removeItem(e){this._deleteSingleCookie(e),this._deleteChunkedCookies(e)}_deleteSingleCookie(e){this.getCookie(e)&&this.deleteCookie(e)}_deleteChunkedCookies(e,t=0){for(let r=t;;r++){let t=`${e}.${r}`;if(void 0===this.getCookie(t))break;this.deleteCookie(t)}}},y=class extends k{constructor(e){super(e)}getCookie(e){return b()?(0,v.parse)(document.cookie)[e]:null}setCookie(e,t){if(!b())return null;document.cookie=(0,v.serialize)(e,t,{...this.cookieOptions,httpOnly:!1})}deleteCookie(e){if(!b())return null;document.cookie=(0,v.serialize)(e,"",{...this.cookieOptions,maxAge:0,httpOnly:!1})}};function A(e,t,r){var o;let s=b();return(0,n.UU)(e,t,{...r,auth:{flowType:"pkce",autoRefreshToken:s,detectSessionInUrl:s,persistSession:!0,storage:r.auth.storage,...(null==(o=r.auth)?void 0:o.storageKey)?{storageKey:r.auth.storageKey}:{}}})}var P=g.parse,E=g.serialize},34631:e=>{"use strict";e.exports=require("tls")},36586:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var o=r(38828),s=r(42671),i=r.n(s),n=r(62257);function a(){return(0,o.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,o.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100",children:(0,o.jsx)("svg",{className:"h-6 w-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})})}),(0,o.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Iniciar Sesi\xf3n"}),(0,o.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Accede a tu cuenta del portal ciudadano"})]}),(0,o.jsx)(n.LoginForm,{}),(0,o.jsx)("div",{className:"text-center",children:(0,o.jsxs)("p",{className:"text-sm text-gray-600",children:["\xbfNo tienes una cuenta?"," ",(0,o.jsx)(i(),{href:"/auth/register",className:"font-medium text-primary-600 hover:text-primary-500",children:"Reg\xedstrate aqu\xed"})]})})]})})}},42671:(e,t,r)=>{let{createProxy:o}=r(47927);e.exports=o("C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\node_modules\\next\\dist\\client\\app-dir\\link.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56682:(e,t,r)=>{"use strict";r.d(t,{LoginForm:()=>d});var o=r(13486),s=r(60159),i=r(2984),n=r(78126),a=r(26928);let l=process.env.NEXT_PUBLIC_SUPABASE_URL,u=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;if(!l||!u)throw Error("Missing Supabase environment variables");let c=()=>(0,a.createClientComponentClient)();function d(){let[e,t]=(0,s.useState)(""),[r,n]=(0,s.useState)(""),[a,l]=(0,s.useState)(!1),[u,d]=(0,s.useState)(null),p=(0,i.useRouter)(),h=c(),f=async t=>{t.preventDefault(),l(!0),d(null);try{let{data:t,error:o}=await h.auth.signInWithPassword({email:e,password:r});if(o)return void d(o.message);t.user&&(p.push("/dashboard"),p.refresh())}catch(e){d("Error inesperado. Por favor intenta de nuevo."),console.error("Login error:",e)}finally{l(!1)}},m=async()=>{l(!0),d(null);try{let{error:e}=await h.auth.signInWithOAuth({provider:"google",options:{redirectTo:`${window.location.origin}/auth/callback`}});e&&d(e.message)}catch(e){d("Error inesperado. Por favor intenta de nuevo."),console.error("Google login error:",e)}finally{l(!1)}};return(0,o.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:f,children:[u&&(0,o.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,o.jsx)("div",{className:"text-sm text-red-700",children:u})}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"email",className:"form-label",children:"Correo Electr\xf3nico"}),(0,o.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"form-input",value:e,onChange:e=>t(e.target.value),disabled:a})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"password",className:"form-label",children:"Contrase\xf1a"}),(0,o.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,className:"form-input",value:r,onChange:e=>n(e.target.value),disabled:a})]})]}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("button",{type:"submit",disabled:a,className:"btn-primary w-full",children:a?"Iniciando sesi\xf3n...":"Iniciar Sesi\xf3n"}),(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,o.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,o.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,o.jsx)("span",{className:"px-2 bg-gray-50 text-gray-500",children:"O contin\xfaa con"})})]}),(0,o.jsxs)("button",{type:"button",onClick:m,disabled:a,className:"btn-secondary w-full flex items-center justify-center",children:[(0,o.jsxs)("svg",{className:"w-5 h-5 mr-2",viewBox:"0 0 24 24",children:[(0,o.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,o.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,o.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,o.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Google"]})]})]})}(0,n.UU)(l,u)},62257:(e,t,r)=>{"use strict";r.d(t,{LoginForm:()=>o});let o=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call LoginForm() from the server but LoginForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\components\\auth\\LoginForm.tsx","LoginForm")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64341:e=>{"use strict";var t={decodeValues:!0,map:!1,silent:!1};function r(e){return"string"==typeof e&&!!e.trim()}function o(e,o){var s,i,n,a,l=e.split(";").filter(r),u=(s=l.shift(),i="",n="",(a=s.split("=")).length>1?(i=a.shift(),n=a.join("=")):n=s,{name:i,value:n}),c=u.name,d=u.value;o=o?Object.assign({},t,o):t;try{d=o.decodeValues?decodeURIComponent(d):d}catch(e){console.error("set-cookie-parser encountered an error while decoding a cookie with value '"+d+"'. Set options.decodeValues to false to disable this feature.",e)}var p={name:c,value:d};return l.forEach(function(e){var t=e.split("="),r=t.shift().trimLeft().toLowerCase(),o=t.join("=");"expires"===r?p.expires=new Date(o):"max-age"===r?p.maxAge=parseInt(o,10):"secure"===r?p.secure=!0:"httponly"===r?p.httpOnly=!0:"samesite"===r?p.sameSite=o:"partitioned"===r?p.partitioned=!0:p[r]=o}),p}function s(e,s){if(s=s?Object.assign({},t,s):t,!e)if(!s.map)return[];else return{};if(e.headers)if("function"==typeof e.headers.getSetCookie)e=e.headers.getSetCookie();else if(e.headers["set-cookie"])e=e.headers["set-cookie"];else{var i=e.headers[Object.keys(e.headers).find(function(e){return"set-cookie"===e.toLowerCase()})];i||!e.headers.cookie||s.silent||console.warn("Warning: set-cookie-parser appears to have been called on a request object. It is designed to parse Set-Cookie headers from responses, not Cookie headers from requests. Set the option {silent: true} to suppress this warning."),e=i}return(Array.isArray(e)||(e=[e]),s.map)?e.filter(r).reduce(function(e,t){var r=o(t,s);return e[r.name]=r,e},{}):e.filter(r).map(function(e){return o(e,s)})}e.exports=s,e.exports.parse=s,e.exports.parseString=o,e.exports.splitCookiesString=function(e){if(Array.isArray(e))return e;if("string"!=typeof e)return[];var t,r,o,s,i,n=[],a=0;function l(){for(;a<e.length&&/\s/.test(e.charAt(a));)a+=1;return a<e.length}for(;a<e.length;){for(t=a,i=!1;l();)if(","===(r=e.charAt(a))){for(o=a,a+=1,l(),s=a;a<e.length&&"="!==(r=e.charAt(a))&&";"!==r&&","!==r;)a+=1;a<e.length&&"="===e.charAt(a)?(i=!0,a=s,n.push(e.substring(t,o)),t=a):a=o+1}else a+=1;(!i||a>=e.length)&&n.push(e.substring(t,e.length))}return n}},74075:e=>{"use strict";e.exports=require("zlib")},77036:(e,t,r)=>{Promise.resolve().then(r.bind(r,56682)),Promise.resolve().then(r.t.bind(r,49989,23))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[191,118,114,85,900],()=>r(22614));module.exports=o})();