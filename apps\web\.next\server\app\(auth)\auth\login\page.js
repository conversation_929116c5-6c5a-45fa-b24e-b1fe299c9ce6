/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(auth)/auth/login/page";
exports.ids = ["app/(auth)/auth/login/page"];
exports.modules = {

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(auth)%2Fauth%2Flogin%2Fpage&page=%2F(auth)%2Fauth%2Flogin%2Fpage&appPaths=%2F(auth)%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(auth)%2Fauth%2Flogin%2Fpage&page=%2F(auth)%2Fauth%2Flogin%2Fpage&appPaths=%2F(auth)%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/module.compiled.js?87f3\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/../../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/../../node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/../../node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(auth)/layout.tsx */ \"(rsc)/./app/(auth)/layout.tsx\"));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/../../node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module7 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/../../node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page8 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(auth)/auth/login/page.tsx */ \"(rsc)/./app/(auth)/auth/login/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(auth)',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page8, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\(auth)\\\\auth\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\(auth)\\\\layout.tsx\"],\n'not-found': [module5, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module6, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module7, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\(auth)\\\\auth\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(auth)/auth/login/page\",\n        pathname: \"/auth/login\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(auth)%2Fauth%2Flogin%2Fpage&page=%2F(auth)%2Fauth%2Flogin%2Fpage&appPaths=%2F(auth)%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Capps%5C%5Cweb%5C%5Ccomponents%5C%5Cauth%5C%5CLoginForm.tsx%22%2C%22ids%22%3A%5B%22LoginForm%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Capps%5C%5Cweb%5C%5Ccomponents%5C%5Cauth%5C%5CLoginForm.tsx%22%2C%22ids%22%3A%5B%22LoginForm%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/auth/LoginForm.tsx */ \"(rsc)/./components/auth/LoginForm.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/../../node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNKdWFuJTIwUHVsZ2FyaW4lNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDY2hpYS1uZXh0JTVDJTVDYXBwcyU1QyU1Q3dlYiU1QyU1Q2NvbXBvbmVudHMlNUMlNUNhdXRoJTVDJTVDTG9naW5Gb3JtLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkxvZ2luRm9ybSUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNKdWFuJTIwUHVsZ2FyaW4lNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDY2hpYS1uZXh0JTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNhcHAtZGlyJTVDJTVDbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMl9fZXNNb2R1bGUlMjIlMkMlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwS0FBZ0w7QUFDaEw7QUFDQSx3TkFBbU0iLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkxvZ2luRm9ybVwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXEp1YW4gUHVsZ2FyaW5cXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcY2hpYS1uZXh0XFxcXGFwcHNcXFxcd2ViXFxcXGNvbXBvbmVudHNcXFxcYXV0aFxcXFxMb2dpbkZvcm0udHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJfX2VzTW9kdWxlXCIsXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcSnVhbiBQdWxnYXJpblxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxjaGlhLW5leHRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcYXBwLWRpclxcXFxsaW5rLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Capps%5C%5Cweb%5C%5Ccomponents%5C%5Cauth%5C%5CLoginForm.tsx%22%2C%22ids%22%3A%5B%22LoginForm%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/client-page.js */ \"(rsc)/../../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/../../node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/../../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/../../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/../../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/../../node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/../../node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/../../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Capps%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Capps%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cpackages%5C%5Cauth%5C%5Cdist%5C%5Ccontext%5C%5Cauth-context.js%22%2C%22ids%22%3A%5B%22*%22%2C%22AuthContext%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cpackages%5C%5Cauth%5C%5Cdist%5C%5Ccontext%5C%5Cauth-context.js%22%2C%22ids%22%3A%5B%22*%22%2C%22AuthContext%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../packages/auth/dist/context/auth-context.js */ \"(rsc)/../../packages/auth/dist/context/auth-context.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNKdWFuJTIwUHVsZ2FyaW4lNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDY2hpYS1uZXh0JTVDJTVDcGFja2FnZXMlNUMlNUNhdXRoJTVDJTVDZGlzdCU1QyU1Q2NvbnRleHQlNUMlNUNhdXRoLWNvbnRleHQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjIqJTIyJTJDJTIyQXV0aENvbnRleHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRNQUFxSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcSnVhbiBQdWxnYXJpblxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxjaGlhLW5leHRcXFxccGFja2FnZXNcXFxcYXV0aFxcXFxkaXN0XFxcXGNvbnRleHRcXFxcYXV0aC1jb250ZXh0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cpackages%5C%5Cauth%5C%5Cdist%5C%5Ccontext%5C%5Cauth-context.js%22%2C%22ids%22%3A%5B%22*%22%2C%22AuthContext%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../../packages/auth/dist/context/auth-context.js":
/*!********************************************************!*\
  !*** ../../packages/auth/dist/context/auth-context.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthContext: () => (/* binding */ AuthContext),
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthContext = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthContext() from the server but AuthContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\packages\\auth\\dist\\context\\auth-context.js",
"AuthContext",
);const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\packages\\auth\\dist\\context\\auth-context.js",
"AuthProvider",
);

/***/ }),

/***/ "(rsc)/../../packages/auth/dist/hooks/use-auth.js":
/*!**************************************************!*\
  !*** ../../packages/auth/dist/hooks/use-auth.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useAuthError: () => (/* binding */ useAuthError),\n/* harmony export */   useAuthLoading: () => (/* binding */ useAuthLoading),\n/* harmony export */   useHasRole: () => (/* binding */ useHasRole),\n/* harmony export */   useIsAuthenticated: () => (/* binding */ useIsAuthenticated),\n/* harmony export */   useUser: () => (/* binding */ useUser)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/../../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _context_auth_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../context/auth-context */ \"(rsc)/../../packages/auth/dist/context/auth-context.js\");\n\n\n/**\n * Custom hook to access authentication context\n *\n * @returns AuthContextType - Authentication context with user state and methods\n * @throws Error if used outside of AuthProvider\n *\n * @example\n * ```tsx\n * function LoginComponent() {\n *   const { login, user, status } = useAuth();\n *\n *   const handleLogin = async (credentials) => {\n *     try {\n *       await login(credentials);\n *     } catch (error) {\n *       console.error('Login failed:', error);\n *     }\n *   };\n *\n *   if (status === 'loading') {\n *     return <div>Loading...</div>;\n *   }\n *\n *   return (\n *     <form onSubmit={handleLogin}>\n *       // Login form\n *     </form>\n *   );\n * }\n * ```\n */ function useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_auth_context__WEBPACK_IMPORTED_MODULE_1__.AuthContext);\n    if (!context) {\n        throw new Error('useAuth must be used within an AuthProvider. ' + 'Make sure to wrap your component tree with <AuthProvider>.');\n    }\n    return context;\n}\n/**\n * Hook to check if user is authenticated\n *\n * @returns boolean - True if user is authenticated\n *\n * @example\n * ```tsx\n * function ProtectedComponent() {\n *   const isAuthenticated = useIsAuthenticated();\n *\n *   if (!isAuthenticated) {\n *     return <LoginPrompt />;\n *   }\n *\n *   return <ProtectedContent />;\n * }\n * ```\n */ function useIsAuthenticated() {\n    const { status } = useAuth();\n    return status === 'authenticated';\n}\n/**\n * Hook to get current user profile\n *\n * @returns UserProfile | null - Current user profile or null if not authenticated\n *\n * @example\n * ```tsx\n * function UserProfile() {\n *   const user = useUser();\n *\n *   if (!user) {\n *     return <div>Not logged in</div>;\n *   }\n *\n *   return (\n *     <div>\n *       <h1>Welcome, {user.firstName}!</h1>\n *       <p>Email: {user.email}</p>\n *     </div>\n *   );\n * }\n * ```\n */ function useUser() {\n    const { user } = useAuth();\n    return user;\n}\n/**\n * Hook to check if user has specific role\n *\n * @param role - Role to check for\n * @returns boolean - True if user has the specified role\n *\n * @example\n * ```tsx\n * function AdminPanel() {\n *   const isAdmin = useHasRole('admin_sistema');\n *\n *   if (!isAdmin) {\n *     return <div>Access denied</div>;\n *   }\n *\n *   return <AdminContent />;\n * }\n * ```\n */ function useHasRole(role) {\n    const { user } = useAuth();\n    return user?.role === role;\n}\n/**\n * Hook to check authentication loading state\n *\n * @returns boolean - True if authentication is loading\n *\n * @example\n * ```tsx\n * function App() {\n *   const isLoading = useAuthLoading();\n *\n *   if (isLoading) {\n *     return <LoadingSpinner />;\n *   }\n *\n *   return <MainApp />;\n * }\n * ```\n */ function useAuthLoading() {\n    const { status } = useAuth();\n    return status === 'loading';\n}\n/**\n * Hook to get authentication error\n *\n * @returns AuthError | null - Current authentication error or null\n *\n * @example\n * ```tsx\n * function LoginForm() {\n *   const error = useAuthError();\n *\n *   return (\n *     <form>\n *       {error && (\n *         <div className=\"error\">\n *           {error.message}\n *         </div>\n *       )}\n *       // Form fields\n *     </form>\n *   );\n * }\n * ```\n */ function useAuthError() {\n    const { error } = useAuth();\n    return error;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../packages/auth/dist/hooks/use-auth.js\n");

/***/ }),

/***/ "(rsc)/../../packages/auth/dist/index.js":
/*!*****************************************!*\
  !*** ../../packages/auth/dist/index.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthContext: () => (/* reexport safe */ _context_auth_context__WEBPACK_IMPORTED_MODULE_2__.AuthContext),\n/* harmony export */   AuthProvider: () => (/* reexport safe */ _context_auth_context__WEBPACK_IMPORTED_MODULE_2__.AuthProvider),\n/* harmony export */   AuthService: () => (/* reexport safe */ _services_auth_service__WEBPACK_IMPORTED_MODULE_1__.AuthService),\n/* harmony export */   AuthStatus: () => (/* reexport safe */ _types_auth__WEBPACK_IMPORTED_MODULE_0__.AuthStatus),\n/* harmony export */   DocumentNumberSchema: () => (/* reexport safe */ _types_auth__WEBPACK_IMPORTED_MODULE_0__.DocumentNumberSchema),\n/* harmony export */   EmailSchema: () => (/* reexport safe */ _types_auth__WEBPACK_IMPORTED_MODULE_0__.EmailSchema),\n/* harmony export */   LoginSchema: () => (/* reexport safe */ _types_auth__WEBPACK_IMPORTED_MODULE_0__.LoginSchema),\n/* harmony export */   PasswordResetSchema: () => (/* reexport safe */ _types_auth__WEBPACK_IMPORTED_MODULE_0__.PasswordResetSchema),\n/* harmony export */   PasswordSchema: () => (/* reexport safe */ _types_auth__WEBPACK_IMPORTED_MODULE_0__.PasswordSchema),\n/* harmony export */   PhoneSchema: () => (/* reexport safe */ _types_auth__WEBPACK_IMPORTED_MODULE_0__.PhoneSchema),\n/* harmony export */   RegistrationSchema: () => (/* reexport safe */ _types_auth__WEBPACK_IMPORTED_MODULE_0__.RegistrationSchema),\n/* harmony export */   TwoFactorStatus: () => (/* reexport safe */ _types_auth__WEBPACK_IMPORTED_MODULE_0__.TwoFactorStatus),\n/* harmony export */   TwoFactorVerificationSchema: () => (/* reexport safe */ _types_auth__WEBPACK_IMPORTED_MODULE_0__.TwoFactorVerificationSchema),\n/* harmony export */   UpdatePasswordSchema: () => (/* reexport safe */ _types_auth__WEBPACK_IMPORTED_MODULE_0__.UpdatePasswordSchema),\n/* harmony export */   UserRole: () => (/* reexport safe */ _types_auth__WEBPACK_IMPORTED_MODULE_0__.UserRole),\n/* harmony export */   createSupabaseAdminClient: () => (/* reexport safe */ _lib_supabase_client__WEBPACK_IMPORTED_MODULE_4__.createSupabaseAdminClient),\n/* harmony export */   createSupabaseClient: () => (/* reexport safe */ _lib_supabase_client__WEBPACK_IMPORTED_MODULE_4__.createSupabaseClient),\n/* harmony export */   getContextualSupabaseClient: () => (/* reexport safe */ _lib_supabase_client__WEBPACK_IMPORTED_MODULE_4__.getContextualSupabaseClient),\n/* harmony export */   getSupabaseAdminClient: () => (/* reexport safe */ _lib_supabase_client__WEBPACK_IMPORTED_MODULE_4__.getSupabaseAdminClient),\n/* harmony export */   getSupabaseClient: () => (/* reexport safe */ _lib_supabase_client__WEBPACK_IMPORTED_MODULE_4__.getSupabaseClient),\n/* harmony export */   isServer: () => (/* reexport safe */ _lib_supabase_client__WEBPACK_IMPORTED_MODULE_4__.isServer),\n/* harmony export */   resetSupabaseClients: () => (/* reexport safe */ _lib_supabase_client__WEBPACK_IMPORTED_MODULE_4__.resetSupabaseClients),\n/* harmony export */   useAuth: () => (/* reexport safe */ _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth),\n/* harmony export */   useAuthError: () => (/* reexport safe */ _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthError),\n/* harmony export */   useAuthLoading: () => (/* reexport safe */ _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthLoading),\n/* harmony export */   useHasRole: () => (/* reexport safe */ _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useHasRole),\n/* harmony export */   useIsAuthenticated: () => (/* reexport safe */ _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useIsAuthenticated),\n/* harmony export */   useUser: () => (/* reexport safe */ _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useUser)\n/* harmony export */ });\n/* harmony import */ var _types_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types/auth */ \"(rsc)/../../packages/auth/dist/types/auth.js\");\n/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./services/auth-service */ \"(rsc)/../../packages/auth/dist/services/auth-service.js\");\n/* harmony import */ var _context_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context/auth-context */ \"(rsc)/../../packages/auth/dist/context/auth-context.js\");\n/* harmony import */ var _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./hooks/use-auth */ \"(rsc)/../../packages/auth/dist/hooks/use-auth.js\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/supabase-client */ \"(rsc)/../../packages/auth/dist/lib/supabase-client.js\");\n/**\n * @chia/auth - Authentication package for CHIA Portal Ciudadano Digital\n *\n * This package provides comprehensive authentication functionality including:\n * - User registration and login\n * - Password reset and recovery\n * - Two-factor authentication\n * - Session management\n * - Role-based access control\n * - Audit logging\n */ \n// Services\n\n// Context and Hooks\n\n\n// Supabase Client\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../packages/auth/dist/index.js\n");

/***/ }),

/***/ "(rsc)/../../packages/auth/dist/lib/supabase-client.js":
/*!*******************************************************!*\
  !*** ../../packages/auth/dist/lib/supabase-client.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseAdminClient: () => (/* binding */ createSupabaseAdminClient),\n/* harmony export */   createSupabaseClient: () => (/* binding */ createSupabaseClient),\n/* harmony export */   getContextualSupabaseClient: () => (/* binding */ getContextualSupabaseClient),\n/* harmony export */   getSupabaseAdminClient: () => (/* binding */ getSupabaseAdminClient),\n/* harmony export */   getSupabaseClient: () => (/* binding */ getSupabaseClient),\n/* harmony export */   isServer: () => (/* binding */ isServer),\n/* harmony export */   resetSupabaseClients: () => (/* binding */ resetSupabaseClients)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/../../node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n/**\n * Environment variables validation\n */ function validateEnvironment() {\n    const url = \"https://hndowofzjzjoljnapokv.supabase.co\";\n    const anonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY\";\n    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!url) {\n        throw new Error('NEXT_PUBLIC_SUPABASE_URL environment variable is required');\n    }\n    if (!anonKey) {\n        throw new Error('NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable is required');\n    }\n    return {\n        url,\n        anonKey,\n        serviceRoleKey\n    };\n}\n/**\n * Create Supabase client for browser/client-side operations\n */ function createSupabaseClient() {\n    const config = validateEnvironment();\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(config.url, config.anonKey, {\n        auth: {\n            autoRefreshToken: true,\n            persistSession: true,\n            detectSessionInUrl: true,\n            flowType: 'pkce'\n        },\n        global: {\n            headers: {\n                'X-Client-Info': 'chia-portal@1.0.0'\n            }\n        }\n    });\n}\n/**\n * Create Supabase admin client for server-side operations\n * Requires service role key for admin operations\n */ function createSupabaseAdminClient() {\n    const config = validateEnvironment();\n    if (!config.serviceRoleKey) {\n        throw new Error('SUPABASE_SERVICE_ROLE_KEY environment variable is required for admin operations');\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(config.url, config.serviceRoleKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        },\n        global: {\n            headers: {\n                'X-Client-Info': 'chia-portal-admin@1.0.0'\n            }\n        }\n    });\n}\n/**\n * Singleton instance for client-side operations\n */ let supabaseClient = null;\n/**\n * Get or create Supabase client instance\n */ function getSupabaseClient() {\n    if (!supabaseClient) {\n        supabaseClient = createSupabaseClient();\n    }\n    return supabaseClient;\n}\n/**\n * Singleton instance for admin operations\n */ let supabaseAdminClient = null;\n/**\n * Get or create Supabase admin client instance\n */ function getSupabaseAdminClient() {\n    if (!supabaseAdminClient) {\n        supabaseAdminClient = createSupabaseAdminClient();\n    }\n    return supabaseAdminClient;\n}\n/**\n * Reset client instances (useful for testing)\n */ function resetSupabaseClients() {\n    supabaseClient = null;\n    supabaseAdminClient = null;\n}\n/**\n * Check if we're running on the server side\n */ function isServer() {\n    return \"undefined\" === 'undefined';\n}\n/**\n * Get appropriate client based on environment\n */ function getContextualSupabaseClient() {\n    return isServer() ? getSupabaseAdminClient() : getSupabaseClient();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../packages/auth/dist/lib/supabase-client.js\n");

/***/ }),

/***/ "(rsc)/../../packages/auth/dist/services/auth-service.js":
/*!*********************************************************!*\
  !*** ../../packages/auth/dist/services/auth-service.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: () => (/* binding */ AuthService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/supabase-client */ \"(rsc)/../../packages/auth/dist/lib/supabase-client.js\");\n/* harmony import */ var _types_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types/auth */ \"(rsc)/../../packages/auth/dist/types/auth.js\");\n\n\n/**\n * Authentication service class\n * Handles all authentication operations with Supabase\n */ class AuthService {\n    constructor(){\n        this.supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.getSupabaseClient)();\n        this.adminSupabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.getSupabaseAdminClient)();\n    }\n    /**\n     * Register a new citizen user\n     */ async register(data) {\n        try {\n            // Step 1: Create auth user with Supabase Auth\n            const { data: authData, error: authError } = await this.supabase.auth.signUp({\n                email: data.email,\n                password: data.password,\n                options: {\n                    data: {\n                        first_name: data.firstName,\n                        last_name: data.lastName,\n                        document_type: data.documentType,\n                        document_number: data.documentNumber,\n                        phone: data.phone,\n                        city: data.city,\n                        department: data.department,\n                        birth_date: data.birthDate,\n                        role: _types_auth__WEBPACK_IMPORTED_MODULE_1__.UserRole.CIUDADANO\n                    }\n                }\n            });\n            if (authError) {\n                throw this.mapSupabaseError(authError);\n            }\n            if (!authData.user) {\n                throw new Error('Failed to create user account');\n            }\n            // Step 2: Create citizen profile in database\n            const { data: profileData, error: profileError } = await this.supabase.from('ciudadano').insert({\n                id: authData.user.id,\n                email: data.email,\n                first_name: data.firstName,\n                last_name: data.lastName,\n                document_type: data.documentType,\n                document_number: data.documentNumber,\n                phone: data.phone,\n                city: data.city,\n                department: data.department,\n                birth_date: data.birthDate,\n                is_email_verified: false,\n                is_phone_verified: false,\n                two_factor_enabled: false\n            }).select().single();\n            if (profileError) {\n                // Cleanup auth user if profile creation fails\n                await this.adminSupabase.auth.admin.deleteUser(authData.user.id);\n                throw this.mapDatabaseError(profileError);\n            }\n            // Step 3: Log registration event\n            await this.logAuditEvent(authData.user.id, 'user_registered', 'user', authData.user.id);\n            return this.mapDatabaseUserToProfile(profileData);\n        } catch (error) {\n            throw this.handleError(error, 'Registration failed');\n        }\n    }\n    /**\n     * Login user with email and password\n     */ async login(credentials) {\n        try {\n            // Step 1: Authenticate with Supabase Auth\n            const { data: authData, error: authError } = await this.supabase.auth.signInWithPassword({\n                email: credentials.email,\n                password: credentials.password\n            });\n            if (authError) {\n                throw this.mapSupabaseError(authError);\n            }\n            if (!authData.user || !authData.session) {\n                throw new Error('Authentication failed');\n            }\n            // Step 2: Get user profile\n            const profile = await this.getUserProfile(authData.user.id);\n            // Step 3: Check if 2FA is required\n            if (profile.twoFactorEnabled && !credentials.twoFactorCode) {\n                throw new Error('Two-factor authentication code required');\n            }\n            // Step 4: Verify 2FA code if provided\n            if (profile.twoFactorEnabled && credentials.twoFactorCode) {\n                await this.verifyTwoFactorCode(authData.user.id, credentials.twoFactorCode);\n            }\n            // Step 5: Update last login timestamp\n            await this.supabase.from('ciudadano').update({\n                last_login_at: new Date().toISOString()\n            }).eq('id', authData.user.id);\n            // Step 6: Create session record\n            const sessionData = await this.createSessionRecord(authData.user.id, authData.session);\n            // Step 7: Log login event\n            await this.logAuditEvent(authData.user.id, 'user_login', 'user', authData.user.id);\n            return {\n                user: profile,\n                accessToken: authData.session.access_token,\n                refreshToken: authData.session.refresh_token,\n                expiresAt: authData.session.expires_at || 0,\n                sessionId: sessionData.id\n            };\n        } catch (error) {\n            throw this.handleError(error, 'Login failed');\n        }\n    }\n    /**\n     * Logout user\n     */ async logout() {\n        try {\n            const { data: { user } } = await this.supabase.auth.getUser();\n            if (user) {\n                // Deactivate session records\n                await this.supabase.from('user_sessions').update({\n                    is_active: false\n                }).eq('user_id', user.id);\n                // Log logout event\n                await this.logAuditEvent(user.id, 'user_logout', 'user', user.id);\n            }\n            // Sign out from Supabase Auth\n            const { error } = await this.supabase.auth.signOut();\n            if (error) {\n                throw this.mapSupabaseError(error);\n            }\n        } catch (error) {\n            throw this.handleError(error, 'Logout failed');\n        }\n    }\n    /**\n     * Get current user profile\n     */ async getCurrentUser() {\n        try {\n            const { data: { user }, error } = await this.supabase.auth.getUser();\n            if (error) {\n                throw this.mapSupabaseError(error);\n            }\n            if (!user) {\n                return null;\n            }\n            return await this.getUserProfile(user.id);\n        } catch (error) {\n            console.error('Failed to get current user:', error);\n            return null;\n        }\n    }\n    /**\n     * Refresh authentication session\n     */ async refreshSession() {\n        try {\n            const { data, error } = await this.supabase.auth.refreshSession();\n            if (error) {\n                throw this.mapSupabaseError(error);\n            }\n            if (!data.session || !data.user) {\n                return null;\n            }\n            const profile = await this.getUserProfile(data.user.id);\n            return {\n                user: profile,\n                accessToken: data.session.access_token,\n                refreshToken: data.session.refresh_token,\n                expiresAt: data.session.expires_at || 0,\n                sessionId: '' // Will be updated by session management\n            };\n        } catch (error) {\n            console.error('Failed to refresh session:', error);\n            return null;\n        }\n    }\n    /**\n     * Request password reset\n     */ async requestPasswordReset(email) {\n        try {\n            const { error } = await this.supabase.auth.resetPasswordForEmail(email, {\n                redirectTo: `${window.location.origin}/auth/reset-password`\n            });\n            if (error) {\n                throw this.mapSupabaseError(error);\n            }\n            // Log password reset request\n            const { data: userData } = await this.supabase.from('ciudadano').select('id').eq('email', email).single();\n            if (userData) {\n                await this.logAuditEvent(userData.id, 'password_reset_requested', 'user', userData.id);\n            }\n        } catch (error) {\n            throw this.handleError(error, 'Password reset request failed');\n        }\n    }\n    /**\n     * Update password with reset token\n     */ async updatePassword(data) {\n        try {\n            const { error } = await this.supabase.auth.updateUser({\n                password: data.newPassword\n            });\n            if (error) {\n                throw this.mapSupabaseError(error);\n            }\n            // Log password update\n            const { data: { user } } = await this.supabase.auth.getUser();\n            if (user) {\n                await this.logAuditEvent(user.id, 'password_updated', 'user', user.id);\n            }\n        } catch (error) {\n            throw this.handleError(error, 'Password update failed');\n        }\n    }\n    /**\n     * Get user profile by ID\n     */ async getUserProfile(userId) {\n        const { data, error } = await this.supabase.from('ciudadano').select('*').eq('id', userId).single();\n        if (error) {\n            throw this.mapDatabaseError(error);\n        }\n        return this.mapDatabaseUserToProfile(data);\n    }\n    /**\n     * Create session record in database\n     */ async createSessionRecord(userId, session) {\n        const { data, error } = await this.supabase.from('user_sessions').insert({\n            user_id: userId,\n            session_token: session.access_token,\n            refresh_token: session.refresh_token,\n            expires_at: new Date(session.expires_at * 1000).toISOString(),\n            is_active: true\n        }).select().single();\n        if (error) {\n            throw this.mapDatabaseError(error);\n        }\n        return data;\n    }\n    /**\n     * Verify two-factor authentication code\n     */ async verifyTwoFactorCode(userId, code) {\n        // Implementation will be added in the 2FA service\n        throw new Error('Two-factor authentication not yet implemented');\n    }\n    /**\n     * Log audit event\n     */ async logAuditEvent(userId, action, resourceType, resourceId, details) {\n        try {\n            await this.supabase.from('audit_log').insert({\n                user_id: userId,\n                action,\n                resource_type: resourceType,\n                resource_id: resourceId,\n                details,\n                created_at: new Date().toISOString()\n            });\n        } catch (error) {\n            console.error('Failed to log audit event:', error);\n        }\n    }\n    /**\n     * Map database user to UserProfile interface\n     */ mapDatabaseUserToProfile(dbUser) {\n        return {\n            id: dbUser.id,\n            email: dbUser.email,\n            role: _types_auth__WEBPACK_IMPORTED_MODULE_1__.UserRole.CIUDADANO,\n            firstName: dbUser.first_name,\n            lastName: dbUser.last_name,\n            documentType: dbUser.document_type,\n            documentNumber: dbUser.document_number,\n            phone: dbUser.phone,\n            address: dbUser.address,\n            city: dbUser.city,\n            department: dbUser.department,\n            birthDate: dbUser.birth_date,\n            isEmailVerified: dbUser.is_email_verified,\n            isPhoneVerified: dbUser.is_phone_verified,\n            twoFactorEnabled: dbUser.two_factor_enabled,\n            twoFactorStatus: dbUser.two_factor_enabled ? _types_auth__WEBPACK_IMPORTED_MODULE_1__.TwoFactorStatus.ENABLED : _types_auth__WEBPACK_IMPORTED_MODULE_1__.TwoFactorStatus.DISABLED,\n            lastLoginAt: dbUser.last_login_at,\n            createdAt: dbUser.created_at,\n            updatedAt: dbUser.updated_at,\n            metadata: dbUser.metadata\n        };\n    }\n    /**\n     * Map Supabase auth errors to AuthError\n     */ mapSupabaseError(error) {\n        return {\n            code: error.name || 'AUTH_ERROR',\n            message: error.message,\n            details: {\n                originalError: error\n            }\n        };\n    }\n    /**\n     * Map database errors to AuthError\n     */ mapDatabaseError(error) {\n        return {\n            code: error.code || 'DATABASE_ERROR',\n            message: error.message || 'Database operation failed',\n            details: {\n                originalError: error\n            }\n        };\n    }\n    /**\n     * Handle and format errors\n     */ handleError(error, context) {\n        if (error.code && error.message) {\n            return error;\n        }\n        return {\n            code: 'UNKNOWN_ERROR',\n            message: `${context}: ${error.message || 'Unknown error occurred'}`,\n            details: {\n                originalError: error\n            }\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../packages/auth/dist/services/auth-service.js\n");

/***/ }),

/***/ "(rsc)/../../packages/auth/dist/types/auth.js":
/*!**********************************************!*\
  !*** ../../packages/auth/dist/types/auth.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthStatus: () => (/* binding */ AuthStatus),\n/* harmony export */   DocumentNumberSchema: () => (/* binding */ DocumentNumberSchema),\n/* harmony export */   EmailSchema: () => (/* binding */ EmailSchema),\n/* harmony export */   LoginSchema: () => (/* binding */ LoginSchema),\n/* harmony export */   PasswordResetSchema: () => (/* binding */ PasswordResetSchema),\n/* harmony export */   PasswordSchema: () => (/* binding */ PasswordSchema),\n/* harmony export */   PhoneSchema: () => (/* binding */ PhoneSchema),\n/* harmony export */   RegistrationSchema: () => (/* binding */ RegistrationSchema),\n/* harmony export */   TwoFactorStatus: () => (/* binding */ TwoFactorStatus),\n/* harmony export */   TwoFactorVerificationSchema: () => (/* binding */ TwoFactorVerificationSchema),\n/* harmony export */   UpdatePasswordSchema: () => (/* binding */ UpdatePasswordSchema),\n/* harmony export */   UserRole: () => (/* binding */ UserRole)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/../../node_modules/zod/v3/types.js\");\n\n/**\n * User roles in the CHIA system\n */ var UserRole;\n(function(UserRole) {\n    UserRole[\"CIUDADANO\"] = \"ciudadano\";\n    UserRole[\"ADMIN_MUNICIPAL\"] = \"admin_municipal\";\n    UserRole[\"ADMIN_SISTEMA\"] = \"admin_sistema\";\n    UserRole[\"OPERADOR\"] = \"operador\";\n})(UserRole || (UserRole = {}));\n/**\n * Authentication status enum\n */ var AuthStatus;\n(function(AuthStatus) {\n    AuthStatus[\"LOADING\"] = \"loading\";\n    AuthStatus[\"AUTHENTICATED\"] = \"authenticated\";\n    AuthStatus[\"UNAUTHENTICATED\"] = \"unauthenticated\";\n    AuthStatus[\"ERROR\"] = \"error\";\n})(AuthStatus || (AuthStatus = {}));\n/**\n * Two-factor authentication status\n */ var TwoFactorStatus;\n(function(TwoFactorStatus) {\n    TwoFactorStatus[\"DISABLED\"] = \"disabled\";\n    TwoFactorStatus[\"ENABLED\"] = \"enabled\";\n    TwoFactorStatus[\"PENDING_SETUP\"] = \"pending_setup\";\n})(TwoFactorStatus || (TwoFactorStatus = {}));\n/**\n * Validation schemas using Zod\n */ const EmailSchema = zod__WEBPACK_IMPORTED_MODULE_0__.string().email('Email inválido');\nconst PasswordSchema = zod__WEBPACK_IMPORTED_MODULE_0__.string().min(8, 'La contraseña debe tener al menos 8 caracteres').regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/, 'La contraseña debe contener al menos una mayúscula, una minúscula, un número y un carácter especial');\nconst PhoneSchema = zod__WEBPACK_IMPORTED_MODULE_0__.string().regex(/^\\+57[0-9]{10}$/, 'Número de teléfono colombiano inválido (+57XXXXXXXXXX)');\nconst DocumentNumberSchema = zod__WEBPACK_IMPORTED_MODULE_0__.string().min(6, 'Número de documento debe tener al menos 6 dígitos').max(12, 'Número de documento no puede tener más de 12 dígitos').regex(/^\\d+$/, 'Número de documento debe contener solo números');\n/**\n * Validation schemas for forms\n */ const LoginSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    email: EmailSchema,\n    password: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, 'Contraseña requerida'),\n    rememberMe: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().optional(),\n    twoFactorCode: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional()\n});\nconst RegistrationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    email: EmailSchema,\n    password: PasswordSchema,\n    confirmPassword: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    firstName: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(2, 'Nombre debe tener al menos 2 caracteres'),\n    lastName: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(2, 'Apellido debe tener al menos 2 caracteres'),\n    documentType: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'CC',\n        'CE',\n        'TI',\n        'PP'\n    ]),\n    documentNumber: DocumentNumberSchema,\n    phone: PhoneSchema.optional(),\n    city: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(2, 'Ciudad requerida'),\n    department: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(2, 'Departamento requerido'),\n    birthDate: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    acceptTerms: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().refine((val)=>val === true, 'Debe aceptar los términos y condiciones'),\n    acceptPrivacyPolicy: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().refine((val)=>val === true, 'Debe aceptar la política de privacidad')\n}).refine((data)=>data.password === data.confirmPassword, {\n    message: 'Las contraseñas no coinciden',\n    path: [\n        'confirmPassword'\n    ]\n});\nconst PasswordResetSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    email: EmailSchema\n});\nconst UpdatePasswordSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    newPassword: PasswordSchema,\n    confirmPassword: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    resetToken: zod__WEBPACK_IMPORTED_MODULE_0__.string()\n}).refine((data)=>data.newPassword === data.confirmPassword, {\n    message: 'Las contraseñas no coinciden',\n    path: [\n        'confirmPassword'\n    ]\n});\nconst TwoFactorVerificationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    code: zod__WEBPACK_IMPORTED_MODULE_0__.string().length(6, 'Código debe tener 6 dígitos').regex(/^\\d+$/, 'Código debe contener solo números'),\n    backupCode: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../packages/auth/dist/types/auth.js\n");

/***/ }),

/***/ "(rsc)/./app/(auth)/auth/login/page.tsx":
/*!****************************************!*\
  !*** ./app/(auth)/auth/login/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/../../node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_auth_LoginForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth/LoginForm */ \"(rsc)/./components/auth/LoginForm.tsx\");\n\n\n\nfunction LoginPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-6 w-6 text-primary-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\(auth)\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\(auth)\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 10,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\(auth)\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n                            children: \"Iniciar Sesi\\xf3n\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\(auth)\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-center text-sm text-gray-600\",\n                            children: \"Accede a tu cuenta del portal ciudadano\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\(auth)\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\(auth)\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_LoginForm__WEBPACK_IMPORTED_MODULE_2__.LoginForm, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\(auth)\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: [\n                            \"\\xbfNo tienes una cuenta?\",\n                            ' ',\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/auth/register\",\n                                className: \"font-medium text-primary-600 hover:text-primary-500\",\n                                children: \"Reg\\xedstrate aqu\\xed\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\(auth)\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\(auth)\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\(auth)\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\(auth)\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\(auth)\\\\auth\\\\login\\\\page.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/(auth)/auth/login/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/(auth)/layout.tsx":
/*!*******************************!*\
  !*** ./app/(auth)/layout.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _chia_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @chia/auth */ \"(rsc)/../../packages/auth/dist/index.js\");\n\n\nconst metadata = {\n    robots: {\n        index: false,\n        follow: false\n    }\n};\n/**\n * Authentication layout component\n * Provides AuthProvider context for all auth pages\n */ function AuthLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chia_auth__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\(auth)\\\\layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvKGF1dGgpL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTBDO0FBR25DLE1BQU1DLFdBQXFCO0lBQ2hDQyxRQUFRO1FBQ05DLE9BQU87UUFDUEMsUUFBUTtJQUNWO0FBQ0YsRUFBRTtBQU1GOzs7Q0FHQyxHQUNjLFNBQVNDLFdBQVcsRUFBRUMsUUFBUSxFQUFtQjtJQUM5RCxxQkFDRSw4REFBQ04sb0RBQVlBO2tCQUNWTTs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcSnVhbiBQdWxnYXJpblxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxjaGlhLW5leHRcXGFwcHNcXHdlYlxcYXBwXFwoYXV0aClcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnQGNoaWEvYXV0aCc7XG5pbXBvcnQgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICByb2JvdHM6IHtcbiAgICBpbmRleDogZmFsc2UsXG4gICAgZm9sbG93OiBmYWxzZVxuICB9XG59O1xuXG5pbnRlcmZhY2UgQXV0aExheW91dFByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn1cblxuLyoqXG4gKiBBdXRoZW50aWNhdGlvbiBsYXlvdXQgY29tcG9uZW50XG4gKiBQcm92aWRlcyBBdXRoUHJvdmlkZXIgY29udGV4dCBmb3IgYWxsIGF1dGggcGFnZXNcbiAqL1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXV0aExheW91dCh7IGNoaWxkcmVuIH06IEF1dGhMYXlvdXRQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxBdXRoUHJvdmlkZXI+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9BdXRoUHJvdmlkZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiQXV0aFByb3ZpZGVyIiwibWV0YWRhdGEiLCJyb2JvdHMiLCJpbmRleCIsImZvbGxvdyIsIkF1dGhMYXlvdXQiLCJjaGlsZHJlbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/(auth)/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7c3867ab4652\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcSnVhbiBQdWxnYXJpblxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxjaGlhLW5leHRcXGFwcHNcXHdlYlxcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjdjMzg2N2FiNDY1MlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst viewport = {\n    width: 'device-width',\n    initialScale: 1\n};\nconst metadata = {\n    metadataBase: new URL('https://portal.chia-cundinamarca.gov.co'),\n    title: {\n        default: 'CHIA - Portal Ciudadano Digital',\n        template: '%s | CHIA - Portal Ciudadano'\n    },\n    description: 'Portal oficial de servicios ciudadanos para el municipio de Chía, Cundinamarca. Realiza trámites en línea, consulta información municipal y accede a servicios digitales las 24 horas.',\n    keywords: [\n        'Chía',\n        'Cundinamarca',\n        'servicios ciudadanos',\n        'gobierno digital',\n        'trámites en línea',\n        'certificados',\n        'impuestos',\n        'licencias',\n        'portal ciudadano',\n        'alcaldía',\n        'municipio'\n    ],\n    authors: [\n        {\n            name: 'Alcaldía Municipal de Chía'\n        }\n    ],\n    creator: 'Alcaldía Municipal de Chía',\n    publisher: 'Alcaldía Municipal de Chía',\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            'max-video-preview': -1,\n            'max-image-preview': 'large',\n            'max-snippet': -1\n        }\n    },\n    openGraph: {\n        type: 'website',\n        locale: 'es_CO',\n        url: 'https://portal.chia-cundinamarca.gov.co',\n        siteName: 'CHIA - Portal Ciudadano',\n        title: 'CHIA - Portal Ciudadano Digital',\n        description: 'Portal oficial de servicios ciudadanos para el municipio de Chía, Cundinamarca. Trámites en línea las 24 horas.',\n        images: [\n            {\n                url: '/og-image.jpg',\n                width: 1200,\n                height: 630,\n                alt: 'Portal Ciudadano de Chía'\n            }\n        ]\n    },\n    twitter: {\n        card: 'summary_large_image',\n        title: 'CHIA - Portal Ciudadano Digital',\n        description: 'Portal oficial de servicios ciudadanos para el municipio de Chía, Cundinamarca.',\n        images: [\n            '/og-image.jpg'\n        ],\n        creator: '@AlcaldiaChia'\n    },\n    alternates: {\n        canonical: 'https://portal.chia-cundinamarca.gov.co'\n    },\n    category: 'government'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"es\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} h-full`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"root\",\n                className: \"min-h-full\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\layout.tsx\",\n                lineNumber: 75,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\layout.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\layout.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/auth/LoginForm.tsx":
/*!***************************************!*\
  !*** ./components/auth/LoginForm.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LoginForm: () => (/* binding */ LoginForm)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const LoginForm = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call LoginForm() from the server but LoginForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\components\\auth\\LoginForm.tsx",
"LoginForm",
);

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Capps%5C%5Cweb%5C%5Ccomponents%5C%5Cauth%5C%5CLoginForm.tsx%22%2C%22ids%22%3A%5B%22LoginForm%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Capps%5C%5Cweb%5C%5Ccomponents%5C%5Cauth%5C%5CLoginForm.tsx%22%2C%22ids%22%3A%5B%22LoginForm%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/auth/LoginForm.tsx */ \"(ssr)/./components/auth/LoginForm.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/../../node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNKdWFuJTIwUHVsZ2FyaW4lNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDY2hpYS1uZXh0JTVDJTVDYXBwcyU1QyU1Q3dlYiU1QyU1Q2NvbXBvbmVudHMlNUMlNUNhdXRoJTVDJTVDTG9naW5Gb3JtLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkxvZ2luRm9ybSUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNKdWFuJTIwUHVsZ2FyaW4lNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDY2hpYS1uZXh0JTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNhcHAtZGlyJTVDJTVDbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMl9fZXNNb2R1bGUlMjIlMkMlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwS0FBZ0w7QUFDaEw7QUFDQSx3TkFBbU0iLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkxvZ2luRm9ybVwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXEp1YW4gUHVsZ2FyaW5cXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcY2hpYS1uZXh0XFxcXGFwcHNcXFxcd2ViXFxcXGNvbXBvbmVudHNcXFxcYXV0aFxcXFxMb2dpbkZvcm0udHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJfX2VzTW9kdWxlXCIsXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcSnVhbiBQdWxnYXJpblxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxjaGlhLW5leHRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcYXBwLWRpclxcXFxsaW5rLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Capps%5C%5Cweb%5C%5Ccomponents%5C%5Cauth%5C%5CLoginForm.tsx%22%2C%22ids%22%3A%5B%22LoginForm%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/../../node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/../../node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Capps%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Capps%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cpackages%5C%5Cauth%5C%5Cdist%5C%5Ccontext%5C%5Cauth-context.js%22%2C%22ids%22%3A%5B%22*%22%2C%22AuthContext%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cpackages%5C%5Cauth%5C%5Cdist%5C%5Ccontext%5C%5Cauth-context.js%22%2C%22ids%22%3A%5B%22*%22%2C%22AuthContext%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../packages/auth/dist/context/auth-context.js */ \"(ssr)/../../packages/auth/dist/context/auth-context.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNKdWFuJTIwUHVsZ2FyaW4lNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDY2hpYS1uZXh0JTVDJTVDcGFja2FnZXMlNUMlNUNhdXRoJTVDJTVDZGlzdCU1QyU1Q2NvbnRleHQlNUMlNUNhdXRoLWNvbnRleHQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjIqJTIyJTJDJTIyQXV0aENvbnRleHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRNQUFxSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcSnVhbiBQdWxnYXJpblxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxjaGlhLW5leHRcXFxccGFja2FnZXNcXFxcYXV0aFxcXFxkaXN0XFxcXGNvbnRleHRcXFxcYXV0aC1jb250ZXh0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cpackages%5C%5Cauth%5C%5Cdist%5C%5Ccontext%5C%5Cauth-context.js%22%2C%22ids%22%3A%5B%22*%22%2C%22AuthContext%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../packages/auth/dist/context/auth-context.js":
/*!********************************************************!*\
  !*** ../../packages/auth/dist/context/auth-context.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthContext: () => (/* binding */ AuthContext),\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/auth-service */ \"(ssr)/../../packages/auth/dist/services/auth-service.js\");\n/* harmony import */ var _types_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../types/auth */ \"(ssr)/../../packages/auth/dist/types/auth.js\");\n/* __next_internal_client_entry_do_not_use__ AuthContext,AuthProvider auto */ \n\n\n\n/**\n * Initial authentication state\n */ const initialState = {\n    status: _types_auth__WEBPACK_IMPORTED_MODULE_3__.AuthStatus.LOADING,\n    session: null,\n    user: null,\n    error: null\n};\n/**\n * Authentication reducer\n */ function authReducer(state, action) {\n    switch(action.type){\n        case 'SET_LOADING':\n            return {\n                ...state,\n                status: _types_auth__WEBPACK_IMPORTED_MODULE_3__.AuthStatus.LOADING,\n                error: null\n            };\n        case 'SET_AUTHENTICATED':\n            return {\n                ...state,\n                status: _types_auth__WEBPACK_IMPORTED_MODULE_3__.AuthStatus.AUTHENTICATED,\n                session: action.payload.session,\n                user: action.payload.user,\n                error: null\n            };\n        case 'SET_UNAUTHENTICATED':\n            return {\n                ...state,\n                status: _types_auth__WEBPACK_IMPORTED_MODULE_3__.AuthStatus.UNAUTHENTICATED,\n                session: null,\n                user: null,\n                error: null\n            };\n        case 'SET_ERROR':\n            return {\n                ...state,\n                status: _types_auth__WEBPACK_IMPORTED_MODULE_3__.AuthStatus.ERROR,\n                error: action.payload\n            };\n        case 'CLEAR_ERROR':\n            return {\n                ...state,\n                error: null\n            };\n        case 'UPDATE_USER':\n            return {\n                ...state,\n                user: action.payload\n            };\n        default:\n            return state;\n    }\n}\n/**\n * Authentication context\n */ const AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\n/**\n * Authentication provider component\n * Manages authentication state and provides auth methods to child components\n *\n * @example\n * ```tsx\n * function App() {\n *   return (\n *     <AuthProvider>\n *       <Router>\n *         <Routes>\n *           <Route path=\"/login\" element={<LoginPage />} />\n *           <Route path=\"/dashboard\" element={<ProtectedRoute><Dashboard /></ProtectedRoute>} />\n *         </Routes>\n *       </Router>\n *     </AuthProvider>\n *   );\n * }\n * ```\n */ function AuthProvider({ children }) {\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(authReducer, initialState);\n    const authService = new _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.AuthService();\n    /**\n     * Initialize authentication state\n     */ const initializeAuth = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[initializeAuth]\": async ()=>{\n            try {\n                dispatch({\n                    type: 'SET_LOADING'\n                });\n                const user = await authService.getCurrentUser();\n                if (user) {\n                    const session = await authService.refreshSession();\n                    if (session) {\n                        dispatch({\n                            type: 'SET_AUTHENTICATED',\n                            payload: {\n                                session,\n                                user\n                            }\n                        });\n                    } else {\n                        dispatch({\n                            type: 'SET_UNAUTHENTICATED'\n                        });\n                    }\n                } else {\n                    dispatch({\n                        type: 'SET_UNAUTHENTICATED'\n                    });\n                }\n            } catch (error) {\n                console.error('Auth initialization failed:', error);\n                dispatch({\n                    type: 'SET_UNAUTHENTICATED'\n                });\n            }\n        }\n    }[\"AuthProvider.useCallback[initializeAuth]\"], []);\n    /**\n     * Login user\n     */ const login = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[login]\": async (credentials)=>{\n            try {\n                dispatch({\n                    type: 'SET_LOADING'\n                });\n                dispatch({\n                    type: 'CLEAR_ERROR'\n                });\n                const session = await authService.login(credentials);\n                dispatch({\n                    type: 'SET_AUTHENTICATED',\n                    payload: {\n                        session,\n                        user: session.user\n                    }\n                });\n            } catch (error) {\n                const authError = error;\n                dispatch({\n                    type: 'SET_ERROR',\n                    payload: authError\n                });\n                throw authError;\n            }\n        }\n    }[\"AuthProvider.useCallback[login]\"], []);\n    /**\n     * Register new user\n     */ const register = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[register]\": async (data)=>{\n            try {\n                dispatch({\n                    type: 'SET_LOADING'\n                });\n                dispatch({\n                    type: 'CLEAR_ERROR'\n                });\n                const user = await authService.register(data);\n                // After registration, user needs to verify email before being fully authenticated\n                dispatch({\n                    type: 'SET_UNAUTHENTICATED'\n                });\n            } catch (error) {\n                const authError = error;\n                dispatch({\n                    type: 'SET_ERROR',\n                    payload: authError\n                });\n                throw authError;\n            }\n        }\n    }[\"AuthProvider.useCallback[register]\"], []);\n    /**\n     * Logout user\n     */ const logout = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[logout]\": async ()=>{\n            try {\n                await authService.logout();\n                dispatch({\n                    type: 'SET_UNAUTHENTICATED'\n                });\n            } catch (error) {\n                console.error('Logout failed:', error);\n                // Force logout even if server request fails\n                dispatch({\n                    type: 'SET_UNAUTHENTICATED'\n                });\n            }\n        }\n    }[\"AuthProvider.useCallback[logout]\"], []);\n    /**\n     * Request password reset\n     */ const resetPassword = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[resetPassword]\": async (email)=>{\n            try {\n                dispatch({\n                    type: 'CLEAR_ERROR'\n                });\n                await authService.requestPasswordReset(email);\n            } catch (error) {\n                const authError = error;\n                dispatch({\n                    type: 'SET_ERROR',\n                    payload: authError\n                });\n                throw authError;\n            }\n        }\n    }[\"AuthProvider.useCallback[resetPassword]\"], []);\n    /**\n     * Update password\n     */ const updatePassword = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[updatePassword]\": async (data)=>{\n            try {\n                dispatch({\n                    type: 'CLEAR_ERROR'\n                });\n                await authService.updatePassword(data);\n            } catch (error) {\n                const authError = error;\n                dispatch({\n                    type: 'SET_ERROR',\n                    payload: authError\n                });\n                throw authError;\n            }\n        }\n    }[\"AuthProvider.useCallback[updatePassword]\"], []);\n    /**\n     * Update user profile\n     */ const updateProfile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[updateProfile]\": async (data)=>{\n            try {\n                dispatch({\n                    type: 'CLEAR_ERROR'\n                });\n                // Implementation will be added when profile update service is ready\n                throw new Error('Profile update not yet implemented');\n            } catch (error) {\n                const authError = error;\n                dispatch({\n                    type: 'SET_ERROR',\n                    payload: authError\n                });\n                throw authError;\n            }\n        }\n    }[\"AuthProvider.useCallback[updateProfile]\"], []);\n    /**\n     * Refresh authentication session\n     */ const refreshSession = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[refreshSession]\": async ()=>{\n            try {\n                const session = await authService.refreshSession();\n                if (session) {\n                    dispatch({\n                        type: 'SET_AUTHENTICATED',\n                        payload: {\n                            session,\n                            user: session.user\n                        }\n                    });\n                } else {\n                    dispatch({\n                        type: 'SET_UNAUTHENTICATED'\n                    });\n                }\n            } catch (error) {\n                console.error('Session refresh failed:', error);\n                dispatch({\n                    type: 'SET_UNAUTHENTICATED'\n                });\n            }\n        }\n    }[\"AuthProvider.useCallback[refreshSession]\"], []);\n    /**\n     * Setup two-factor authentication\n     */ const setupTwoFactor = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[setupTwoFactor]\": async ()=>{\n            try {\n                dispatch({\n                    type: 'CLEAR_ERROR'\n                });\n                // Implementation will be added in 2FA service\n                throw new Error('Two-factor authentication setup not yet implemented');\n            } catch (error) {\n                const authError = error;\n                dispatch({\n                    type: 'SET_ERROR',\n                    payload: authError\n                });\n                throw authError;\n            }\n        }\n    }[\"AuthProvider.useCallback[setupTwoFactor]\"], []);\n    /**\n     * Verify two-factor authentication\n     */ const verifyTwoFactor = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[verifyTwoFactor]\": async (data)=>{\n            try {\n                dispatch({\n                    type: 'CLEAR_ERROR'\n                });\n                // Implementation will be added in 2FA service\n                throw new Error('Two-factor authentication verification not yet implemented');\n            } catch (error) {\n                const authError = error;\n                dispatch({\n                    type: 'SET_ERROR',\n                    payload: authError\n                });\n                throw authError;\n            }\n        }\n    }[\"AuthProvider.useCallback[verifyTwoFactor]\"], []);\n    /**\n     * Disable two-factor authentication\n     */ const disableTwoFactor = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[disableTwoFactor]\": async (password)=>{\n            try {\n                dispatch({\n                    type: 'CLEAR_ERROR'\n                });\n                // Implementation will be added in 2FA service\n                throw new Error('Two-factor authentication disable not yet implemented');\n            } catch (error) {\n                const authError = error;\n                dispatch({\n                    type: 'SET_ERROR',\n                    payload: authError\n                });\n                throw authError;\n            }\n        }\n    }[\"AuthProvider.useCallback[disableTwoFactor]\"], []);\n    /**\n     * Initialize authentication on mount\n     */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            initializeAuth();\n        }\n    }[\"AuthProvider.useEffect\"], [\n        initializeAuth\n    ]);\n    /**\n     * Context value\n     */ const contextValue = {\n        status: state.status,\n        session: state.session,\n        user: state.user,\n        error: state.error,\n        login,\n        register,\n        logout,\n        resetPassword,\n        updatePassword,\n        updateProfile,\n        refreshSession,\n        setupTwoFactor,\n        verifyTwoFactor,\n        disableTwoFactor\n    };\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(AuthContext.Provider, {\n        value: contextValue,\n        children: children\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/auth/dist/context/auth-context.js\n");

/***/ }),

/***/ "(ssr)/../../packages/auth/dist/lib/supabase-client.js":
/*!*******************************************************!*\
  !*** ../../packages/auth/dist/lib/supabase-client.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseAdminClient: () => (/* binding */ createSupabaseAdminClient),\n/* harmony export */   createSupabaseClient: () => (/* binding */ createSupabaseClient),\n/* harmony export */   getContextualSupabaseClient: () => (/* binding */ getContextualSupabaseClient),\n/* harmony export */   getSupabaseAdminClient: () => (/* binding */ getSupabaseAdminClient),\n/* harmony export */   getSupabaseClient: () => (/* binding */ getSupabaseClient),\n/* harmony export */   isServer: () => (/* binding */ isServer),\n/* harmony export */   resetSupabaseClients: () => (/* binding */ resetSupabaseClients)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/../../node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n/**\n * Environment variables validation\n */ function validateEnvironment() {\n    const url = \"https://hndowofzjzjoljnapokv.supabase.co\";\n    const anonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY\";\n    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!url) {\n        throw new Error('NEXT_PUBLIC_SUPABASE_URL environment variable is required');\n    }\n    if (!anonKey) {\n        throw new Error('NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable is required');\n    }\n    return {\n        url,\n        anonKey,\n        serviceRoleKey\n    };\n}\n/**\n * Create Supabase client for browser/client-side operations\n */ function createSupabaseClient() {\n    const config = validateEnvironment();\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(config.url, config.anonKey, {\n        auth: {\n            autoRefreshToken: true,\n            persistSession: true,\n            detectSessionInUrl: true,\n            flowType: 'pkce'\n        },\n        global: {\n            headers: {\n                'X-Client-Info': 'chia-portal@1.0.0'\n            }\n        }\n    });\n}\n/**\n * Create Supabase admin client for server-side operations\n * Requires service role key for admin operations\n */ function createSupabaseAdminClient() {\n    const config = validateEnvironment();\n    if (!config.serviceRoleKey) {\n        throw new Error('SUPABASE_SERVICE_ROLE_KEY environment variable is required for admin operations');\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(config.url, config.serviceRoleKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        },\n        global: {\n            headers: {\n                'X-Client-Info': 'chia-portal-admin@1.0.0'\n            }\n        }\n    });\n}\n/**\n * Singleton instance for client-side operations\n */ let supabaseClient = null;\n/**\n * Get or create Supabase client instance\n */ function getSupabaseClient() {\n    if (!supabaseClient) {\n        supabaseClient = createSupabaseClient();\n    }\n    return supabaseClient;\n}\n/**\n * Singleton instance for admin operations\n */ let supabaseAdminClient = null;\n/**\n * Get or create Supabase admin client instance\n */ function getSupabaseAdminClient() {\n    if (!supabaseAdminClient) {\n        supabaseAdminClient = createSupabaseAdminClient();\n    }\n    return supabaseAdminClient;\n}\n/**\n * Reset client instances (useful for testing)\n */ function resetSupabaseClients() {\n    supabaseClient = null;\n    supabaseAdminClient = null;\n}\n/**\n * Check if we're running on the server side\n */ function isServer() {\n    return \"undefined\" === 'undefined';\n}\n/**\n * Get appropriate client based on environment\n */ function getContextualSupabaseClient() {\n    return isServer() ? getSupabaseAdminClient() : getSupabaseClient();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/auth/dist/lib/supabase-client.js\n");

/***/ }),

/***/ "(ssr)/../../packages/auth/dist/services/auth-service.js":
/*!*********************************************************!*\
  !*** ../../packages/auth/dist/services/auth-service.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: () => (/* binding */ AuthService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/supabase-client */ \"(ssr)/../../packages/auth/dist/lib/supabase-client.js\");\n/* harmony import */ var _types_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types/auth */ \"(ssr)/../../packages/auth/dist/types/auth.js\");\n\n\n/**\n * Authentication service class\n * Handles all authentication operations with Supabase\n */ class AuthService {\n    constructor(){\n        this.supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.getSupabaseClient)();\n        this.adminSupabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.getSupabaseAdminClient)();\n    }\n    /**\n     * Register a new citizen user\n     */ async register(data) {\n        try {\n            // Step 1: Create auth user with Supabase Auth\n            const { data: authData, error: authError } = await this.supabase.auth.signUp({\n                email: data.email,\n                password: data.password,\n                options: {\n                    data: {\n                        first_name: data.firstName,\n                        last_name: data.lastName,\n                        document_type: data.documentType,\n                        document_number: data.documentNumber,\n                        phone: data.phone,\n                        city: data.city,\n                        department: data.department,\n                        birth_date: data.birthDate,\n                        role: _types_auth__WEBPACK_IMPORTED_MODULE_1__.UserRole.CIUDADANO\n                    }\n                }\n            });\n            if (authError) {\n                throw this.mapSupabaseError(authError);\n            }\n            if (!authData.user) {\n                throw new Error('Failed to create user account');\n            }\n            // Step 2: Create citizen profile in database\n            const { data: profileData, error: profileError } = await this.supabase.from('ciudadano').insert({\n                id: authData.user.id,\n                email: data.email,\n                first_name: data.firstName,\n                last_name: data.lastName,\n                document_type: data.documentType,\n                document_number: data.documentNumber,\n                phone: data.phone,\n                city: data.city,\n                department: data.department,\n                birth_date: data.birthDate,\n                is_email_verified: false,\n                is_phone_verified: false,\n                two_factor_enabled: false\n            }).select().single();\n            if (profileError) {\n                // Cleanup auth user if profile creation fails\n                await this.adminSupabase.auth.admin.deleteUser(authData.user.id);\n                throw this.mapDatabaseError(profileError);\n            }\n            // Step 3: Log registration event\n            await this.logAuditEvent(authData.user.id, 'user_registered', 'user', authData.user.id);\n            return this.mapDatabaseUserToProfile(profileData);\n        } catch (error) {\n            throw this.handleError(error, 'Registration failed');\n        }\n    }\n    /**\n     * Login user with email and password\n     */ async login(credentials) {\n        try {\n            // Step 1: Authenticate with Supabase Auth\n            const { data: authData, error: authError } = await this.supabase.auth.signInWithPassword({\n                email: credentials.email,\n                password: credentials.password\n            });\n            if (authError) {\n                throw this.mapSupabaseError(authError);\n            }\n            if (!authData.user || !authData.session) {\n                throw new Error('Authentication failed');\n            }\n            // Step 2: Get user profile\n            const profile = await this.getUserProfile(authData.user.id);\n            // Step 3: Check if 2FA is required\n            if (profile.twoFactorEnabled && !credentials.twoFactorCode) {\n                throw new Error('Two-factor authentication code required');\n            }\n            // Step 4: Verify 2FA code if provided\n            if (profile.twoFactorEnabled && credentials.twoFactorCode) {\n                await this.verifyTwoFactorCode(authData.user.id, credentials.twoFactorCode);\n            }\n            // Step 5: Update last login timestamp\n            await this.supabase.from('ciudadano').update({\n                last_login_at: new Date().toISOString()\n            }).eq('id', authData.user.id);\n            // Step 6: Create session record\n            const sessionData = await this.createSessionRecord(authData.user.id, authData.session);\n            // Step 7: Log login event\n            await this.logAuditEvent(authData.user.id, 'user_login', 'user', authData.user.id);\n            return {\n                user: profile,\n                accessToken: authData.session.access_token,\n                refreshToken: authData.session.refresh_token,\n                expiresAt: authData.session.expires_at || 0,\n                sessionId: sessionData.id\n            };\n        } catch (error) {\n            throw this.handleError(error, 'Login failed');\n        }\n    }\n    /**\n     * Logout user\n     */ async logout() {\n        try {\n            const { data: { user } } = await this.supabase.auth.getUser();\n            if (user) {\n                // Deactivate session records\n                await this.supabase.from('user_sessions').update({\n                    is_active: false\n                }).eq('user_id', user.id);\n                // Log logout event\n                await this.logAuditEvent(user.id, 'user_logout', 'user', user.id);\n            }\n            // Sign out from Supabase Auth\n            const { error } = await this.supabase.auth.signOut();\n            if (error) {\n                throw this.mapSupabaseError(error);\n            }\n        } catch (error) {\n            throw this.handleError(error, 'Logout failed');\n        }\n    }\n    /**\n     * Get current user profile\n     */ async getCurrentUser() {\n        try {\n            const { data: { user }, error } = await this.supabase.auth.getUser();\n            if (error) {\n                throw this.mapSupabaseError(error);\n            }\n            if (!user) {\n                return null;\n            }\n            return await this.getUserProfile(user.id);\n        } catch (error) {\n            console.error('Failed to get current user:', error);\n            return null;\n        }\n    }\n    /**\n     * Refresh authentication session\n     */ async refreshSession() {\n        try {\n            const { data, error } = await this.supabase.auth.refreshSession();\n            if (error) {\n                throw this.mapSupabaseError(error);\n            }\n            if (!data.session || !data.user) {\n                return null;\n            }\n            const profile = await this.getUserProfile(data.user.id);\n            return {\n                user: profile,\n                accessToken: data.session.access_token,\n                refreshToken: data.session.refresh_token,\n                expiresAt: data.session.expires_at || 0,\n                sessionId: '' // Will be updated by session management\n            };\n        } catch (error) {\n            console.error('Failed to refresh session:', error);\n            return null;\n        }\n    }\n    /**\n     * Request password reset\n     */ async requestPasswordReset(email) {\n        try {\n            const { error } = await this.supabase.auth.resetPasswordForEmail(email, {\n                redirectTo: `${window.location.origin}/auth/reset-password`\n            });\n            if (error) {\n                throw this.mapSupabaseError(error);\n            }\n            // Log password reset request\n            const { data: userData } = await this.supabase.from('ciudadano').select('id').eq('email', email).single();\n            if (userData) {\n                await this.logAuditEvent(userData.id, 'password_reset_requested', 'user', userData.id);\n            }\n        } catch (error) {\n            throw this.handleError(error, 'Password reset request failed');\n        }\n    }\n    /**\n     * Update password with reset token\n     */ async updatePassword(data) {\n        try {\n            const { error } = await this.supabase.auth.updateUser({\n                password: data.newPassword\n            });\n            if (error) {\n                throw this.mapSupabaseError(error);\n            }\n            // Log password update\n            const { data: { user } } = await this.supabase.auth.getUser();\n            if (user) {\n                await this.logAuditEvent(user.id, 'password_updated', 'user', user.id);\n            }\n        } catch (error) {\n            throw this.handleError(error, 'Password update failed');\n        }\n    }\n    /**\n     * Get user profile by ID\n     */ async getUserProfile(userId) {\n        const { data, error } = await this.supabase.from('ciudadano').select('*').eq('id', userId).single();\n        if (error) {\n            throw this.mapDatabaseError(error);\n        }\n        return this.mapDatabaseUserToProfile(data);\n    }\n    /**\n     * Create session record in database\n     */ async createSessionRecord(userId, session) {\n        const { data, error } = await this.supabase.from('user_sessions').insert({\n            user_id: userId,\n            session_token: session.access_token,\n            refresh_token: session.refresh_token,\n            expires_at: new Date(session.expires_at * 1000).toISOString(),\n            is_active: true\n        }).select().single();\n        if (error) {\n            throw this.mapDatabaseError(error);\n        }\n        return data;\n    }\n    /**\n     * Verify two-factor authentication code\n     */ async verifyTwoFactorCode(userId, code) {\n        // Implementation will be added in the 2FA service\n        throw new Error('Two-factor authentication not yet implemented');\n    }\n    /**\n     * Log audit event\n     */ async logAuditEvent(userId, action, resourceType, resourceId, details) {\n        try {\n            await this.supabase.from('audit_log').insert({\n                user_id: userId,\n                action,\n                resource_type: resourceType,\n                resource_id: resourceId,\n                details,\n                created_at: new Date().toISOString()\n            });\n        } catch (error) {\n            console.error('Failed to log audit event:', error);\n        }\n    }\n    /**\n     * Map database user to UserProfile interface\n     */ mapDatabaseUserToProfile(dbUser) {\n        return {\n            id: dbUser.id,\n            email: dbUser.email,\n            role: _types_auth__WEBPACK_IMPORTED_MODULE_1__.UserRole.CIUDADANO,\n            firstName: dbUser.first_name,\n            lastName: dbUser.last_name,\n            documentType: dbUser.document_type,\n            documentNumber: dbUser.document_number,\n            phone: dbUser.phone,\n            address: dbUser.address,\n            city: dbUser.city,\n            department: dbUser.department,\n            birthDate: dbUser.birth_date,\n            isEmailVerified: dbUser.is_email_verified,\n            isPhoneVerified: dbUser.is_phone_verified,\n            twoFactorEnabled: dbUser.two_factor_enabled,\n            twoFactorStatus: dbUser.two_factor_enabled ? _types_auth__WEBPACK_IMPORTED_MODULE_1__.TwoFactorStatus.ENABLED : _types_auth__WEBPACK_IMPORTED_MODULE_1__.TwoFactorStatus.DISABLED,\n            lastLoginAt: dbUser.last_login_at,\n            createdAt: dbUser.created_at,\n            updatedAt: dbUser.updated_at,\n            metadata: dbUser.metadata\n        };\n    }\n    /**\n     * Map Supabase auth errors to AuthError\n     */ mapSupabaseError(error) {\n        return {\n            code: error.name || 'AUTH_ERROR',\n            message: error.message,\n            details: {\n                originalError: error\n            }\n        };\n    }\n    /**\n     * Map database errors to AuthError\n     */ mapDatabaseError(error) {\n        return {\n            code: error.code || 'DATABASE_ERROR',\n            message: error.message || 'Database operation failed',\n            details: {\n                originalError: error\n            }\n        };\n    }\n    /**\n     * Handle and format errors\n     */ handleError(error, context) {\n        if (error.code && error.message) {\n            return error;\n        }\n        return {\n            code: 'UNKNOWN_ERROR',\n            message: `${context}: ${error.message || 'Unknown error occurred'}`,\n            details: {\n                originalError: error\n            }\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/auth/dist/services/auth-service.js\n");

/***/ }),

/***/ "(ssr)/../../packages/auth/dist/types/auth.js":
/*!**********************************************!*\
  !*** ../../packages/auth/dist/types/auth.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthStatus: () => (/* binding */ AuthStatus),\n/* harmony export */   DocumentNumberSchema: () => (/* binding */ DocumentNumberSchema),\n/* harmony export */   EmailSchema: () => (/* binding */ EmailSchema),\n/* harmony export */   LoginSchema: () => (/* binding */ LoginSchema),\n/* harmony export */   PasswordResetSchema: () => (/* binding */ PasswordResetSchema),\n/* harmony export */   PasswordSchema: () => (/* binding */ PasswordSchema),\n/* harmony export */   PhoneSchema: () => (/* binding */ PhoneSchema),\n/* harmony export */   RegistrationSchema: () => (/* binding */ RegistrationSchema),\n/* harmony export */   TwoFactorStatus: () => (/* binding */ TwoFactorStatus),\n/* harmony export */   TwoFactorVerificationSchema: () => (/* binding */ TwoFactorVerificationSchema),\n/* harmony export */   UpdatePasswordSchema: () => (/* binding */ UpdatePasswordSchema),\n/* harmony export */   UserRole: () => (/* binding */ UserRole)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(ssr)/../../node_modules/zod/v3/types.js\");\n\n/**\n * User roles in the CHIA system\n */ var UserRole;\n(function(UserRole) {\n    UserRole[\"CIUDADANO\"] = \"ciudadano\";\n    UserRole[\"ADMIN_MUNICIPAL\"] = \"admin_municipal\";\n    UserRole[\"ADMIN_SISTEMA\"] = \"admin_sistema\";\n    UserRole[\"OPERADOR\"] = \"operador\";\n})(UserRole || (UserRole = {}));\n/**\n * Authentication status enum\n */ var AuthStatus;\n(function(AuthStatus) {\n    AuthStatus[\"LOADING\"] = \"loading\";\n    AuthStatus[\"AUTHENTICATED\"] = \"authenticated\";\n    AuthStatus[\"UNAUTHENTICATED\"] = \"unauthenticated\";\n    AuthStatus[\"ERROR\"] = \"error\";\n})(AuthStatus || (AuthStatus = {}));\n/**\n * Two-factor authentication status\n */ var TwoFactorStatus;\n(function(TwoFactorStatus) {\n    TwoFactorStatus[\"DISABLED\"] = \"disabled\";\n    TwoFactorStatus[\"ENABLED\"] = \"enabled\";\n    TwoFactorStatus[\"PENDING_SETUP\"] = \"pending_setup\";\n})(TwoFactorStatus || (TwoFactorStatus = {}));\n/**\n * Validation schemas using Zod\n */ const EmailSchema = zod__WEBPACK_IMPORTED_MODULE_0__.string().email('Email inválido');\nconst PasswordSchema = zod__WEBPACK_IMPORTED_MODULE_0__.string().min(8, 'La contraseña debe tener al menos 8 caracteres').regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/, 'La contraseña debe contener al menos una mayúscula, una minúscula, un número y un carácter especial');\nconst PhoneSchema = zod__WEBPACK_IMPORTED_MODULE_0__.string().regex(/^\\+57[0-9]{10}$/, 'Número de teléfono colombiano inválido (+57XXXXXXXXXX)');\nconst DocumentNumberSchema = zod__WEBPACK_IMPORTED_MODULE_0__.string().min(6, 'Número de documento debe tener al menos 6 dígitos').max(12, 'Número de documento no puede tener más de 12 dígitos').regex(/^\\d+$/, 'Número de documento debe contener solo números');\n/**\n * Validation schemas for forms\n */ const LoginSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    email: EmailSchema,\n    password: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, 'Contraseña requerida'),\n    rememberMe: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().optional(),\n    twoFactorCode: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional()\n});\nconst RegistrationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    email: EmailSchema,\n    password: PasswordSchema,\n    confirmPassword: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    firstName: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(2, 'Nombre debe tener al menos 2 caracteres'),\n    lastName: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(2, 'Apellido debe tener al menos 2 caracteres'),\n    documentType: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'CC',\n        'CE',\n        'TI',\n        'PP'\n    ]),\n    documentNumber: DocumentNumberSchema,\n    phone: PhoneSchema.optional(),\n    city: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(2, 'Ciudad requerida'),\n    department: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(2, 'Departamento requerido'),\n    birthDate: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    acceptTerms: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().refine((val)=>val === true, 'Debe aceptar los términos y condiciones'),\n    acceptPrivacyPolicy: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().refine((val)=>val === true, 'Debe aceptar la política de privacidad')\n}).refine((data)=>data.password === data.confirmPassword, {\n    message: 'Las contraseñas no coinciden',\n    path: [\n        'confirmPassword'\n    ]\n});\nconst PasswordResetSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    email: EmailSchema\n});\nconst UpdatePasswordSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    newPassword: PasswordSchema,\n    confirmPassword: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    resetToken: zod__WEBPACK_IMPORTED_MODULE_0__.string()\n}).refine((data)=>data.newPassword === data.confirmPassword, {\n    message: 'Las contraseñas no coinciden',\n    path: [\n        'confirmPassword'\n    ]\n});\nconst TwoFactorVerificationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    code: zod__WEBPACK_IMPORTED_MODULE_0__.string().length(6, 'Código debe tener 6 dígitos').regex(/^\\d+$/, 'Código debe contener solo números'),\n    backupCode: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/auth/dist/types/auth.js\n");

/***/ }),

/***/ "(ssr)/./components/auth/LoginForm.tsx":
/*!***************************************!*\
  !*** ./components/auth/LoginForm.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoginForm: () => (/* binding */ LoginForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ LoginForm auto */ \n\n\n\nfunction LoginForm() {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_3__.createClientSupabase)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError(null);\n        try {\n            const { data, error } = await supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) {\n                setError(error.message);\n                return;\n            }\n            if (data.user) {\n                router.push('/dashboard');\n                router.refresh();\n            }\n        } catch (err) {\n            setError('Error inesperado. Por favor intenta de nuevo.');\n            console.error('Login error:', err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleGoogleLogin = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            const { error } = await supabase.auth.signInWithOAuth({\n                provider: 'google',\n                options: {\n                    redirectTo: `${window.location.origin}/auth/callback`\n                }\n            });\n            if (error) {\n                setError(error.message);\n            }\n        } catch (err) {\n            setError('Error inesperado. Por favor intenta de nuevo.');\n            console.error('Google login error:', err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        className: \"mt-8 space-y-6\",\n        onSubmit: handleSubmit,\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"rounded-md bg-red-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-red-700\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\auth\\\\LoginForm.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\auth\\\\LoginForm.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"email\",\n                                className: \"form-label\",\n                                children: \"Correo Electr\\xf3nico\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                id: \"email\",\n                                name: \"email\",\n                                type: \"email\",\n                                autoComplete: \"email\",\n                                required: true,\n                                className: \"form-input\",\n                                value: email,\n                                onChange: (e)=>setEmail(e.target.value),\n                                disabled: loading\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"password\",\n                                className: \"form-label\",\n                                children: \"Contrase\\xf1a\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                id: \"password\",\n                                name: \"password\",\n                                type: \"password\",\n                                autoComplete: \"current-password\",\n                                required: true,\n                                className: \"form-input\",\n                                value: password,\n                                onChange: (e)=>setPassword(e.target.value),\n                                disabled: loading\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\auth\\\\LoginForm.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        disabled: loading,\n                        className: \"btn-primary w-full\",\n                        children: loading ? 'Iniciando sesión...' : 'Iniciar Sesión'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full border-t border-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex justify-center text-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 bg-gray-50 text-gray-500\",\n                                    children: \"O contin\\xfaa con\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: handleGoogleLogin,\n                        disabled: loading,\n                        className: \"btn-secondary w-full flex items-center justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 mr-2\",\n                                viewBox: \"0 0 24 24\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fill: \"currentColor\",\n                                        d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fill: \"currentColor\",\n                                        d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fill: \"currentColor\",\n                                        d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fill: \"currentColor\",\n                                        d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this),\n                            \"Google\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\auth\\\\LoginForm.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\auth\\\\LoginForm.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/auth/LoginForm.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAdminSupabase: () => (/* binding */ createAdminSupabase),\n/* harmony export */   createClientSupabase: () => (/* binding */ createClientSupabase),\n/* harmony export */   createServerSupabase: () => (/* binding */ createServerSupabase),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/../../node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/../../node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n\n\n// Environment variables validation\nconst supabaseUrl = \"https://hndowofzjzjoljnapokv.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error('Missing Supabase environment variables');\n}\n// Client-side Supabase client\nconst createClientSupabase = ()=>{\n    return (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n};\n// Server-side Supabase client - dynamic import to avoid build issues\nconst createServerSupabase = async ()=>{\n    const { cookies } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/next\").then(__webpack_require__.bind(__webpack_require__, /*! next/headers */ \"(ssr)/../../node_modules/next/dist/api/headers.js\"));\n    const cookieStore = cookies();\n    return (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createServerComponentClient)({\n        cookies: ()=>cookieStore\n    });\n};\n// Admin client for server-side operations\nconst createAdminSupabase = ()=>{\n    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!serviceRoleKey) {\n        throw new Error('Missing Supabase service role key');\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, serviceRoleKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n};\n// Default client export for backward compatibility\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/supabase.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?3713":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?8e41":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/@swc","vendor-chunks/isows","vendor-chunks/zod","vendor-chunks/jose","vendor-chunks/set-cookie-parser"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(auth)%2Fauth%2Flogin%2Fpage&page=%2F(auth)%2Fauth%2Flogin%2Fpage&appPaths=%2F(auth)%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();