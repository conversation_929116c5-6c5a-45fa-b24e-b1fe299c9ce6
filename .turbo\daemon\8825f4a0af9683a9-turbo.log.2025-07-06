2025-07-06T05:15:49.290191Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf("apps\\web\\.turbo")}
2025-07-06T05:15:49.290643Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-06T05:15:49.400644Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web"), AnchoredSystemPathBuf("packages\\ui\\.turbo"), AnchoredSystemPathBuf("packages\\ui")}
2025-07-06T05:15:49.400681Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }, WorkspacePackage { name: Other("@chia/ui"), path: AnchoredSystemPathBuf("packages\\ui") }}))
2025-07-06T05:15:49.400747Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-06T05:15:49.415052Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-06T05:15:50.394538Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("node_modules")}
2025-07-06T05:15:50.394570Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-06T05:15:52.799864Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\ui")}
2025-07-06T05:15:52.799906Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/ui"), path: AnchoredSystemPathBuf("packages\\ui") }}))
2025-07-06T05:15:52.821759Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-06T05:16:50.092460Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("README.md"), AnchoredSystemPathBuf("")}
2025-07-06T05:16:50.092494Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-06T05:19:03.801373Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs\\stories\\1.2.authentication-user-management.md")}
2025-07-06T05:19:03.801494Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-06T05:19:38.894033Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs\\stories")}
2025-07-06T05:19:38.894060Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-06T05:20:16.195352Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs\\stories\\1.3.ai-chatbot-knowledge-base.md")}
2025-07-06T05:20:16.195409Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-06T05:20:33.288381Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs\\stories")}
2025-07-06T05:20:33.288405Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-06T05:20:56.401078Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs\\stories\\1.4.semantic-search-content-discovery.md")}
2025-07-06T05:20:56.401282Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-06T05:20:57.102806Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs\\stories")}
2025-07-06T05:20:57.102893Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-06T05:21:32.888256Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs\\stories\\1.5.citizen-portal-dashboard.md")}
2025-07-06T05:21:32.888298Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-06T05:21:33.497810Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs\\stories")}
2025-07-06T05:21:33.497849Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-06T05:22:46.888139Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs\\stories\\1.6.admin-panel-content-management.md")}
2025-07-06T05:22:46.888177Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-06T05:22:47.589521Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs\\stories")}
2025-07-06T05:22:47.589556Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-06T14:25:56.343216Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo")}
2025-07-06T14:25:56.343522Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-06T14:25:56.483202Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
