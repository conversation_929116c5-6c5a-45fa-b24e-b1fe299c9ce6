(()=>{var e={};e.id=974,e.ids=[974],e.modules={154:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,385,23)),Promise.resolve().then(a.t.bind(a,3737,23)),Promise.resolve().then(a.t.bind(a,6081,23)),Promise.resolve().then(a.t.bind(a,1904,23)),Promise.resolve().then(a.t.bind(a,5856,23)),Promise.resolve().then(a.t.bind(a,5492,23)),Promise.resolve().then(a.t.bind(a,9082,23)),Promise.resolve().then(a.t.bind(a,5812,23))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1971:()=>{},2002:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,9355,23)),Promise.resolve().then(a.t.bind(a,4439,23)),Promise.resolve().then(a.t.bind(a,7851,23)),Promise.resolve().then(a.t.bind(a,4730,23)),Promise.resolve().then(a.t.bind(a,9774,23)),Promise.resolve().then(a.t.bind(a,3170,23)),Promise.resolve().then(a.t.bind(a,968,23)),Promise.resolve().then(a.t.bind(a,8298,23))},2108:(e,t,a)=>{Promise.resolve().then(a.bind(a,2711))},2711:!1,3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3514:()=>{},3873:e=>{"use strict";e.exports=require("path")},3964:(e,t,a)=>{Promise.resolve().then(a.bind(a,5851))},4356:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o,metadata:()=>s});var r=a(8828),i=a(7666),n=a.n(i);a(1971);let s={title:{default:"CHIA - Portal Ciudadano Digital",template:"%s | CHIA - Portal Ciudadano"},description:"Portal oficial de servicios ciudadanos para el municipio de Ch\xeda, Cundinamarca. Realiza tr\xe1mites en l\xednea, consulta informaci\xf3n municipal y accede a servicios digitales las 24 horas.",keywords:["Ch\xeda","Cundinamarca","servicios ciudadanos","gobierno digital","tr\xe1mites en l\xednea","certificados","impuestos","licencias","portal ciudadano","alcald\xeda","municipio"],authors:[{name:"Alcald\xeda Municipal de Ch\xeda"}],creator:"Alcald\xeda Municipal de Ch\xeda",publisher:"Alcald\xeda Municipal de Ch\xeda",viewport:"width=device-width, initial-scale=1",robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},openGraph:{type:"website",locale:"es_CO",url:"https://portal.chia-cundinamarca.gov.co",siteName:"CHIA - Portal Ciudadano",title:"CHIA - Portal Ciudadano Digital",description:"Portal oficial de servicios ciudadanos para el municipio de Ch\xeda, Cundinamarca. Tr\xe1mites en l\xednea las 24 horas.",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"Portal Ciudadano de Ch\xeda"}]},twitter:{card:"summary_large_image",title:"CHIA - Portal Ciudadano Digital",description:"Portal oficial de servicios ciudadanos para el municipio de Ch\xeda, Cundinamarca.",images:["/og-image.jpg"],creator:"@AlcaldiaChia"},alternates:{canonical:"https://portal.chia-cundinamarca.gov.co"},category:"government"};function o({children:e}){return(0,r.jsx)("html",{lang:"es",className:"h-full",children:(0,r.jsx)("body",{className:`${n().className} h-full`,children:(0,r.jsx)("div",{id:"root",className:"min-h-full",children:e})})})}},5362:()=>{},5851:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(3952).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\page.tsx","default")},6880:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>s.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=a(4332),i=a(8819),n=a(7851),s=a.n(n),o=a(7540),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(t,l);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,5851)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,4356)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,9033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,2341,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\page.tsx"],p={require:a,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[363,118,114],()=>a(6880));module.exports=r})();