/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/faqs/route";
exports.ids = ["app/api/faqs/route"];
exports.modules = {

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffaqs%2Froute&page=%2Fapi%2Ffaqs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffaqs%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffaqs%2Froute&page=%2Fapi%2Ffaqs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffaqs%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/../../node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/../../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Juan_Pulgarin_Documents_augment_projects_chia_next_apps_web_app_api_faqs_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/faqs/route.ts */ \"(rsc)/./app/api/faqs/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/faqs/route\",\n        pathname: \"/api/faqs\",\n        filename: \"route\",\n        bundlePath: \"app/api/faqs/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\api\\\\faqs\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Juan_Pulgarin_Documents_augment_projects_chia_next_apps_web_app_api_faqs_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffaqs%2Froute&page=%2Fapi%2Ffaqs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffaqs%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!**********************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \**********************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./app/api/faqs/route.ts":
/*!*******************************!*\
  !*** ./app/api/faqs/route.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/../../node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase-server */ \"(rsc)/./lib/supabase-server.ts\");\n\n\nasync function GET(request) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabase)();\n        const { searchParams } = new URL(request.url);\n        // Get query parameters\n        const limit = parseInt(searchParams.get('limit') || '20');\n        const offset = parseInt(searchParams.get('offset') || '0');\n        const tema = searchParams.get('tema');\n        const dependencia = searchParams.get('dependencia');\n        const search = searchParams.get('search');\n        let query = supabase.from('faqs_view').select('*').order('tema').range(offset, offset + limit - 1);\n        // Apply filters\n        if (tema) {\n            query = query.ilike('tema', `%${tema}%`);\n        }\n        if (dependencia) {\n            query = query.eq('dependencia_id', dependencia);\n        }\n        // Apply search filter using ilike for now\n        if (search) {\n            query = query.or(`pregunta.ilike.%${search}%,respuesta.ilike.%${search}%`);\n        }\n        const { data: faqs, error, count } = await query;\n        if (error) {\n            console.error('Error fetching FAQs:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch FAQs',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        // Transform data to match frontend expectations\n        const transformedFaqs = faqs?.map((faq)=>({\n                id: faq.id,\n                tema: faq.tema,\n                pregunta: faq.pregunta,\n                respuesta: faq.respuesta,\n                palabrasClave: [],\n                prioridad: 0,\n                vistas: 0,\n                utilidad: 0,\n                dependencia: {\n                    id: faq.dependencia_id,\n                    codigo: faq.dependencia_codigo,\n                    nombre: faq.dependencia_nombre,\n                    sigla: faq.dependencia_sigla\n                }\n            })) || [];\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: transformedFaqs,\n            pagination: {\n                limit,\n                offset,\n                total: count || transformedFaqs.length\n            },\n            filters: {\n                tema,\n                dependencia,\n                search\n            }\n        });\n    } catch (error) {\n        console.error('Unexpected error in FAQs API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n// Get topics for filtering\nasync function POST(request) {\n    try {\n        const { action } = await request.json();\n        if (action === 'get_topics') {\n            const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabase)();\n            const { data: topics, error } = await supabase.schema('ingestion').from('faqs').select('tema').eq('activo', true).not('tema', 'is', null);\n            if (error) {\n                throw error;\n            }\n            const uniqueTopics = [\n                ...new Set(topics?.map((f)=>f.tema))\n            ].filter(Boolean);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: uniqueTopics\n            });\n        }\n        if (action === 'increment_views') {\n            const { faqId } = await request.json();\n            const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabase)();\n            // First get current views count\n            const { data: currentFaq } = await supabase.schema('ingestion').from('faqs').select('vistas').eq('id', faqId).single();\n            const newViews = (currentFaq?.vistas || 0) + 1;\n            const { error } = await supabase.schema('ingestion').from('faqs').update({\n                vistas: newViews\n            }).eq('id', faqId);\n            if (error) {\n                throw error;\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'Views incremented'\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Invalid action'\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error('Error in FAQs POST:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to process request'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL2ZhcXMvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF3RDtBQUNLO0FBRXRELGVBQWVFLElBQUlDLE9BQW9CO0lBQzVDLElBQUk7UUFDRixNQUFNQyxXQUFXSCwwRUFBb0JBO1FBQ3JDLE1BQU0sRUFBRUksWUFBWSxFQUFFLEdBQUcsSUFBSUMsSUFBSUgsUUFBUUksR0FBRztRQUU1Qyx1QkFBdUI7UUFDdkIsTUFBTUMsUUFBUUMsU0FBU0osYUFBYUssR0FBRyxDQUFDLFlBQVk7UUFDcEQsTUFBTUMsU0FBU0YsU0FBU0osYUFBYUssR0FBRyxDQUFDLGFBQWE7UUFDdEQsTUFBTUUsT0FBT1AsYUFBYUssR0FBRyxDQUFDO1FBQzlCLE1BQU1HLGNBQWNSLGFBQWFLLEdBQUcsQ0FBQztRQUNyQyxNQUFNSSxTQUFTVCxhQUFhSyxHQUFHLENBQUM7UUFFaEMsSUFBSUssUUFBUVgsU0FDVFksSUFBSSxDQUFDLGFBQ0xDLE1BQU0sQ0FBQyxLQUNQQyxLQUFLLENBQUMsUUFDTkMsS0FBSyxDQUFDUixRQUFRQSxTQUFTSCxRQUFRO1FBRWxDLGdCQUFnQjtRQUNoQixJQUFJSSxNQUFNO1lBQ1JHLFFBQVFBLE1BQU1LLEtBQUssQ0FBQyxRQUFRLENBQUMsQ0FBQyxFQUFFUixLQUFLLENBQUMsQ0FBQztRQUN6QztRQUVBLElBQUlDLGFBQWE7WUFDZkUsUUFBUUEsTUFBTU0sRUFBRSxDQUFDLGtCQUFrQlI7UUFDckM7UUFFQSwwQ0FBMEM7UUFDMUMsSUFBSUMsUUFBUTtZQUNWQyxRQUFRQSxNQUFNTyxFQUFFLENBQUMsQ0FBQyxnQkFBZ0IsRUFBRVIsT0FBTyxtQkFBbUIsRUFBRUEsT0FBTyxDQUFDLENBQUM7UUFDM0U7UUFFQSxNQUFNLEVBQUVTLE1BQU1DLElBQUksRUFBRUMsS0FBSyxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNWDtRQUUzQyxJQUFJVSxPQUFPO1lBQ1RFLFFBQVFGLEtBQUssQ0FBQyx3QkFBd0JBO1lBQ3RDLE9BQU96QixxREFBWUEsQ0FBQzRCLElBQUksQ0FDdEI7Z0JBQUVILE9BQU87Z0JBQXdCSSxTQUFTSixNQUFNSyxPQUFPO1lBQUMsR0FDeEQ7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLGdEQUFnRDtRQUNoRCxNQUFNQyxrQkFBa0JSLE1BQU1TLElBQUlDLENBQUFBLE1BQVE7Z0JBQ3hDQyxJQUFJRCxJQUFJQyxFQUFFO2dCQUNWdkIsTUFBTXNCLElBQUl0QixJQUFJO2dCQUNkd0IsVUFBVUYsSUFBSUUsUUFBUTtnQkFDdEJDLFdBQVdILElBQUlHLFNBQVM7Z0JBQ3hCQyxlQUFlLEVBQUU7Z0JBQ2pCQyxXQUFXO2dCQUNYQyxRQUFRO2dCQUNSQyxVQUFVO2dCQUNWNUIsYUFBYTtvQkFDWHNCLElBQUlELElBQUlRLGNBQWM7b0JBQ3RCQyxRQUFRVCxJQUFJVSxrQkFBa0I7b0JBQzlCQyxRQUFRWCxJQUFJWSxrQkFBa0I7b0JBQzlCQyxPQUFPYixJQUFJYyxpQkFBaUI7Z0JBQzlCO1lBQ0YsT0FBTyxFQUFFO1FBRVQsT0FBT2hELHFEQUFZQSxDQUFDNEIsSUFBSSxDQUFDO1lBQ3ZCcUIsU0FBUztZQUNUMUIsTUFBTVM7WUFDTmtCLFlBQVk7Z0JBQ1YxQztnQkFDQUc7Z0JBQ0F3QyxPQUFPekIsU0FBU00sZ0JBQWdCb0IsTUFBTTtZQUN4QztZQUNBQyxTQUFTO2dCQUNQekM7Z0JBQ0FDO2dCQUNBQztZQUNGO1FBQ0Y7SUFFRixFQUFFLE9BQU9XLE9BQU87UUFDZEUsUUFBUUYsS0FBSyxDQUFDLGlDQUFpQ0E7UUFDL0MsT0FBT3pCLHFEQUFZQSxDQUFDNEIsSUFBSSxDQUN0QjtZQUNFSCxPQUFPO1lBQ1BJLFNBQVNKLGlCQUFpQjZCLFFBQVE3QixNQUFNSyxPQUFPLEdBQUc7UUFDcEQsR0FDQTtZQUFFQyxRQUFRO1FBQUk7SUFFbEI7QUFDRjtBQUVBLDJCQUEyQjtBQUNwQixlQUFld0IsS0FBS3BELE9BQW9CO0lBQzdDLElBQUk7UUFDRixNQUFNLEVBQUVxRCxNQUFNLEVBQUUsR0FBRyxNQUFNckQsUUFBUXlCLElBQUk7UUFFckMsSUFBSTRCLFdBQVcsY0FBYztZQUMzQixNQUFNcEQsV0FBV0gsMEVBQW9CQTtZQUVyQyxNQUFNLEVBQUVzQixNQUFNa0MsTUFBTSxFQUFFaEMsS0FBSyxFQUFFLEdBQUcsTUFBTXJCLFNBQ25Dc0QsTUFBTSxDQUFDLGFBQ1AxQyxJQUFJLENBQUMsUUFDTEMsTUFBTSxDQUFDLFFBQ1BJLEVBQUUsQ0FBQyxVQUFVLE1BQ2JzQyxHQUFHLENBQUMsUUFBUSxNQUFNO1lBRXJCLElBQUlsQyxPQUFPO2dCQUNULE1BQU1BO1lBQ1I7WUFFQSxNQUFNbUMsZUFBZTttQkFBSSxJQUFJQyxJQUFJSixRQUFReEIsSUFBSTZCLENBQUFBLElBQUtBLEVBQUVsRCxJQUFJO2FBQUcsQ0FBQ21ELE1BQU0sQ0FBQ0M7WUFFbkUsT0FBT2hFLHFEQUFZQSxDQUFDNEIsSUFBSSxDQUFDO2dCQUN2QnFCLFNBQVM7Z0JBQ1QxQixNQUFNcUM7WUFDUjtRQUNGO1FBRUEsSUFBSUosV0FBVyxtQkFBbUI7WUFDaEMsTUFBTSxFQUFFUyxLQUFLLEVBQUUsR0FBRyxNQUFNOUQsUUFBUXlCLElBQUk7WUFDcEMsTUFBTXhCLFdBQVdILDBFQUFvQkE7WUFFckMsZ0NBQWdDO1lBQ2hDLE1BQU0sRUFBRXNCLE1BQU0yQyxVQUFVLEVBQUUsR0FBRyxNQUFNOUQsU0FDaENzRCxNQUFNLENBQUMsYUFDUDFDLElBQUksQ0FBQyxRQUNMQyxNQUFNLENBQUMsVUFDUEksRUFBRSxDQUFDLE1BQU00QyxPQUNURSxNQUFNO1lBRVQsTUFBTUMsV0FBVyxDQUFDRixZQUFZMUIsVUFBVSxLQUFLO1lBRTdDLE1BQU0sRUFBRWYsS0FBSyxFQUFFLEdBQUcsTUFBTXJCLFNBQ3JCc0QsTUFBTSxDQUFDLGFBQ1AxQyxJQUFJLENBQUMsUUFDTHFELE1BQU0sQ0FBQztnQkFBRTdCLFFBQVE0QjtZQUFTLEdBQzFCL0MsRUFBRSxDQUFDLE1BQU00QztZQUVaLElBQUl4QyxPQUFPO2dCQUNULE1BQU1BO1lBQ1I7WUFFQSxPQUFPekIscURBQVlBLENBQUM0QixJQUFJLENBQUM7Z0JBQ3ZCcUIsU0FBUztnQkFDVG5CLFNBQVM7WUFDWDtRQUNGO1FBRUEsT0FBTzlCLHFEQUFZQSxDQUFDNEIsSUFBSSxDQUN0QjtZQUFFSCxPQUFPO1FBQWlCLEdBQzFCO1lBQUVNLFFBQVE7UUFBSTtJQUdsQixFQUFFLE9BQU9OLE9BQU87UUFDZEUsUUFBUUYsS0FBSyxDQUFDLHVCQUF1QkE7UUFDckMsT0FBT3pCLHFEQUFZQSxDQUFDNEIsSUFBSSxDQUN0QjtZQUFFSCxPQUFPO1FBQTRCLEdBQ3JDO1lBQUVNLFFBQVE7UUFBSTtJQUVsQjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEp1YW4gUHVsZ2FyaW5cXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcY2hpYS1uZXh0XFxhcHBzXFx3ZWJcXGFwcFxcYXBpXFxmYXFzXFxyb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgY3JlYXRlU2VydmVyU3VwYWJhc2UgfSBmcm9tICdAL2xpYi9zdXBhYmFzZS1zZXJ2ZXInO1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVTZXJ2ZXJTdXBhYmFzZSgpO1xuICAgIGNvbnN0IHsgc2VhcmNoUGFyYW1zIH0gPSBuZXcgVVJMKHJlcXVlc3QudXJsKTtcbiAgICBcbiAgICAvLyBHZXQgcXVlcnkgcGFyYW1ldGVyc1xuICAgIGNvbnN0IGxpbWl0ID0gcGFyc2VJbnQoc2VhcmNoUGFyYW1zLmdldCgnbGltaXQnKSB8fCAnMjAnKTtcbiAgICBjb25zdCBvZmZzZXQgPSBwYXJzZUludChzZWFyY2hQYXJhbXMuZ2V0KCdvZmZzZXQnKSB8fCAnMCcpO1xuICAgIGNvbnN0IHRlbWEgPSBzZWFyY2hQYXJhbXMuZ2V0KCd0ZW1hJyk7XG4gICAgY29uc3QgZGVwZW5kZW5jaWEgPSBzZWFyY2hQYXJhbXMuZ2V0KCdkZXBlbmRlbmNpYScpO1xuICAgIGNvbnN0IHNlYXJjaCA9IHNlYXJjaFBhcmFtcy5nZXQoJ3NlYXJjaCcpO1xuXG4gICAgbGV0IHF1ZXJ5ID0gc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdmYXFzX3ZpZXcnKVxuICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAub3JkZXIoJ3RlbWEnKVxuICAgICAgLnJhbmdlKG9mZnNldCwgb2Zmc2V0ICsgbGltaXQgLSAxKTtcblxuICAgIC8vIEFwcGx5IGZpbHRlcnNcbiAgICBpZiAodGVtYSkge1xuICAgICAgcXVlcnkgPSBxdWVyeS5pbGlrZSgndGVtYScsIGAlJHt0ZW1hfSVgKTtcbiAgICB9XG4gICAgXG4gICAgaWYgKGRlcGVuZGVuY2lhKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5LmVxKCdkZXBlbmRlbmNpYV9pZCcsIGRlcGVuZGVuY2lhKTtcbiAgICB9XG5cbiAgICAvLyBBcHBseSBzZWFyY2ggZmlsdGVyIHVzaW5nIGlsaWtlIGZvciBub3dcbiAgICBpZiAoc2VhcmNoKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5Lm9yKGBwcmVndW50YS5pbGlrZS4lJHtzZWFyY2h9JSxyZXNwdWVzdGEuaWxpa2UuJSR7c2VhcmNofSVgKTtcbiAgICB9XG5cbiAgICBjb25zdCB7IGRhdGE6IGZhcXMsIGVycm9yLCBjb3VudCB9ID0gYXdhaXQgcXVlcnk7XG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIEZBUXM6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnRmFpbGVkIHRvIGZldGNoIEZBUXMnLCBkZXRhaWxzOiBlcnJvci5tZXNzYWdlIH0sXG4gICAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyBUcmFuc2Zvcm0gZGF0YSB0byBtYXRjaCBmcm9udGVuZCBleHBlY3RhdGlvbnNcbiAgICBjb25zdCB0cmFuc2Zvcm1lZEZhcXMgPSBmYXFzPy5tYXAoZmFxID0+ICh7XG4gICAgICBpZDogZmFxLmlkLFxuICAgICAgdGVtYTogZmFxLnRlbWEsXG4gICAgICBwcmVndW50YTogZmFxLnByZWd1bnRhLFxuICAgICAgcmVzcHVlc3RhOiBmYXEucmVzcHVlc3RhLFxuICAgICAgcGFsYWJyYXNDbGF2ZTogW10sXG4gICAgICBwcmlvcmlkYWQ6IDAsXG4gICAgICB2aXN0YXM6IDAsXG4gICAgICB1dGlsaWRhZDogMCxcbiAgICAgIGRlcGVuZGVuY2lhOiB7XG4gICAgICAgIGlkOiBmYXEuZGVwZW5kZW5jaWFfaWQsXG4gICAgICAgIGNvZGlnbzogZmFxLmRlcGVuZGVuY2lhX2NvZGlnbyxcbiAgICAgICAgbm9tYnJlOiBmYXEuZGVwZW5kZW5jaWFfbm9tYnJlLFxuICAgICAgICBzaWdsYTogZmFxLmRlcGVuZGVuY2lhX3NpZ2xhXG4gICAgICB9XG4gICAgfSkpIHx8IFtdO1xuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBkYXRhOiB0cmFuc2Zvcm1lZEZhcXMsXG4gICAgICBwYWdpbmF0aW9uOiB7XG4gICAgICAgIGxpbWl0LFxuICAgICAgICBvZmZzZXQsXG4gICAgICAgIHRvdGFsOiBjb3VudCB8fCB0cmFuc2Zvcm1lZEZhcXMubGVuZ3RoXG4gICAgICB9LFxuICAgICAgZmlsdGVyczoge1xuICAgICAgICB0ZW1hLFxuICAgICAgICBkZXBlbmRlbmNpYSxcbiAgICAgICAgc2VhcmNoXG4gICAgICB9XG4gICAgfSk7XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdVbmV4cGVjdGVkIGVycm9yIGluIEZBUXMgQVBJOicsIGVycm9yKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IFxuICAgICAgICBlcnJvcjogJ0ludGVybmFsIHNlcnZlciBlcnJvcicsIFxuICAgICAgICBkZXRhaWxzOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdVbmtub3duIGVycm9yJyBcbiAgICAgIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApO1xuICB9XG59XG5cbi8vIEdldCB0b3BpY3MgZm9yIGZpbHRlcmluZ1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IGFjdGlvbiB9ID0gYXdhaXQgcmVxdWVzdC5qc29uKCk7XG4gICAgXG4gICAgaWYgKGFjdGlvbiA9PT0gJ2dldF90b3BpY3MnKSB7XG4gICAgICBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZVNlcnZlclN1cGFiYXNlKCk7XG4gICAgICBcbiAgICAgIGNvbnN0IHsgZGF0YTogdG9waWNzLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgLnNjaGVtYSgnaW5nZXN0aW9uJylcbiAgICAgICAgLmZyb20oJ2ZhcXMnKVxuICAgICAgICAuc2VsZWN0KCd0ZW1hJylcbiAgICAgICAgLmVxKCdhY3Rpdm8nLCB0cnVlKVxuICAgICAgICAubm90KCd0ZW1hJywgJ2lzJywgbnVsbCk7XG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgIH1cblxuICAgICAgY29uc3QgdW5pcXVlVG9waWNzID0gWy4uLm5ldyBTZXQodG9waWNzPy5tYXAoZiA9PiBmLnRlbWEpKV0uZmlsdGVyKEJvb2xlYW4pO1xuICAgICAgXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICBkYXRhOiB1bmlxdWVUb3BpY3NcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIGlmIChhY3Rpb24gPT09ICdpbmNyZW1lbnRfdmlld3MnKSB7XG4gICAgICBjb25zdCB7IGZhcUlkIH0gPSBhd2FpdCByZXF1ZXN0Lmpzb24oKTtcbiAgICAgIGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlU2VydmVyU3VwYWJhc2UoKTtcbiAgICAgIFxuICAgICAgLy8gRmlyc3QgZ2V0IGN1cnJlbnQgdmlld3MgY291bnRcbiAgICAgIGNvbnN0IHsgZGF0YTogY3VycmVudEZhcSB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgLnNjaGVtYSgnaW5nZXN0aW9uJylcbiAgICAgICAgLmZyb20oJ2ZhcXMnKVxuICAgICAgICAuc2VsZWN0KCd2aXN0YXMnKVxuICAgICAgICAuZXEoJ2lkJywgZmFxSWQpXG4gICAgICAgIC5zaW5nbGUoKTtcblxuICAgICAgY29uc3QgbmV3Vmlld3MgPSAoY3VycmVudEZhcT8udmlzdGFzIHx8IDApICsgMTtcblxuICAgICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgLnNjaGVtYSgnaW5nZXN0aW9uJylcbiAgICAgICAgLmZyb20oJ2ZhcXMnKVxuICAgICAgICAudXBkYXRlKHsgdmlzdGFzOiBuZXdWaWV3cyB9KVxuICAgICAgICAuZXEoJ2lkJywgZmFxSWQpO1xuXG4gICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgIG1lc3NhZ2U6ICdWaWV3cyBpbmNyZW1lbnRlZCdcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdJbnZhbGlkIGFjdGlvbicgfSxcbiAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICk7XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBpbiBGQVFzIFBPU1Q6JywgZXJyb3IpO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdGYWlsZWQgdG8gcHJvY2VzcyByZXF1ZXN0JyB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsImNyZWF0ZVNlcnZlclN1cGFiYXNlIiwiR0VUIiwicmVxdWVzdCIsInN1cGFiYXNlIiwic2VhcmNoUGFyYW1zIiwiVVJMIiwidXJsIiwibGltaXQiLCJwYXJzZUludCIsImdldCIsIm9mZnNldCIsInRlbWEiLCJkZXBlbmRlbmNpYSIsInNlYXJjaCIsInF1ZXJ5IiwiZnJvbSIsInNlbGVjdCIsIm9yZGVyIiwicmFuZ2UiLCJpbGlrZSIsImVxIiwib3IiLCJkYXRhIiwiZmFxcyIsImVycm9yIiwiY291bnQiLCJjb25zb2xlIiwianNvbiIsImRldGFpbHMiLCJtZXNzYWdlIiwic3RhdHVzIiwidHJhbnNmb3JtZWRGYXFzIiwibWFwIiwiZmFxIiwiaWQiLCJwcmVndW50YSIsInJlc3B1ZXN0YSIsInBhbGFicmFzQ2xhdmUiLCJwcmlvcmlkYWQiLCJ2aXN0YXMiLCJ1dGlsaWRhZCIsImRlcGVuZGVuY2lhX2lkIiwiY29kaWdvIiwiZGVwZW5kZW5jaWFfY29kaWdvIiwibm9tYnJlIiwiZGVwZW5kZW5jaWFfbm9tYnJlIiwic2lnbGEiLCJkZXBlbmRlbmNpYV9zaWdsYSIsInN1Y2Nlc3MiLCJwYWdpbmF0aW9uIiwidG90YWwiLCJsZW5ndGgiLCJmaWx0ZXJzIiwiRXJyb3IiLCJQT1NUIiwiYWN0aW9uIiwidG9waWNzIiwic2NoZW1hIiwibm90IiwidW5pcXVlVG9waWNzIiwiU2V0IiwiZiIsImZpbHRlciIsIkJvb2xlYW4iLCJmYXFJZCIsImN1cnJlbnRGYXEiLCJzaW5nbGUiLCJuZXdWaWV3cyIsInVwZGF0ZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/api/faqs/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/supabase-server.ts":
/*!********************************!*\
  !*** ./lib/supabase-server.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAdminSupabase: () => (/* binding */ createAdminSupabase),\n/* harmony export */   createServerSupabase: () => (/* binding */ createServerSupabase),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/../../node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Environment variables validation\nconst supabaseUrl = \"https://hndowofzjzjoljnapokv.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY\";\nconst serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error('Missing Supabase environment variables');\n}\n// Admin client for server-side operations (API routes)\nfunction createAdminSupabase() {\n    // For now, use anon key if service role key is not available\n    const key = serviceRoleKey || supabaseAnonKey;\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, key, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n// Regular client for server-side operations\nfunction createServerSupabase() {\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n}\n// Default export for API routes\nconst supabase = createServerSupabase();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/supabase-server.ts\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!**********************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \**********************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?3713":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?8e41":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/isows","vendor-chunks/tr46","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffaqs%2Froute&page=%2Fapi%2Ffaqs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffaqs%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();