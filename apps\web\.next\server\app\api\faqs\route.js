(()=>{var e={};e.id=349,e.ids=[349],e.modules={1241:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},40585:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>x,routeModule:()=>u,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>g});var t={};s.r(t),s.d(t,{GET:()=>p,POST:()=>c});var i=s(48106),a=s(48819),n=s(12050),o=s(4235),d=s(73474);async function p(e){try{let r=(0,d.y8)(),{searchParams:s}=new URL(e.url),t=parseInt(s.get("limit")||"20"),i=parseInt(s.get("offset")||"0"),a=s.get("tema"),n=s.get("dependencia"),p=s.get("search"),c=r.schema("ingestion").from("faqs").select(`
        id,
        tema,
        descripcion,
        pregunta,
        respuesta,
        palabras_clave,
        prioridad,
        vistas,
        utilidad_promedio,
        dependencias:dependencia_id (
          id,
          codigo,
          nombre,
          sigla
        ),
        subdependencias:subdependencia_id (
          id,
          codigo,
          nombre,
          sigla
        )
      `).eq("activo",!0).order("prioridad",{ascending:!1}).order("vistas",{ascending:!1}).range(i,i+t-1);a&&(c=c.ilike("tema",`%${a}%`)),n&&(c=c.eq("dependencia_id",n)),p&&(c=c.textSearch("vector_busqueda",p,{type:"websearch",config:"spanish"}));let{data:u,error:l,count:g}=await c;if(l)return console.error("Error fetching FAQs:",l),o.NextResponse.json({error:"Failed to fetch FAQs",details:l.message},{status:500});let m=u?.map(e=>({id:e.id,tema:e.tema,descripcion:e.descripcion,pregunta:e.pregunta,respuesta:e.respuesta,palabrasClave:e.palabras_clave||[],prioridad:e.prioridad||0,vistas:e.vistas||0,utilidad:e.utilidad_promedio||0,dependencia:{id:e.dependencias?.id,codigo:e.dependencias?.codigo,nombre:e.dependencias?.nombre,sigla:e.dependencias?.sigla},subdependencia:e.subdependencias?{id:e.subdependencias.id,codigo:e.subdependencias.codigo,nombre:e.subdependencias.nombre,sigla:e.subdependencias.sigla}:null}))||[];return o.NextResponse.json({success:!0,data:m,pagination:{limit:t,offset:i,total:g||m.length},filters:{tema:a,dependencia:n,search:p}})}catch(e){return console.error("Unexpected error in FAQs API:",e),o.NextResponse.json({error:"Internal server error",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function c(e){try{let{action:r}=await e.json();if("get_topics"===r){let e=(0,d.y8)(),{data:r,error:s}=await e.schema("ingestion").from("faqs").select("tema").eq("activo",!0).not("tema","is",null);if(s)throw s;let t=[...new Set(r?.map(e=>e.tema))].filter(Boolean);return o.NextResponse.json({success:!0,data:t})}if("increment_views"===r){let{faqId:r}=await e.json(),s=(0,d.y8)(),{error:t}=await s.schema("ingestion").from("faqs").update({vistas:s.raw("vistas + 1")}).eq("id",r);if(t)throw t;return o.NextResponse.json({success:!0,message:"Views incremented"})}return o.NextResponse.json({error:"Invalid action"},{status:400})}catch(e){return console.error("Error in FAQs POST:",e),o.NextResponse.json({error:"Failed to process request"},{status:500})}}let u=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/faqs/route",pathname:"/api/faqs",filename:"route",bundlePath:"app/api/faqs/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\api\\faqs\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:l,workUnitAsyncStorage:g,serverHooks:m}=u;function x(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:g})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72088:()=>{},73474:(e,r,s)=>{"use strict";s.d(r,{y8:()=>n});var t=s(2492);let i=process.env.NEXT_PUBLIC_SUPABASE_URL,a=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;if(process.env.SUPABASE_SERVICE_ROLE_KEY,!i||!a)throw Error("Missing Supabase environment variables");let n=()=>(0,t.UU)(i,a);n()},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80408:()=>{},81630:e=>{"use strict";e.exports=require("http")},87032:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[191,111,744],()=>s(40585));module.exports=t})();