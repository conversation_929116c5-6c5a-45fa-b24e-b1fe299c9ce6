"use client";

import { 
  <PERSON><PERSON>utt<PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  AnimatedSubscribeButton, 
  <PERSON><PERSON><PERSON><PERSON>utton,
  <PERSON><PERSON>utton,
  <PERSON><PERSON><PERSON>ingButton,
  InteractiveHoverButton
} from "@/components/ui/button";

export default function MagicUIButtonsDemo() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Magic UI Buttons Demo
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Showcase of all Magic UI button components integrated into Chia Next
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* <PERSON> */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
              Rainbow Button
            </h3>
            <div className="space-y-4">
              <RainbowButton>
                Default Rainbow
              </RainbowButton>
              <RainbowButton variant="outline" size="lg">
                Large Outline
              </RainbowButton>
              <RainbowButton size="sm">
                Small Rainbow
              </RainbowButton>
            </div>
          </div>

          {/* Shiny Button */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
              Shiny Button
            </h3>
            <div className="space-y-4">
              <ShinyButton>
                Shiny Effect
              </ShinyButton>
              <ShinyButton className="bg-blue-500 hover:bg-blue-600">
                Custom Blue
              </ShinyButton>
            </div>
          </div>

          {/* Animated Subscribe Button */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
              Animated Subscribe
            </h3>
            <div className="space-y-4">
              <AnimatedSubscribeButton>
                <span>Subscribe</span>
                <span>Subscribed!</span>
              </AnimatedSubscribeButton>
            </div>
          </div>

          {/* Ripple Button */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
              Ripple Button
            </h3>
            <div className="space-y-4">
              <RippleButton>
                Click for Ripple
              </RippleButton>
              <RippleButton rippleColor="#3b82f6" className="bg-blue-500 text-white">
                Blue Ripple
              </RippleButton>
            </div>
          </div>

          {/* Shimmer Button */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
              Shimmer Button
            </h3>
            <div className="space-y-4">
              <ShimmerButton>
                Shimmer Effect
              </ShimmerButton>
              <ShimmerButton className="bg-green-500 hover:bg-green-600">
                Green Shimmer
              </ShimmerButton>
            </div>
          </div>

          {/* Pulsating Button */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
              Pulsating Button
            </h3>
            <div className="space-y-4">
              <PulsatingButton>
                Pulsating Effect
              </PulsatingButton>
              <PulsatingButton pulseColor="#ef4444" duration="1s">
                Red Pulse
              </PulsatingButton>
            </div>
          </div>

          {/* Interactive Hover Button */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
              Interactive Hover
            </h3>
            <div className="space-y-4">
              <InteractiveHoverButton>
                Hover Me
              </InteractiveHoverButton>
              <InteractiveHoverButton className="bg-purple-500 hover:bg-purple-600">
                Purple Hover
              </InteractiveHoverButton>
            </div>
          </div>
        </div>

        {/* Usage Examples */}
        <div className="mt-16 bg-white dark:bg-gray-800 p-8 rounded-lg shadow-lg">
          <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">
            Usage Examples
          </h2>
          <div className="space-y-4 text-sm">
            <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded">
              <code className="text-gray-800 dark:text-gray-200">
                {`import { RainbowButton } from "@/components/ui/button";

<RainbowButton variant="outline" size="lg">
  Click me!
</RainbowButton>`}
              </code>
            </div>
            <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded">
              <code className="text-gray-800 dark:text-gray-200">
                {`import { AnimatedSubscribeButton } from "@/components/ui/button";

<AnimatedSubscribeButton>
  <span>Subscribe</span>
  <span>Subscribed!</span>
</AnimatedSubscribeButton>`}
              </code>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
