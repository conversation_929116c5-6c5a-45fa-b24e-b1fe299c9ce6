import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabase();
    const { searchParams } = new URL(request.url);
    
    // Get query parameters
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');
    const categoria = searchParams.get('categoria');
    const dependencia = searchParams.get('dependencia');
    const search = searchParams.get('search');

    let query = supabase
      .schema('ingestion')
      .from('tramites')
      .select(`
        id,
        nombre,
        descripcion,
        formulario,
        tiempo_respuesta,
        tiene_pago,
        costo_detalle,
        url_suit,
        url_govco,
        categoria,
        modalidad,
        requisitos,
        documentos_requeridos,
        popularidad,
        satisfaccion_promedio,
        dependencias:dependencia_id (
          id,
          codigo,
          nombre,
          sigla
        ),
        subdependencias:subdependencia_id (
          id,
          codigo,
          nombre,
          sigla
        )
      `)
      .eq('activo', true)
      .order('popularidad', { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply filters
    if (categoria) {
      query = query.eq('categoria', categoria);
    }
    
    if (dependencia) {
      query = query.eq('dependencia_id', dependencia);
    }

    // Apply search filter using full-text search
    if (search) {
      query = query.textSearch('vector_busqueda', search, {
        type: 'websearch',
        config: 'spanish'
      });
    }

    const { data: tramites, error, count } = await query;

    if (error) {
      console.error('Error fetching tramites:', error);
      return NextResponse.json(
        { error: 'Failed to fetch government procedures', details: error.message },
        { status: 500 }
      );
    }

    // Transform data to match frontend expectations
    const transformedTramites = tramites?.map(tramite => ({
      id: tramite.id,
      nombre: tramite.nombre,
      descripcion: tramite.descripcion || '',
      categoria: tramite.categoria || 'General',
      tiempoRespuesta: tramite.tiempo_respuesta || 'No especificado',
      tienePago: tramite.tiene_pago || 'No especificado',
      costoDetalle: tramite.costo_detalle,
      modalidad: tramite.modalidad || [],
      requisitos: tramite.requisitos || [],
      documentosRequeridos: tramite.documentos_requeridos || [],
      urlSuit: tramite.url_suit,
      urlGovco: tramite.url_govco,
      popularidad: tramite.popularidad || 0,
      satisfaccion: tramite.satisfaccion_promedio || 0,
      dependencia: {
        id: (tramite.dependencias as any)?.id,
        codigo: (tramite.dependencias as any)?.codigo,
        nombre: (tramite.dependencias as any)?.nombre,
        sigla: (tramite.dependencias as any)?.sigla
      },
      subdependencia: tramite.subdependencias ? {
        id: (tramite.subdependencias as any).id,
        codigo: (tramite.subdependencias as any).codigo,
        nombre: (tramite.subdependencias as any).nombre,
        sigla: (tramite.subdependencias as any).sigla
      } : null
    })) || [];

    return NextResponse.json({
      success: true,
      data: transformedTramites,
      pagination: {
        limit,
        offset,
        total: count || transformedTramites.length
      },
      filters: {
        categoria,
        dependencia,
        search
      }
    });

  } catch (error) {
    console.error('Unexpected error in tramites API:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

// Get categories for filtering
export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();
    
    if (action === 'get_categories') {
      const supabase = createServerSupabase();
      
      const { data: categories, error } = await supabase
        .schema('ingestion')
        .from('tramites')
        .select('categoria')
        .eq('activo', true)
        .not('categoria', 'is', null);

      if (error) {
        throw error;
      }

      const uniqueCategories = [...new Set(categories?.map(t => t.categoria))].filter(Boolean);
      
      return NextResponse.json({
        success: true,
        data: uniqueCategories
      });
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error in tramites POST:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}
