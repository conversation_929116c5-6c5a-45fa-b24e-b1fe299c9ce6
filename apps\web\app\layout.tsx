import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: {
    default: 'CHIA - Portal Ciudadano Digital',
    template: '%s | CHIA - Portal Ciudadano'
  },
  description: 'Portal oficial de servicios ciudadanos para el municipio de Chía, Cundinamarca. Realiza trámites en línea, consulta información municipal y accede a servicios digitales las 24 horas.',
  keywords: [
    'Chía', 'Cundinamarca', 'servicios ciudadanos', 'gobierno digital',
    'trámites en línea', 'certificados', 'impuestos', 'licencias',
    'portal ciudadano', 'alcaldía', 'municipio'
  ],
  authors: [{ name: 'Alcaldía Municipal de Chía' }],
  creator: 'Alcaldía Municipal de Chía',
  publisher: 'Alcaldía Municipal de Chía',
  viewport: 'width=device-width, initial-scale=1',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'es_CO',
    url: 'https://portal.chia-cundinamarca.gov.co',
    siteName: 'CHIA - Portal Ciudadano',
    title: 'CHIA - Portal Ciudadano Digital',
    description: 'Portal oficial de servicios ciudadanos para el municipio de Chía, Cundinamarca. Trámites en línea las 24 horas.',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Portal Ciudadano de Chía',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'CHIA - Portal Ciudadano Digital',
    description: 'Portal oficial de servicios ciudadanos para el municipio de Chía, Cundinamarca.',
    images: ['/og-image.jpg'],
    creator: '@AlcaldiaChia',
  },
  alternates: {
    canonical: 'https://portal.chia-cundinamarca.gov.co',
  },
  category: 'government',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="es" className="h-full">
      <body className={`${inter.className} h-full`}>
        <div id="root" className="min-h-full">
          {children}
        </div>
      </body>
    </html>
  );
}
