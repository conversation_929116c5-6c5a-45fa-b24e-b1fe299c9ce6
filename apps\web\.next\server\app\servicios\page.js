(()=>{var e={};e.id=104,e.ids=[104],e.modules={1421:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n,metadata:()=>o});var s=t(38828),a=t(25089),i=t(51496);let o={title:"Servicios Ciudadanos | Portal CHIA",description:"Accede a todos los servicios digitales del municipio de Ch\xeda. Certificados, pagos, licencias y m\xe1s.",keywords:"servicios municipales, certificados, pagos en l\xednea, licencias, Ch\xeda"};function n(){return(0,s.jsx)(a.default,{className:"bg-gray-50",children:(0,s.jsx)(i.default,{})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5856:(e,r,t)=>{var s=t(86626),a=t(22759),i=t(97372),o=0/0,n=/^[-+]0x[0-9a-f]+$/i,l=/^0b[01]+$/i,c=/^0o[0-7]+$/i,d=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(i(e))return o;if(a(e)){var r="function"==typeof e.valueOf?e.valueOf():e;e=a(r)?r+"":r}if("string"!=typeof e)return 0===e?e:+e;e=s(e);var t=l.test(e);return t||c.test(e)?d(e.slice(2),t?2:8):n.test(e)?o:+e}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12463:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(60159);let a=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))})},14062:e=>{var r=/\s/;e.exports=function(e){for(var t=e.length;t--&&r.test(e.charAt(t)););return t}},16646:(e,r,t)=>{var s=t(64339),a=t(64897),i=t(35380),o=s?s.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":o&&o in Object(e)?a(e):i(e)}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22759:e=>{e.exports=function(e){var r=typeof e;return null!=e&&("object"==r||"function"==r)}},24035:(e,r,t)=>{Promise.resolve().then(t.bind(t,25089)),Promise.resolve().then(t.bind(t,51496))},25089:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(33952).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\PageLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\components\\layout\\PageLayout.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35380:e=>{var r=Object.prototype.toString;e.exports=function(e){return r.call(e)}},44304:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=t(24332),a=t(48819),i=t(67851),o=t.n(i),n=t(97540),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let c={children:["",{children:["servicios",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1421)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\servicios\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,34356)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\servicios\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/servicios/page",pathname:"/servicios",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},47779:(e,r,t)=>{var s=t(22759),a=t(55554),i=t(5856),o=Math.max,n=Math.min;e.exports=function(e,r,t){var l,c,d,u,m,p,x=0,g=!1,h=!1,f=!0;if("function"!=typeof e)throw TypeError("Expected a function");function v(r){var t=l,s=c;return l=c=void 0,x=r,u=e.apply(s,t)}function b(e){var t=e-p,s=e-x;return void 0===p||t>=r||t<0||h&&s>=d}function y(){var e,t,s,i=a();if(b(i))return j(i);m=setTimeout(y,(e=i-p,t=i-x,s=r-e,h?n(s,d-t):s))}function j(e){return(m=void 0,f&&l)?v(e):(l=c=void 0,u)}function w(){var e,t=a(),s=b(t);if(l=arguments,c=this,p=t,s){if(void 0===m)return x=e=p,m=setTimeout(y,r),g?v(e):u;if(h)return clearTimeout(m),m=setTimeout(y,r),v(p)}return void 0===m&&(m=setTimeout(y,r)),u}return r=i(r)||0,s(t)&&(g=!!t.leading,d=(h="maxWait"in t)?o(i(t.maxWait)||0,r):d,f="trailing"in t?!!t.trailing:f),w.cancel=function(){void 0!==m&&clearTimeout(m),x=0,l=p=c=m=void 0},w.flush=function(){return void 0===m?u:j(a())},w}},49124:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},51496:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(33952).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\services\\\\ServicesPageClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\components\\services\\ServicesPageClient.tsx","default")},53456:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(60159);let a=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},55554:(e,r,t)=>{var s=t(71207);e.exports=function(){return s.Date.now()}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64339:(e,r,t)=>{e.exports=t(71207).Symbol},64897:(e,r,t)=>{var s=t(64339),a=Object.prototype,i=a.hasOwnProperty,o=a.toString,n=s?s.toStringTag:void 0;e.exports=function(e){var r=i.call(e,n),t=e[n];try{e[n]=void 0;var s=!0}catch(e){}var a=o.call(e);return s&&(r?e[n]=t:delete e[n]),a}},71207:(e,r,t)=>{var s=t(82578),a="object"==typeof self&&self&&self.Object===Object&&self;e.exports=s||a||Function("return this")()},81068:(e,r,t)=>{"use strict";t.d(r,{default:()=>w});var s=t(13486),a=t(60159),i=t(49989),o=t.n(i),n=t(86604),l=t(8721),c=t(70726),d=t(88160),u=t(38149),m=t(12463);let p=a.forwardRef(function({title:e,titleId:r,...t},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},t),e?a.createElement("title",{id:r},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"}))});var x=t(53456);let g=e=>{let r={certificados:n.A,pagos:l.A,licencias:c.A,registro:d.A,consultas:u.A,default:n.A};return r[e.toLowerCase()]||r.default},h=e=>({certificados:"/servicios/certificados",pagos:"/servicios/pagos",licencias:"/servicios/licencias",registro:"/servicios/registro",consultas:"/servicios/consultas"})[e.categoria.toLowerCase()]||`/servicios/tramite/${e.id}`,f=e=>e>=7;function v({searchQuery:e,selectedCategory:r}){let[t,i]=(0,a.useState)([]),[n,l]=(0,a.useState)(!0),[c,d]=(0,a.useState)(null),[v,b]=(0,a.useState)([]),y=async()=>{try{l(!0),d(null);let t=new URLSearchParams;t.append("limit","20"),e&&t.append("search",e),r&&"Todos"!==r&&t.append("categoria",r);let s=await fetch(`/api/tramites?${t.toString()}`);if(!s.ok)throw Error(`Error ${s.status}: ${s.statusText}`);let a=await s.json();if(a.success)i(a.data||[]);else throw Error(a.error||"Failed to fetch services")}catch(e){console.error("Error fetching tramites:",e),d(e instanceof Error?e.message:"Error loading services")}finally{l(!1)}};return n?(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[...Array(6)].map((e,r)=>(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-md p-6 animate-pulse",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-lg"}),(0,s.jsx)("div",{className:"w-16 h-6 bg-gray-200 rounded-full"})]}),(0,s.jsx)("div",{className:"w-3/4 h-6 bg-gray-200 rounded mb-2"}),(0,s.jsx)("div",{className:"w-full h-4 bg-gray-200 rounded mb-4"}),(0,s.jsx)("div",{className:"w-1/2 h-4 bg-gray-200 rounded mb-4"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("div",{className:"w-full h-3 bg-gray-200 rounded"}),(0,s.jsx)("div",{className:"w-3/4 h-3 bg-gray-200 rounded"})]})]},r))}):c?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)(m.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Error al cargar servicios"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:c}),(0,s.jsx)("button",{onClick:y,className:"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors",children:"Reintentar"})]}):0===t.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)(u.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No se encontraron servicios"}),(0,s.jsx)("p",{className:"text-gray-600",children:e?`No hay servicios que coincidan con "${e}"`:"No hay servicios disponibles en esta categor\xeda"})]}):(0,s.jsxs)(s.Fragment,{children:[v.length>0&&(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsx)("div",{className:"flex flex-wrap gap-2 justify-center",children:v.map(e=>(0,s.jsx)("button",{className:`px-4 py-2 rounded-full text-sm font-medium border transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 ${r===e||!r&&"Todos"===e?"bg-primary-600 border-primary-600 text-white":"bg-white border-gray-300 text-gray-700 hover:bg-primary-50 hover:border-primary-300 hover:text-primary-700"}`,children:e},e))})}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:t.map(e=>{let r=g(e.categoria),t=h(e),a=f(e.popularidad);return(0,s.jsxs)(o(),{href:t,className:"group bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-200 hover:border-primary-300",children:[(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("div",{className:"flex items-start justify-between mb-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-3 bg-primary-100 rounded-lg group-hover:bg-primary-200 transition-colors",children:(0,s.jsx)(r,{className:"h-6 w-6 text-primary-600"})}),a&&(0,s.jsxs)("div",{className:"flex items-center gap-1 px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium",children:[(0,s.jsx)(p,{className:"h-3 w-3"}),"Popular"]})]})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors",children:e.nombre}),(0,s.jsx)("p",{className:"text-gray-600 mb-4 text-sm leading-relaxed line-clamp-3",children:e.descripcion}),(0,s.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-500 mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(x.A,{className:"h-4 w-4"}),e.tiempoRespuesta]}),(0,s.jsx)("span",{className:"px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs",children:e.categoria})]}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-xs text-gray-500",children:[(0,s.jsx)("div",{className:"w-1 h-1 bg-primary-400 rounded-full"}),e.dependencia.nombre]}),e.modalidad.length>0&&(0,s.jsxs)("div",{className:"flex items-center gap-2 text-xs text-gray-500",children:[(0,s.jsx)("div",{className:"w-1 h-1 bg-primary-400 rounded-full"}),e.modalidad.join(", ")]}),"No especificado"!==e.tienePago&&(0,s.jsxs)("div",{className:"flex items-center gap-2 text-xs text-gray-500",children:[(0,s.jsx)("div",{className:"w-1 h-1 bg-primary-400 rounded-full"}),e.tienePago]})]})]}),(0,s.jsx)("div",{className:"px-6 py-4 bg-gray-50 group-hover:bg-primary-50 transition-colors",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700 group-hover:text-primary-700",children:"Acceder al servicio"}),(0,s.jsx)("svg",{className:"h-4 w-4 text-gray-400 group-hover:text-primary-600 group-hover:translate-x-1 transition-all",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})})]},e.id)})})]})}var b=t(47779),y=t.n(b);function j({onSearch:e,placeholder:r="Buscar servicios...",className:t=""}){let[i,o]=(0,a.useState)(""),n=(0,a.useCallback)(y()(r=>{e(r)},300),[e]);return(0,s.jsx)("form",{onSubmit:r=>{r.preventDefault(),e(i)},className:t,children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(u.A,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,s.jsx)("input",{type:"text",value:i,onChange:e=>{let r=e.target.value;o(r),n(r)},placeholder:r,className:"w-full pl-12 pr-4 py-4 text-gray-900 bg-white rounded-lg shadow-lg focus:outline-none focus:ring-2 focus:ring-primary-300 focus:border-transparent"})]})})}function w(){let[e,r]=(0,a.useState)(""),[t,i]=(0,a.useState)("Todos");return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"bg-gradient-to-r from-primary-600 to-primary-800 text-white",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"Servicios Ciudadanos"}),(0,s.jsx)("p",{className:"text-xl text-primary-100 mb-8 max-w-3xl mx-auto",children:"Accede a todos los servicios digitales del municipio de Ch\xeda de forma r\xe1pida, segura y desde cualquier lugar."}),(0,s.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,s.jsx)(j,{onSearch:e=>{r(e)},placeholder:"Buscar servicios municipales..."})})]})})}),(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,s.jsx)(v,{searchQuery:e,selectedCategory:t}),(0,s.jsxs)("div",{className:"mt-16 bg-white rounded-xl shadow-md p-8 text-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"\xbfNecesitas ayuda?"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6 max-w-2xl mx-auto",children:"Nuestro asistente de IA est\xe1 disponible 24/7 para ayudarte con cualquier consulta sobre nuestros servicios."}),(0,s.jsx)(o(),{href:"/chat",className:"inline-flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",children:"Hablar con el Asistente IA"})]})]})]})}},82578:e=>{e.exports="object"==typeof global&&global&&global.Object===Object&&global},86626:(e,r,t)=>{var s=t(14062),a=/^\s+/;e.exports=function(e){return e?e.slice(0,s(e)+1).replace(a,""):e}},89691:(e,r,t)=>{Promise.resolve().then(t.bind(t,52300)),Promise.resolve().then(t.bind(t,81068))},97372:(e,r,t)=>{var s=t(16646),a=t(49124);e.exports=function(e){return"symbol"==typeof e||a(e)&&"[object Symbol]"==s(e)}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[191,118,114,439],()=>t(44304));module.exports=s})();