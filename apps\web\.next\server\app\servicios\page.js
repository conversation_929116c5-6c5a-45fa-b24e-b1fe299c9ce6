(()=>{var e={};e.id=104,e.ids=[104],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6980:(e,r,t)=>{Promise.resolve().then(t.bind(t,59509)),Promise.resolve().then(t.t.bind(t,49989,23))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25023:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>v,metadata:()=>h});var s=t(38828),a=t(42671),i=t.n(a),o=t(25089),n=t(52146),l=t(98883),c=t(61365);let d=c.forwardRef(function({title:e,titleId:r,...t},s){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},t),e?c.createElement("title",{id:r},e):null,c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21"}))});var m=t(27998),u=t(35523);let p=c.forwardRef(function({title:e,titleId:r,...t},s){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},t),e?c.createElement("title",{id:r},e):null,c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"}))});var x=t(53558);let h={title:"Servicios Ciudadanos | Portal CHIA",description:"Accede a todos los servicios digitales del municipio de Ch\xeda. Certificados, pagos, licencias y m\xe1s.",keywords:"servicios municipales, certificados, pagos en l\xednea, licencias, Ch\xeda"},g=[{id:"certificados",name:"Certificados",description:"Certificados de residencia, nacimiento, defunci\xf3n y otros documentos oficiales",icon:n.A,href:"/servicios/certificados",popular:!0,estimatedTime:"2-5 d\xedas",category:"Documentos",features:["Descarga inmediata","Validaci\xf3n digital","Historial de solicitudes"]},{id:"pagos",name:"Pagos en L\xednea",description:"Pago de impuestos, multas, tasas municipales y otros tributos",icon:l.A,href:"/servicios/pagos",popular:!0,estimatedTime:"Inmediato",category:"Financiero",features:["M\xfaltiples m\xe9todos de pago","Recibo digital","Programar pagos"]},{id:"licencias",name:"Licencias",description:"Licencias de construcci\xf3n, funcionamiento, urbanismo y permisos especiales",icon:d,href:"/servicios/licencias",popular:!1,estimatedTime:"15-30 d\xedas",category:"Permisos",features:["Seguimiento en l\xednea","Documentos digitales","Notificaciones autom\xe1ticas"]},{id:"registro",name:"Registro Civil",description:"Documentos de identidad, registro de nacimientos, matrimonios y defunciones",icon:m.A,href:"/servicios/registro",popular:!0,estimatedTime:"1-3 d\xedas",category:"Documentos",features:["Citas en l\xednea","Pre-registro","Validaci\xf3n biom\xe9trica"]},{id:"consultas",name:"Consultas",description:"Informaci\xf3n catastral, municipal, normativa y consultas generales",icon:u.A,href:"/servicios/consultas",popular:!1,estimatedTime:"Inmediato",category:"Informaci\xf3n",features:["B\xfasqueda avanzada","Mapas interactivos","Exportar datos"]}],f=["Todos","Documentos","Financiero","Permisos","Informaci\xf3n"];function v(){return(0,s.jsxs)(o.default,{className:"bg-gray-50",children:[(0,s.jsx)("div",{className:"bg-gradient-to-r from-primary-600 to-primary-800 text-white",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"Servicios Ciudadanos"}),(0,s.jsx)("p",{className:"text-xl text-primary-100 mb-8 max-w-3xl mx-auto",children:"Accede a todos los servicios digitales del municipio de Ch\xeda de forma r\xe1pida, segura y desde cualquier lugar."}),(0,s.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(u.A,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,s.jsx)("input",{type:"text",placeholder:"Buscar servicios...",className:"w-full pl-12 pr-4 py-4 text-gray-900 bg-white rounded-lg shadow-lg focus:outline-none focus:ring-2 focus:ring-primary-300 focus:border-transparent"})]})})]})})}),(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsx)("div",{className:"flex flex-wrap gap-2 justify-center",children:f.map(e=>(0,s.jsx)("button",{className:"px-4 py-2 rounded-full text-sm font-medium bg-white border border-gray-300 text-gray-700 hover:bg-primary-50 hover:border-primary-300 hover:text-primary-700 transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",children:e},e))})}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:g.map(e=>(0,s.jsxs)(i(),{href:e.href,className:"group bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-200 hover:border-primary-300",children:[(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("div",{className:"flex items-start justify-between mb-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-3 bg-primary-100 rounded-lg group-hover:bg-primary-200 transition-colors",children:(0,s.jsx)(e.icon,{className:"h-6 w-6 text-primary-600"})}),e.popular&&(0,s.jsxs)("div",{className:"flex items-center gap-1 px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium",children:[(0,s.jsx)(p,{className:"h-3 w-3"}),"Popular"]})]})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors",children:e.name}),(0,s.jsx)("p",{className:"text-gray-600 mb-4 text-sm leading-relaxed",children:e.description}),(0,s.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-500 mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(x.A,{className:"h-4 w-4"}),e.estimatedTime]}),(0,s.jsx)("span",{className:"px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs",children:e.category})]}),(0,s.jsx)("div",{className:"space-y-1",children:e.features.map((e,r)=>(0,s.jsxs)("div",{className:"flex items-center gap-2 text-xs text-gray-500",children:[(0,s.jsx)("div",{className:"w-1 h-1 bg-primary-400 rounded-full"}),e]},r))})]}),(0,s.jsx)("div",{className:"px-6 py-4 bg-gray-50 group-hover:bg-primary-50 transition-colors",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700 group-hover:text-primary-700",children:"Acceder al servicio"}),(0,s.jsx)("svg",{className:"h-4 w-4 text-gray-400 group-hover:text-primary-600 group-hover:translate-x-1 transition-all",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})})]},e.id))}),(0,s.jsxs)("div",{className:"mt-16 bg-white rounded-xl shadow-md p-8 text-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"\xbfNecesitas ayuda?"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6 max-w-2xl mx-auto",children:"Nuestro asistente de IA est\xe1 disponible 24/7 para ayudarte con cualquier consulta sobre nuestros servicios."}),(0,s.jsx)(i(),{href:"/chat",className:"inline-flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",children:"Hablar con el Asistente IA"})]})]})]})}},25089:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(33952).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\PageLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\components\\layout\\PageLayout.tsx","default")},27998:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(61365);let a=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 9h3.75M15 12h3.75M15 15h3.75M4.5 19.5h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Zm6-10.125a1.875 1.875 0 1 1-3.75 0 1.875 1.875 0 0 1 3.75 0Zm1.294 6.336a6.721 6.721 0 0 1-3.17.789 6.721 6.721 0 0 1-3.168-.789 3.376 3.376 0 0 1 6.338 0Z"}))})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35523:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(61365);let a=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))})},42671:(e,r,t)=>{let{createProxy:s}=t(47927);e.exports=s("C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\node_modules\\next\\dist\\client\\app-dir\\link.js")},44304:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var s=t(24332),a=t(48819),i=t(67851),o=t.n(i),n=t(97540),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let c={children:["",{children:["servicios",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,25023)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\servicios\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,34356)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\servicios\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/servicios/page",pathname:"/servicios",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},52146:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(61365);let a=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))})},53558:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(61365);let a=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70180:(e,r,t)=>{Promise.resolve().then(t.bind(t,25089)),Promise.resolve().then(t.t.bind(t,42671,23))},98883:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(61365);let a=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"}))})}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[363,118,114,166],()=>t(44304));module.exports=s})();