import { Metadata } from 'next';
import Link from 'next/link';
import { 
  DocumentTextIcon, 
  CreditCardIcon, 
  BuildingOfficeIcon,
  IdentificationIcon,
  MagnifyingGlassIcon,
  ClockIcon,
  StarIcon
} from '@heroicons/react/24/outline';

export const metadata: Metadata = {
  title: 'Servicios Ciudadanos | Portal CHIA',
  description: 'Accede a todos los servicios digitales del municipio de Chía. Certificados, pagos, licencias y más.',
  keywords: 'servicios municipales, certificados, pagos en línea, licencias, Chía',
};

const services = [
  {
    id: 'certificados',
    name: 'Certificados',
    description: 'Certificados de residencia, nacimiento, defunción y otros documentos oficiales',
    icon: DocumentTextIcon,
    href: '/servicios/certificados',
    popular: true,
    estimatedTime: '2-5 días',
    category: 'Documentos',
    features: ['Descarga inmediata', 'Validación digital', 'Historial de solicitudes']
  },
  {
    id: 'pagos',
    name: 'Pagos en Línea',
    description: 'Pago de impuestos, multas, tasas municipales y otros tributos',
    icon: CreditCardIcon,
    href: '/servicios/pagos',
    popular: true,
    estimatedTime: 'Inmediato',
    category: 'Financiero',
    features: ['Múltiples métodos de pago', 'Recibo digital', 'Programar pagos']
  },
  {
    id: 'licencias',
    name: 'Licencias',
    description: 'Licencias de construcción, funcionamiento, urbanismo y permisos especiales',
    icon: BuildingOfficeIcon,
    href: '/servicios/licencias',
    popular: false,
    estimatedTime: '15-30 días',
    category: 'Permisos',
    features: ['Seguimiento en línea', 'Documentos digitales', 'Notificaciones automáticas']
  },
  {
    id: 'registro',
    name: 'Registro Civil',
    description: 'Documentos de identidad, registro de nacimientos, matrimonios y defunciones',
    icon: IdentificationIcon,
    href: '/servicios/registro',
    popular: true,
    estimatedTime: '1-3 días',
    category: 'Documentos',
    features: ['Citas en línea', 'Pre-registro', 'Validación biométrica']
  },
  {
    id: 'consultas',
    name: 'Consultas',
    description: 'Información catastral, municipal, normativa y consultas generales',
    icon: MagnifyingGlassIcon,
    href: '/servicios/consultas',
    popular: false,
    estimatedTime: 'Inmediato',
    category: 'Información',
    features: ['Búsqueda avanzada', 'Mapas interactivos', 'Exportar datos']
  }
];

const categories = ['Todos', 'Documentos', 'Financiero', 'Permisos', 'Información'];

export default function ServiciosPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">
              Servicios Ciudadanos
            </h1>
            <p className="text-xl text-primary-100 mb-8 max-w-3xl mx-auto">
              Accede a todos los servicios digitales del municipio de Chía de forma rápida, 
              segura y desde cualquier lugar.
            </p>
            
            {/* Search Bar */}
            <div className="max-w-2xl mx-auto">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Buscar servicios..."
                  className="w-full pl-12 pr-4 py-4 text-gray-900 bg-white rounded-lg shadow-lg focus:outline-none focus:ring-2 focus:ring-primary-300 focus:border-transparent"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Services Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Category Filter */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-2 justify-center">
            {categories.map((category) => (
              <button
                key={category}
                className="px-4 py-2 rounded-full text-sm font-medium bg-white border border-gray-300 text-gray-700 hover:bg-primary-50 hover:border-primary-300 hover:text-primary-700 transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* Services Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service) => (
            <Link
              key={service.id}
              href={service.href}
              className="group bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-200 hover:border-primary-300"
            >
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-3 bg-primary-100 rounded-lg group-hover:bg-primary-200 transition-colors">
                      <service.icon className="h-6 w-6 text-primary-600" />
                    </div>
                    {service.popular && (
                      <div className="flex items-center gap-1 px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">
                        <StarIcon className="h-3 w-3" />
                        Popular
                      </div>
                    )}
                  </div>
                </div>
                
                <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors">
                  {service.name}
                </h3>
                
                <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                  {service.description}
                </p>
                
                <div className="flex items-center gap-4 text-sm text-gray-500 mb-4">
                  <div className="flex items-center gap-1">
                    <ClockIcon className="h-4 w-4" />
                    {service.estimatedTime}
                  </div>
                  <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs">
                    {service.category}
                  </span>
                </div>
                
                <div className="space-y-1">
                  {service.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2 text-xs text-gray-500">
                      <div className="w-1 h-1 bg-primary-400 rounded-full"></div>
                      {feature}
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="px-6 py-4 bg-gray-50 group-hover:bg-primary-50 transition-colors">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700 group-hover:text-primary-700">
                    Acceder al servicio
                  </span>
                  <svg className="h-4 w-4 text-gray-400 group-hover:text-primary-600 group-hover:translate-x-1 transition-all" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Help Section */}
        <div className="mt-16 bg-white rounded-xl shadow-md p-8 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            ¿Necesitas ayuda?
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Nuestro asistente de IA está disponible 24/7 para ayudarte con cualquier consulta 
            sobre nuestros servicios.
          </p>
          <Link
            href="/chat"
            className="inline-flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
          >
            Hablar con el Asistente IA
          </Link>
        </div>
      </div>
    </div>
  );
}
