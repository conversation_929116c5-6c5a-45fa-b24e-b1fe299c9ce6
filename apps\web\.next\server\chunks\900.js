exports.id=900,exports.ids=[900],exports.modules={1241:()=>{},2421:(e,a,t)=>{"use strict";t.d(a,{TD:()=>o,TI:()=>m,Z2:()=>u,a8:()=>p,gG:()=>r,oH:()=>s});var r,o,s,i=t(67201);!function(e){e.CIUDADANO="ciudadano",e.ADMIN_MUNICIPAL="admin_municipal",e.ADMIN_SISTEMA="admin_sistema",e.OPERADOR="operador"}(r||(r={})),function(e){e.LOADING="loading",e.AUTHENTICATED="authenticated",e.UNAUTHENTICATED="unauthenticated",e.ERROR="error"}(o||(o={})),function(e){e.DISABLED="disabled",e.ENABLED="enabled",e.PENDING_SETUP="pending_setup"}(s||(s={}));let n=i.Yj().email("Email inv\xe1lido"),d=i.Yj().min(8,"La contrase\xf1a debe tener al menos 8 caracteres").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,"La contrase\xf1a debe contener al menos una may\xfascula, una min\xfascula, un n\xfamero y un car\xe1cter especial"),l=i.Yj().regex(/^\+57[0-9]{10}$/,"N\xfamero de tel\xe9fono colombiano inv\xe1lido (+57XXXXXXXXXX)"),c=i.Yj().min(6,"N\xfamero de documento debe tener al menos 6 d\xedgitos").max(12,"N\xfamero de documento no puede tener m\xe1s de 12 d\xedgitos").regex(/^\d+$/,"N\xfamero de documento debe contener solo n\xfameros"),u=i.Ik({email:n,password:i.Yj().min(1,"Contrase\xf1a requerida"),rememberMe:i.zM().optional(),twoFactorCode:i.Yj().optional()}),m=i.Ik({email:n,password:d,confirmPassword:i.Yj(),firstName:i.Yj().min(2,"Nombre debe tener al menos 2 caracteres"),lastName:i.Yj().min(2,"Apellido debe tener al menos 2 caracteres"),documentType:i.k5(["CC","CE","TI","PP"]),documentNumber:c,phone:l.optional(),city:i.Yj().min(2,"Ciudad requerida"),department:i.Yj().min(2,"Departamento requerido"),birthDate:i.Yj().optional(),acceptTerms:i.zM().refine(e=>!0===e,"Debe aceptar los t\xe9rminos y condiciones"),acceptPrivacyPolicy:i.zM().refine(e=>!0===e,"Debe aceptar la pol\xedtica de privacidad")}).refine(e=>e.password===e.confirmPassword,{message:"Las contrase\xf1as no coinciden",path:["confirmPassword"]}),p=i.Ik({email:n});i.Ik({newPassword:d,confirmPassword:i.Yj(),resetToken:i.Yj()}).refine(e=>e.newPassword===e.confirmPassword,{message:"Las contrase\xf1as no coinciden",path:["confirmPassword"]}),i.Ik({code:i.Yj().length(6,"C\xf3digo debe tener 6 d\xedgitos").regex(/^\d+$/,"C\xf3digo debe contener solo n\xfameros"),backupCode:i.Yj().optional()})},21971:()=>{},32002:(e,a,t)=>{Promise.resolve().then(t.t.bind(t,69355,23)),Promise.resolve().then(t.t.bind(t,54439,23)),Promise.resolve().then(t.t.bind(t,67851,23)),Promise.resolve().then(t.t.bind(t,94730,23)),Promise.resolve().then(t.t.bind(t,19774,23)),Promise.resolve().then(t.t.bind(t,53170,23)),Promise.resolve().then(t.t.bind(t,20968,23)),Promise.resolve().then(t.t.bind(t,78298,23))},33875:(e,a,t)=>{"use strict";t.r(a),t.d(a,{AuthContext:()=>o,AuthProvider:()=>s});var r=t(33952);let o=(0,r.registerClientReference)(function(){throw Error("Attempted to call AuthContext() from the server but AuthContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\packages\\auth\\dist\\context\\auth-context.js","AuthContext"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\packages\\auth\\dist\\context\\auth-context.js","AuthProvider")},34356:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>d,metadata:()=>n,viewport:()=>i});var r=t(38828),o=t(7666),s=t.n(o);t(21971);let i={width:"device-width",initialScale:1},n={metadataBase:new URL("https://portal.chia-cundinamarca.gov.co"),title:{default:"CHIA - Portal Ciudadano Digital",template:"%s | CHIA - Portal Ciudadano"},description:"Portal oficial de servicios ciudadanos para el municipio de Ch\xeda, Cundinamarca. Realiza tr\xe1mites en l\xednea, consulta informaci\xf3n municipal y accede a servicios digitales las 24 horas.",keywords:["Ch\xeda","Cundinamarca","servicios ciudadanos","gobierno digital","tr\xe1mites en l\xednea","certificados","impuestos","licencias","portal ciudadano","alcald\xeda","municipio"],authors:[{name:"Alcald\xeda Municipal de Ch\xeda"}],creator:"Alcald\xeda Municipal de Ch\xeda",publisher:"Alcald\xeda Municipal de Ch\xeda",robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},openGraph:{type:"website",locale:"es_CO",url:"https://portal.chia-cundinamarca.gov.co",siteName:"CHIA - Portal Ciudadano",title:"CHIA - Portal Ciudadano Digital",description:"Portal oficial de servicios ciudadanos para el municipio de Ch\xeda, Cundinamarca. Tr\xe1mites en l\xednea las 24 horas.",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"Portal Ciudadano de Ch\xeda"}]},twitter:{card:"summary_large_image",title:"CHIA - Portal Ciudadano Digital",description:"Portal oficial de servicios ciudadanos para el municipio de Ch\xeda, Cundinamarca.",images:["/og-image.jpg"],creator:"@AlcaldiaChia"},alternates:{canonical:"https://portal.chia-cundinamarca.gov.co"},category:"government"};function d({children:e}){return(0,r.jsx)("html",{lang:"es",className:"h-full",children:(0,r.jsx)("body",{className:`${s().className} h-full`,children:(0,r.jsx)("div",{id:"root",className:"min-h-full",children:e})})})}},39953:(e,a,t)=>{Promise.resolve().then(t.bind(t,98117))},40625:(e,a,t)=>{Promise.resolve().then(t.bind(t,33875))},50154:(e,a,t)=>{Promise.resolve().then(t.t.bind(t,30385,23)),Promise.resolve().then(t.t.bind(t,33737,23)),Promise.resolve().then(t.t.bind(t,86081,23)),Promise.resolve().then(t.t.bind(t,1904,23)),Promise.resolve().then(t.t.bind(t,35856,23)),Promise.resolve().then(t.t.bind(t,55492,23)),Promise.resolve().then(t.t.bind(t,89082,23)),Promise.resolve().then(t.t.bind(t,45812,23))},55362:()=>{},68082:(e,a,t)=>{"use strict";t.d(a,{u:()=>d});var r=t(78126);function o(){let e=process.env.NEXT_PUBLIC_SUPABASE_URL,a=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,t=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!e)throw Error("NEXT_PUBLIC_SUPABASE_URL environment variable is required");if(!a)throw Error("NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable is required");return{url:e,anonKey:a,serviceRoleKey:t}}let s=null,i=null;var n=t(2421);class d{constructor(){this.supabase=(s||(s=function(){let e=o();return(0,r.UU)(e.url,e.anonKey,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"pkce"},global:{headers:{"X-Client-Info":"chia-portal@1.0.0"}}})}()),s),this.adminSupabase=(i||(i=function(){let e=o();if(!e.serviceRoleKey)throw Error("SUPABASE_SERVICE_ROLE_KEY environment variable is required for admin operations");return(0,r.UU)(e.url,e.serviceRoleKey,{auth:{autoRefreshToken:!1,persistSession:!1},global:{headers:{"X-Client-Info":"chia-portal-admin@1.0.0"}}})}()),i)}async register(e){try{let{data:a,error:t}=await this.supabase.auth.signUp({email:e.email,password:e.password,options:{data:{first_name:e.firstName,last_name:e.lastName,document_type:e.documentType,document_number:e.documentNumber,phone:e.phone,city:e.city,department:e.department,birth_date:e.birthDate,role:n.gG.CIUDADANO}}});if(t)throw this.mapSupabaseError(t);if(!a.user)throw Error("Failed to create user account");let{data:r,error:o}=await this.supabase.from("ciudadano").insert({id:a.user.id,email:e.email,first_name:e.firstName,last_name:e.lastName,document_type:e.documentType,document_number:e.documentNumber,phone:e.phone,city:e.city,department:e.department,birth_date:e.birthDate,is_email_verified:!1,is_phone_verified:!1,two_factor_enabled:!1}).select().single();if(o)throw await this.adminSupabase.auth.admin.deleteUser(a.user.id),this.mapDatabaseError(o);return await this.logAuditEvent(a.user.id,"user_registered","user",a.user.id),this.mapDatabaseUserToProfile(r)}catch(e){throw this.handleError(e,"Registration failed")}}async login(e){try{let{data:a,error:t}=await this.supabase.auth.signInWithPassword({email:e.email,password:e.password});if(t)throw this.mapSupabaseError(t);if(!a.user||!a.session)throw Error("Authentication failed");let r=await this.getUserProfile(a.user.id);if(r.twoFactorEnabled&&!e.twoFactorCode)throw Error("Two-factor authentication code required");r.twoFactorEnabled&&e.twoFactorCode&&await this.verifyTwoFactorCode(a.user.id,e.twoFactorCode),await this.supabase.from("ciudadano").update({last_login_at:new Date().toISOString()}).eq("id",a.user.id);let o=await this.createSessionRecord(a.user.id,a.session);return await this.logAuditEvent(a.user.id,"user_login","user",a.user.id),{user:r,accessToken:a.session.access_token,refreshToken:a.session.refresh_token,expiresAt:a.session.expires_at||0,sessionId:o.id}}catch(e){throw this.handleError(e,"Login failed")}}async logout(){try{let{data:{user:e}}=await this.supabase.auth.getUser();e&&(await this.supabase.from("user_sessions").update({is_active:!1}).eq("user_id",e.id),await this.logAuditEvent(e.id,"user_logout","user",e.id));let{error:a}=await this.supabase.auth.signOut();if(a)throw this.mapSupabaseError(a)}catch(e){throw this.handleError(e,"Logout failed")}}async getCurrentUser(){try{let{data:{user:e},error:a}=await this.supabase.auth.getUser();if(a)throw this.mapSupabaseError(a);if(!e)return null;return await this.getUserProfile(e.id)}catch(e){return console.error("Failed to get current user:",e),null}}async refreshSession(){try{let{data:e,error:a}=await this.supabase.auth.refreshSession();if(a)throw this.mapSupabaseError(a);if(!e.session||!e.user)return null;return{user:await this.getUserProfile(e.user.id),accessToken:e.session.access_token,refreshToken:e.session.refresh_token,expiresAt:e.session.expires_at||0,sessionId:""}}catch(e){return console.error("Failed to refresh session:",e),null}}async requestPasswordReset(e){try{let{error:a}=await this.supabase.auth.resetPasswordForEmail(e,{redirectTo:`${window.location.origin}/auth/reset-password`});if(a)throw this.mapSupabaseError(a);let{data:t}=await this.supabase.from("ciudadano").select("id").eq("email",e).single();t&&await this.logAuditEvent(t.id,"password_reset_requested","user",t.id)}catch(e){throw this.handleError(e,"Password reset request failed")}}async updatePassword(e){try{let{error:a}=await this.supabase.auth.updateUser({password:e.newPassword});if(a)throw this.mapSupabaseError(a);let{data:{user:t}}=await this.supabase.auth.getUser();t&&await this.logAuditEvent(t.id,"password_updated","user",t.id)}catch(e){throw this.handleError(e,"Password update failed")}}async getUserProfile(e){let{data:a,error:t}=await this.supabase.from("ciudadano").select("*").eq("id",e).single();if(t)throw this.mapDatabaseError(t);return this.mapDatabaseUserToProfile(a)}async createSessionRecord(e,a){let{data:t,error:r}=await this.supabase.from("user_sessions").insert({user_id:e,session_token:a.access_token,refresh_token:a.refresh_token,expires_at:new Date(1e3*a.expires_at).toISOString(),is_active:!0}).select().single();if(r)throw this.mapDatabaseError(r);return t}async verifyTwoFactorCode(e,a){throw Error("Two-factor authentication not yet implemented")}async logAuditEvent(e,a,t,r,o){try{await this.supabase.from("audit_log").insert({user_id:e,action:a,resource_type:t,resource_id:r,details:o,created_at:new Date().toISOString()})}catch(e){console.error("Failed to log audit event:",e)}}mapDatabaseUserToProfile(e){return{id:e.id,email:e.email,role:n.gG.CIUDADANO,firstName:e.first_name,lastName:e.last_name,documentType:e.document_type,documentNumber:e.document_number,phone:e.phone,address:e.address,city:e.city,department:e.department,birthDate:e.birth_date,isEmailVerified:e.is_email_verified,isPhoneVerified:e.is_phone_verified,twoFactorEnabled:e.two_factor_enabled,twoFactorStatus:e.two_factor_enabled?n.oH.ENABLED:n.oH.DISABLED,lastLoginAt:e.last_login_at,createdAt:e.created_at,updatedAt:e.updated_at,metadata:e.metadata}}mapSupabaseError(e){return{code:e.name||"AUTH_ERROR",message:e.message,details:{originalError:e}}}mapDatabaseError(e){return{code:e.code||"DATABASE_ERROR",message:e.message||"Database operation failed",details:{originalError:e}}}handleError(e,a){return e.code&&e.message?e:{code:"UNKNOWN_ERROR",message:`${a}: ${e.message||"Unknown error occurred"}`,details:{originalError:e}}}}},72088:()=>{},73514:()=>{},86580:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>h,metadata:()=>p});var r,o,s,i=t(38828),n=t(83098);!function(e){e.CIUDADANO="ciudadano",e.ADMIN_MUNICIPAL="admin_municipal",e.ADMIN_SISTEMA="admin_sistema",e.OPERADOR="operador"}(r||(r={})),function(e){e.LOADING="loading",e.AUTHENTICATED="authenticated",e.UNAUTHENTICATED="unauthenticated",e.ERROR="error"}(o||(o={})),function(e){e.DISABLED="disabled",e.ENABLED="enabled",e.PENDING_SETUP="pending_setup"}(s||(s={}));let d=n.Yj().email("Email inv\xe1lido"),l=n.Yj().min(8,"La contrase\xf1a debe tener al menos 8 caracteres").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,"La contrase\xf1a debe contener al menos una may\xfascula, una min\xfascula, un n\xfamero y un car\xe1cter especial"),c=n.Yj().regex(/^\+57[0-9]{10}$/,"N\xfamero de tel\xe9fono colombiano inv\xe1lido (+57XXXXXXXXXX)"),u=n.Yj().min(6,"N\xfamero de documento debe tener al menos 6 d\xedgitos").max(12,"N\xfamero de documento no puede tener m\xe1s de 12 d\xedgitos").regex(/^\d+$/,"N\xfamero de documento debe contener solo n\xfameros");n.Ik({email:d,password:n.Yj().min(1,"Contrase\xf1a requerida"),rememberMe:n.zM().optional(),twoFactorCode:n.Yj().optional()}),n.Ik({email:d,password:l,confirmPassword:n.Yj(),firstName:n.Yj().min(2,"Nombre debe tener al menos 2 caracteres"),lastName:n.Yj().min(2,"Apellido debe tener al menos 2 caracteres"),documentType:n.k5(["CC","CE","TI","PP"]),documentNumber:u,phone:c.optional(),city:n.Yj().min(2,"Ciudad requerida"),department:n.Yj().min(2,"Departamento requerido"),birthDate:n.Yj().optional(),acceptTerms:n.zM().refine(e=>!0===e,"Debe aceptar los t\xe9rminos y condiciones"),acceptPrivacyPolicy:n.zM().refine(e=>!0===e,"Debe aceptar la pol\xedtica de privacidad")}).refine(e=>e.password===e.confirmPassword,{message:"Las contrase\xf1as no coinciden",path:["confirmPassword"]}),n.Ik({email:d}),n.Ik({newPassword:l,confirmPassword:n.Yj(),resetToken:n.Yj()}).refine(e=>e.newPassword===e.confirmPassword,{message:"Las contrase\xf1as no coinciden",path:["confirmPassword"]}),n.Ik({code:n.Yj().length(6,"C\xf3digo debe tener 6 d\xedgitos").regex(/^\d+$/,"C\xf3digo debe contener solo n\xfameros"),backupCode:n.Yj().optional()});var m=t(33875);t(61365);let p={robots:{index:!1,follow:!1}};function h({children:e}){return(0,i.jsx)(m.AuthProvider,{children:e})}},98117:(e,a,t)=>{"use strict";t.r(a),t.d(a,{AuthContext:()=>l,AuthProvider:()=>c});var r=t(13486),o=t(60159),s=t(68082),i=t(2421);let n={status:i.TD.LOADING,session:null,user:null,error:null};function d(e,a){switch(a.type){case"SET_LOADING":return{...e,status:i.TD.LOADING,error:null};case"SET_AUTHENTICATED":return{...e,status:i.TD.AUTHENTICATED,session:a.payload.session,user:a.payload.user,error:null};case"SET_UNAUTHENTICATED":return{...e,status:i.TD.UNAUTHENTICATED,session:null,user:null,error:null};case"SET_ERROR":return{...e,status:i.TD.ERROR,error:a.payload};case"CLEAR_ERROR":return{...e,error:null};case"UPDATE_USER":return{...e,user:a.payload};default:return e}}let l=(0,o.createContext)(null);function c({children:e}){let[a,t]=(0,o.useReducer)(d,n),i=new s.u;(0,o.useCallback)(async()=>{try{t({type:"SET_LOADING"});let e=await i.getCurrentUser();if(e){let a=await i.refreshSession();a?t({type:"SET_AUTHENTICATED",payload:{session:a,user:e}}):t({type:"SET_UNAUTHENTICATED"})}else t({type:"SET_UNAUTHENTICATED"})}catch(e){console.error("Auth initialization failed:",e),t({type:"SET_UNAUTHENTICATED"})}},[]);let c=(0,o.useCallback)(async e=>{try{t({type:"SET_LOADING"}),t({type:"CLEAR_ERROR"});let a=await i.login(e);t({type:"SET_AUTHENTICATED",payload:{session:a,user:a.user}})}catch(e){throw t({type:"SET_ERROR",payload:e}),e}},[]),u=(0,o.useCallback)(async e=>{try{t({type:"SET_LOADING"}),t({type:"CLEAR_ERROR"}),await i.register(e),t({type:"SET_UNAUTHENTICATED"})}catch(e){throw t({type:"SET_ERROR",payload:e}),e}},[]),m=(0,o.useCallback)(async()=>{try{await i.logout(),t({type:"SET_UNAUTHENTICATED"})}catch(e){console.error("Logout failed:",e),t({type:"SET_UNAUTHENTICATED"})}},[]),p=(0,o.useCallback)(async e=>{try{t({type:"CLEAR_ERROR"}),await i.requestPasswordReset(e)}catch(e){throw t({type:"SET_ERROR",payload:e}),e}},[]),h=(0,o.useCallback)(async e=>{try{t({type:"CLEAR_ERROR"}),await i.updatePassword(e)}catch(e){throw t({type:"SET_ERROR",payload:e}),e}},[]),E=(0,o.useCallback)(async e=>{try{throw t({type:"CLEAR_ERROR"}),Error("Profile update not yet implemented")}catch(e){throw t({type:"SET_ERROR",payload:e}),e}},[]),w=(0,o.useCallback)(async()=>{try{let e=await i.refreshSession();e?t({type:"SET_AUTHENTICATED",payload:{session:e,user:e.user}}):t({type:"SET_UNAUTHENTICATED"})}catch(e){console.error("Session refresh failed:",e),t({type:"SET_UNAUTHENTICATED"})}},[]),f=(0,o.useCallback)(async()=>{try{throw t({type:"CLEAR_ERROR"}),Error("Two-factor authentication setup not yet implemented")}catch(e){throw t({type:"SET_ERROR",payload:e}),e}},[]),A=(0,o.useCallback)(async e=>{try{throw t({type:"CLEAR_ERROR"}),Error("Two-factor authentication verification not yet implemented")}catch(e){throw t({type:"SET_ERROR",payload:e}),e}},[]),_=(0,o.useCallback)(async e=>{try{throw t({type:"CLEAR_ERROR"}),Error("Two-factor authentication disable not yet implemented")}catch(e){throw t({type:"SET_ERROR",payload:e}),e}},[]),b={status:a.status,session:a.session,user:a.user,error:a.error,login:c,register:u,logout:m,resetPassword:p,updatePassword:h,updateProfile:E,refreshSession:w,setupTwoFactor:f,verifyTwoFactor:A,disableTwoFactor:_};return(0,r.jsx)(l.Provider,{value:b,children:e})}}};