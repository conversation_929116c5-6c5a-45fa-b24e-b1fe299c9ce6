'use client';

import { useState } from 'react';
import MainNavigation from '@/components/layout/MainNavigation';
import HeroSection from '@/components/landing/HeroSection';
import FeaturedServices from '@/components/landing/FeaturedServices';
import ContactInfo from '@/components/landing/ContactInfo';
import ChatWidget from '@/components/landing/ChatWidget';

export default function HomePage() {
  const [isChatOpen, setIsChatOpen] = useState(false);

  const handleSearchClick = () => {
    // In a real implementation, this would open a search modal or navigate to search page
    console.log('Search clicked');
  };

  const handleChatClick = () => {
    setIsChatOpen(true);
  };

  const handleServiceSelect = (serviceId: string) => {
    // In a real implementation, this would navigate to the specific service
    console.log('Service selected:', serviceId);
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <MainNavigation
        onSearchClick={handleSearchClick}
        onChatClick={handleChatClick}
      />

      {/* Main Content */}
      <main>
        {/* Hero Section */}
        <HeroSection
          onSearchClick={handleSearchClick}
          onChatClick={handleChatClick}
        />

        {/* Featured Services */}
        <FeaturedServices onServiceSelect={handleServiceSelect} />

        {/* Contact Information */}
        <ContactInfo />
      </main>

      {/* Chat Widget */}
      <ChatWidget
        isOpen={isChatOpen}
        onToggle={() => setIsChatOpen(!isChatOpen)}
      />
    </div>
  );
}
